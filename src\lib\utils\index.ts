import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatPrice(price: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
  }).format(price)
}

export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(date))
}

export function formatDateTime(date: string | Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(new Date(date))
}

export function generateSlug(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w ]+/g, '')
    .replace(/ +/g, '-')
    .trim()
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength).trim() + '...'
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export function isValidPhone(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]+$/
  return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10
}

export function getInitials(name: string): string {
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

export function getStatusColor(status: string): string {
  const statusColors = {
    pending: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    in_progress: 'text-blue-600 bg-blue-50 border-blue-200',
    accepted: 'text-green-600 bg-green-50 border-green-200',
    rejected: 'text-red-600 bg-red-50 border-red-200',
    completed: 'text-purple-600 bg-purple-50 border-purple-200',
  }
  return statusColors[status as keyof typeof statusColors] || 'text-gray-600 bg-gray-50 border-gray-200'
}

export function calculatePagination(
  total: number,
  page: number,
  limit: number
) {
  const pages = Math.ceil(total / limit)
  const hasNext = page < pages
  const hasPrevious = page > 1
  
  return {
    total,
    page,
    limit,
    pages,
    hasNext,
    hasPrevious,
    offset: (page - 1) * limit,
  }
}

export function getImageUrl(path: string | null | undefined): string {
  if (!path) return '/images/placeholder-product.jpg'
  if (path.startsWith('http')) return path
  return `/images/${path}`
}

export function validateProductData(data: any): string[] {
  const errors: string[] = []
  
  if (!data.name?.trim()) errors.push('Product name is required')
  if (!data.brand?.trim()) errors.push('Brand is required')
  if (!data.model?.trim()) errors.push('Model name is required')
  if (!data.category_id) errors.push('Category is required')
  if (!data.price || data.price <= 0) errors.push('Valid price is required')
  if (!data.stock_quantity || data.stock_quantity < 0) errors.push('Valid stock quantity is required')
  
  return errors
}

export function getProductImageAlt(product: { name: string; brand: string; model_name: string }): string {
  return `${product.brand} ${product.model_name} - ${product.name}`
}
