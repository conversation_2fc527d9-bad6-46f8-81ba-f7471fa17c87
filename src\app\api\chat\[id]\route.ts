import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { getSupabaseServerClient } from '@/lib/supabase/server';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// GET - Fetch chat messages for an inquiry
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const userSupabase = await getSupabaseServerClient();

    // Get authenticated user
    const { data: { user }, error: authError } = await userSupabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    const { id: inquiryId } = await params;

    // Get the 'since' parameter to fetch only new messages
    const url = new URL(request.url);
    const sinceTimestamp = url.searchParams.get('since');

    // Verify user owns this inquiry
    const { data: inquiry, error: inquiryError } = await supabase
      .from('inquiries')
      .select('id')
      .eq('id', inquiryId)
      .eq('user_id', user.id)
      .single();

    if (inquiryError || !inquiry) {
      return NextResponse.json({ error: 'Inquiry not found or access denied' }, { status: 404 });
    }

    // Build the query for chat messages
    let query = supabase
      .from('chat_messages')
      .select('*')
      .eq('inquiry_id', inquiryId);

    // If 'since' parameter is provided, only fetch messages after that timestamp
    if (sinceTimestamp) {
      query = query.gt('created_at', sinceTimestamp);
    }

    // Fetch chat messages
    const { data: messages, error } = await query.order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching chat messages:', error);
      return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 });
    }

    return NextResponse.json({ messages: messages || [] });
  } catch (error) {
    console.error('Error in chat GET API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST - Send a new chat message
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: inquiryId } = await params;
    const body = await request.json();
    const { message_type, content, is_admin } = body;

    let senderId: string;
    let senderType: 'user' | 'admin';
    let user = null;

    // Create supabase client for database operations
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    if (is_admin) {
      // Admin sending message
      senderType = 'admin';
      senderId = 'admin-user'; // Use a default admin ID for now
    } else {
      // User sending message
      const userSupabase = await getSupabaseServerClient();
      const { data: { user: authUser }, error: authError } = await userSupabase.auth.getUser();

      if (authError || !authUser) {
        return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
      }

      user = authUser;
      senderId = user.id;
      senderType = 'user';
    }

    // Validate input
    if (!message_type || !content?.trim()) {
      return NextResponse.json({ error: 'Message type and content are required' }, { status: 400 });
    }

    if (!['text', 'image', 'link'].includes(message_type)) {
      return NextResponse.json({ error: 'Invalid message type' }, { status: 400 });
    }

    // For users, verify they own this inquiry
    // For admins, allow access to any inquiry
    if (!is_admin) {
      const { data: inquiry, error: inquiryError } = await supabase
        .from('inquiries')
        .select('id')
        .eq('id', inquiryId)
        .eq('user_id', user!.id)
        .single();

      if (inquiryError || !inquiry) {
        return NextResponse.json({ error: 'Inquiry not found or access denied' }, { status: 404 });
      }
    } else {
      // For admin, just verify the inquiry exists
      const { data: inquiry, error: inquiryError } = await supabase
        .from('inquiries')
        .select('id')
        .eq('id', inquiryId)
        .single();

      if (inquiryError || !inquiry) {
        return NextResponse.json({ error: 'Inquiry not found' }, { status: 404 });
      }
    }

    // Create new message
    const { data: message, error } = await supabase
      .from('chat_messages')
      .insert({
        inquiry_id: inquiryId,
        sender_type: senderType,
        sender_id: senderId,
        message_type,
        message: content.trim(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating chat message:', error);
      return NextResponse.json({ error: 'Failed to send message' }, { status: 500 });
    }

    return NextResponse.json({
      message,
      success: true
    });
  } catch (error) {
    console.error('Error in chat POST API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
