import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    const supabase = createSupabaseAdminClient();

    // Get carousel settings
    const { data: settings, error } = await supabase
      .from('admin_settings')
      .select('*')
      .in('setting_key', ['carousel_delay', 'carousel_banner_image']);

    if (error) {
      console.error('Error fetching carousel settings:', error);
      return NextResponse.json({ error: 'Failed to fetch settings' }, { status: 500 });
    }

    // Parse settings
    const delaySettings = settings?.find(s => s.setting_key === 'carousel_delay');
    const bannerSettings = settings?.find(s => s.setting_key === 'carousel_banner_image');

    const delay = delaySettings?.setting_value ? parseInt(delaySettings.setting_value) : 5000;
    const enabled = delaySettings?.enabled ?? true;
    const banner_image = bannerSettings?.setting_value || '';

    return NextResponse.json({
      delay,
      enabled,
      banner_image
    });
  } catch (error) {
    console.error('Error in carousel settings GET:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = createSupabaseAdminClient();
    const { delay, enabled, banner_image } = await request.json();

    // Validate delay (between 1-30 seconds)
    if (delay < 1000 || delay > 30000) {
      return NextResponse.json({
        error: 'Delay must be between 1 and 30 seconds'
      }, { status: 400 });
    }

    // Prepare upsert operations
    const upsertOperations = [
      {
        setting_key: 'carousel_delay',
        setting_value: delay.toString(),
        enabled: enabled ?? true,
        updated_at: new Date().toISOString()
      }
    ];

    // Add banner image setting if provided
    if (banner_image !== undefined) {
      upsertOperations.push({
        setting_key: 'carousel_banner_image',
        setting_value: banner_image || '',
        enabled: true,
        updated_at: new Date().toISOString()
      });
    }

    // Upsert all settings
    const { error } = await supabase
      .from('admin_settings')
      .upsert(upsertOperations, {
        onConflict: 'setting_key'
      });

    if (error) {
      console.error('Error updating carousel settings:', error);
      return NextResponse.json({ error: 'Failed to update settings' }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      delay,
      enabled,
      banner_image
    });
  } catch (error) {
    console.error('Error in carousel settings PUT:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
