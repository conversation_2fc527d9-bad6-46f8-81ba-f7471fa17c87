import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseAdminClient();
    const searchParams = request.nextUrl.searchParams;
    
    // Parse query parameters
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const brand = searchParams.get('brand');
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const condition = searchParams.get('condition');
    const stockStatus = searchParams.get('stockStatus');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const sortByParam = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Map frontend sort options to database columns
    let sortBy: string;
    let actualSortOrder: string;

    switch (sortByParam) {
      case 'newest':
        sortBy = 'created_at';
        actualSortOrder = 'desc';
        break;
      case 'oldest':
        sortBy = 'created_at';
        actualSortOrder = 'asc';
        break;
      case 'price_asc':
        sortBy = 'price';
        actualSortOrder = 'asc';
        break;
      case 'price_desc':
        sortBy = 'price';
        actualSortOrder = 'desc';
        break;
      case 'name':
        sortBy = 'name';
        actualSortOrder = 'asc';
        break;
      case 'name_desc':
        sortBy = 'name';
        actualSortOrder = 'desc';
        break;
      case 'brand_asc':
        sortBy = 'brands.name';
        actualSortOrder = 'asc';
        break;
      case 'brand_desc':
        sortBy = 'brands.name';
        actualSortOrder = 'desc';
        break;
      case 'stock_high':
        sortBy = 'stock_quantity';
        actualSortOrder = 'desc';
        break;
      case 'stock_low':
        sortBy = 'stock_quantity';
        actualSortOrder = 'asc';
        break;
      case 'condition_best':
        sortBy = 'condition_id';
        actualSortOrder = 'asc';
        break;
      case 'condition_worst':
        sortBy = 'condition_id';
        actualSortOrder = 'desc';
        break;
      default:
        sortBy = sortByParam;
        actualSortOrder = sortOrder;
        break;
    }
    const includePrivate = searchParams.get('includePrivate') === 'true';

    // Build query with category and brand relationships
    let query = supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          slug
        ),
        brands (
          id,
          name,
          slug
        )
      `)
      .in('status', includePrivate ? ['active', 'private'] : ['active']); // Show active products, and private if authorized

    // Apply filters
    if (category) {
      // First get the category ID from the slug
      const { data: categoryData } = await supabase
        .from('categories')
        .select('id')
        .eq('slug', category)
        .single();

      if (categoryData) {
        query = query.eq('category_id', categoryData.id);
      }
    }

    if (search) {
      // Escape the search term to prevent SQL injection
      const escapedSearch = search.replace(/[%_]/g, '\\$&');
      query = query.or(`name.ilike.%${escapedSearch}%,brands.name.ilike.%${escapedSearch}%`);
    }

    if (brand) {
      query = query.eq('brands.name', brand);
    }

    if (minPrice) {
      query = query.gte('price', parseFloat(minPrice));
    }

    if (maxPrice) {
      query = query.lte('price', parseFloat(maxPrice));
    }

    if (condition) {
      // Get condition ID from slug
      const { data: conditionData } = await supabase
        .from('conditions')
        .select('id')
        .eq('slug', condition)
        .single();

      if (conditionData) {
        query = query.eq('condition_id', conditionData.id);
      }
    }

    // Note: Stock status filtering is handled on the frontend for now
    // since it requires complex logic with low_stock_threshold

    // Apply sorting with stable secondary sort
    query = query.order(sortBy, { ascending: actualSortOrder === 'asc' });

    // Add secondary sort by ID to ensure stable pagination
    if (sortBy !== 'id') {
      query = query.order('id', { ascending: true });
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data: products, error } = await query;

    if (error) {
      console.error('Error fetching products:', error);
      return NextResponse.json(
        { error: 'Failed to fetch products' },
        { status: 500 }
      );
    }



    // Get total count for pagination
    let countQuery = supabase
      .from('products')
      .select(`
        *,
        brands (
          id,
          name,
          slug
        )
      `, { count: 'exact', head: true })
      .in('status', includePrivate ? ['active', 'private'] : ['active']);

    // Apply same filters for count
    if (category) {
      const { data: categoryData } = await supabase
        .from('categories')
        .select('id')
        .eq('slug', category)
        .single();

      if (categoryData) {
        countQuery = countQuery.eq('category_id', categoryData.id);
      }
    }

    if (search) {
      // Escape the search term to prevent SQL injection
      const escapedSearch = search.replace(/[%_]/g, '\\$&');
      countQuery = countQuery.or(`name.ilike.%${escapedSearch}%,brands.name.ilike.%${escapedSearch}%`);
    }

    if (brand) {
      countQuery = countQuery.eq('brands.name', brand);
    }

    if (minPrice) {
      countQuery = countQuery.gte('price', parseFloat(minPrice));
    }

    if (maxPrice) {
      countQuery = countQuery.lte('price', parseFloat(maxPrice));
    }

    if (condition) {
      const { data: conditionData } = await supabase
        .from('conditions')
        .select('id')
        .eq('slug', condition)
        .single();

      if (conditionData) {
        countQuery = countQuery.eq('condition_id', conditionData.id);
      }
    }

    const { count: totalCount } = await countQuery;

    // Get count of active products only (for infinite scroll logic)
    const { count: activeCount } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active');

    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit),
        activeCount: activeCount || 0
      }
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
