import { Metadata } from 'next';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

export const metadata: Metadata = {
  title: 'Become a Dealer - Tisha International',
  description: 'Join the Tisha International dealer network and grow your business with premium refurbished technology. Competitive pricing, support, and training included.',
};

export default function BecomeDealerPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--background-primary)] via-[var(--background-secondary)] to-[var(--background-card)]">
      {/* Hero Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Become a <span className="bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light bg-clip-text text-transparent">
              Dealer
            </span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-8">
            Join our growing network of successful dealers and expand your business with premium refurbished technology solutions.
          </p>
          <Badge variant="gradient" className="text-lg px-6 py-2">
            Now Accepting Applications
          </Badge>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">Why Partner with Tisha International?</h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card className="p-6">
              <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">💰</span>
              </div>
              <h3 className="text-xl font-semibold text-white text-center mb-3">Competitive Margins</h3>
              <p className="text-gray-300 text-center">
                Enjoy attractive profit margins with our competitive dealer pricing structure and volume discounts.
              </p>
            </Card>

            <Card className="p-6">
              <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">🏆</span>
              </div>
              <h3 className="text-xl font-semibold text-white text-center mb-3">Premium Quality</h3>
              <p className="text-gray-300 text-center">
                All products undergo our rigorous 47-point quality assurance process for guaranteed reliability.
              </p>
            </Card>

            <Card className="p-6">
              <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">🎯</span>
              </div>
              <h3 className="text-xl font-semibold text-white text-center mb-3">Marketing Support</h3>
              <p className="text-gray-300 text-center">
                Comprehensive marketing materials, training, and promotional support to help you succeed.
              </p>
            </Card>

            <Card className="p-6">
              <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">🔧</span>
              </div>
              <h3 className="text-xl font-semibold text-white text-center mb-3">Technical Support</h3>
              <p className="text-gray-300 text-center">
                Dedicated technical support team to assist you and your customers with any technical issues.
              </p>
            </Card>

            <Card className="p-6">
              <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">📦</span>
              </div>
              <h3 className="text-xl font-semibold text-white text-center mb-3">Flexible Inventory</h3>
              <p className="text-gray-300 text-center">
                No minimum order requirements for established dealers and flexible inventory management.
              </p>
            </Card>

            <Card className="p-6">
              <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center mx-auto mb-4">
                <span className="text-white text-2xl font-bold">🌟</span>
              </div>
              <h3 className="text-xl font-semibold text-white text-center mb-3">Exclusive Territory</h3>
              <p className="text-gray-300 text-center">
                Protected territory rights for qualified dealers to ensure market exclusivity in your area.
              </p>
            </Card>
          </div>
        </div>
      </section>

      {/* Requirements Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12">
            <Card className="p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Dealer Requirements</h2>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-gray-300">Established business with valid business license</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-gray-300">Minimum 2 years experience in technology retail</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-gray-300">Physical retail location or showroom</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-gray-300">Commitment to customer service excellence</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-gray-300">Financial capability for initial inventory investment</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-gray-300">Dedicated staff for sales and technical support</span>
                </div>
              </div>
            </Card>

            <Card className="p-8">
              <h2 className="text-2xl font-bold text-white mb-6">What We Provide</h2>
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-gray-300">Comprehensive dealer training program</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-gray-300">Marketing materials and promotional support</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-gray-300">Dedicated dealer portal and ordering system</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-gray-300">Ongoing technical and sales support</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-gray-300">Product catalogs and specification sheets</span>
                </div>
                <div className="flex items-center gap-3">
                  <div className="w-6 h-6 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-white text-xs">✓</span>
                  </div>
                  <span className="text-gray-300">Warranty support and return policies</span>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Application Form Section */}
      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <Card className="p-8">
            <h2 className="text-3xl font-bold text-white text-center mb-8">Dealer Application Form</h2>
            <form className="space-y-6">
              {/* Business Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-white border-b border-gray-600 pb-2">Business Information</h3>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="businessName" className="block text-sm font-medium text-gray-300 mb-2">
                      Business Name *
                    </label>
                    <Input
                      id="businessName"
                      name="businessName"
                      type="text"
                      required
                      placeholder="Enter your business name"
                    />
                  </div>
                  <div>
                    <label htmlFor="businessType" className="block text-sm font-medium text-gray-300 mb-2">
                      Business Type *
                    </label>
                    <select
                      id="businessType"
                      name="businessType"
                      required
                      className="w-full px-4 py-3 bg-background-card border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gradient-to-r focus:ring-primary-gradient-dark focus:border-transparent"
                    >
                      <option value="">Select business type</option>
                      <option value="retailer">Computer Retailer</option>
                      <option value="distributor">Technology Distributor</option>
                      <option value="reseller">System Integrator/Reseller</option>
                      <option value="repair">Repair Service Center</option>
                      <option value="other">Other</option>
                    </select>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="yearsInBusiness" className="block text-sm font-medium text-gray-300 mb-2">
                      Years in Business *
                    </label>
                    <select
                      id="yearsInBusiness"
                      name="yearsInBusiness"
                      required
                      className="w-full px-4 py-3 bg-background-card border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gradient-to-r focus:ring-primary-gradient-dark focus:border-transparent"
                    >
                      <option value="">Select years</option>
                      <option value="1-2">1-2 years</option>
                      <option value="3-5">3-5 years</option>
                      <option value="6-10">6-10 years</option>
                      <option value="10+">10+ years</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="businessLicense" className="block text-sm font-medium text-gray-300 mb-2">
                      Business License Number *
                    </label>
                    <Input
                      id="businessLicense"
                      name="businessLicense"
                      type="text"
                      required
                      placeholder="Enter license number"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="businessAddress" className="block text-sm font-medium text-gray-300 mb-2">
                    Business Address *
                  </label>
                  <textarea
                    id="businessAddress"
                    name="businessAddress"
                    required
                    rows={3}
                    placeholder="Enter complete business address"
                    className="w-full px-4 py-3 bg-background-card border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gradient-to-r focus:ring-primary-gradient-dark focus:border-transparent resize-vertical"
                  ></textarea>
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-white border-b border-gray-600 pb-2">Contact Information</h3>
                
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="contactName" className="block text-sm font-medium text-gray-300 mb-2">
                      Primary Contact Name *
                    </label>
                    <Input
                      id="contactName"
                      name="contactName"
                      type="text"
                      required
                      placeholder="Enter contact person name"
                    />
                  </div>
                  <div>
                    <label htmlFor="contactTitle" className="block text-sm font-medium text-gray-300 mb-2">
                      Title/Position *
                    </label>
                    <Input
                      id="contactTitle"
                      name="contactTitle"
                      type="text"
                      required
                      placeholder="Enter title or position"
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                      Email Address *
                    </label>
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      required
                      placeholder="Enter email address"
                    />
                  </div>
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2">
                      Phone Number *
                    </label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      required
                      placeholder="Enter phone number"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="whatsapp" className="block text-sm font-medium text-gray-300 mb-2">
                    WhatsApp Number
                  </label>
                  <Input
                    id="whatsapp"
                    name="whatsapp"
                    type="tel"
                    placeholder="Enter WhatsApp number"
                  />
                </div>
              </div>

              {/* Business Details */}
              <div className="space-y-6">
                <h3 className="text-xl font-semibold text-white border-b border-gray-600 pb-2">Business Details</h3>
                
                <div>
                  <label htmlFor="currentProducts" className="block text-sm font-medium text-gray-300 mb-2">
                    Current Products/Services *
                  </label>
                  <textarea
                    id="currentProducts"
                    name="currentProducts"
                    required
                    rows={3}
                    placeholder="Describe your current products and services"
                    className="w-full px-4 py-3 bg-background-card border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gradient-to-r focus:ring-primary-gradient-dark focus:border-transparent resize-vertical"
                  ></textarea>
                </div>

                <div>
                  <label htmlFor="targetMarket" className="block text-sm font-medium text-gray-300 mb-2">
                    Target Market *
                  </label>
                  <textarea
                    id="targetMarket"
                    name="targetMarket"
                    required
                    rows={3}
                    placeholder="Describe your target customers and market"
                    className="w-full px-4 py-3 bg-background-card border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gradient-to-r focus:ring-primary-gradient-dark focus:border-transparent resize-vertical"
                  ></textarea>
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="monthlyVolume" className="block text-sm font-medium text-gray-300 mb-2">
                      Expected Monthly Volume *
                    </label>
                    <select
                      id="monthlyVolume"
                      name="monthlyVolume"
                      required
                      className="w-full px-4 py-3 bg-background-card border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gradient-to-r focus:ring-primary-gradient-dark focus:border-transparent"
                    >
                      <option value="">Select volume</option>
                      <option value="10-25">10-25 units</option>
                      <option value="26-50">26-50 units</option>
                      <option value="51-100">51-100 units</option>
                      <option value="100+">100+ units</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="territoryInterest" className="block text-sm font-medium text-gray-300 mb-2">
                      Territory of Interest *
                    </label>
                    <Input
                      id="territoryInterest"
                      name="territoryInterest"
                      type="text"
                      required
                      placeholder="City, State or Region"
                    />
                  </div>
                </div>
              </div>

              {/* Additional Information */}
              <div>
                <label htmlFor="additionalInfo" className="block text-sm font-medium text-gray-300 mb-2">
                  Additional Information
                </label>
                <textarea
                  id="additionalInfo"
                  name="additionalInfo"
                  rows={4}
                  placeholder="Tell us why you want to become a Tisha International dealer and any other relevant information"
                  className="w-full px-4 py-3 bg-background-card border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gradient-to-r focus:ring-primary-gradient-dark focus:border-transparent resize-vertical"
                ></textarea>
              </div>

              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  id="terms"
                  name="terms"
                  required
                  className="w-4 h-4 text-primary-gradient-dark bg-background-card border-gray-600 rounded focus:ring-primary-gradient-dark focus:ring-2"
                />
                <label htmlFor="terms" className="text-gray-300 text-sm">
                  I agree to the terms and conditions and privacy policy *
                </label>
              </div>

              <Button variant="primary" size="lg" className="w-full bg-gradient-to-r from-[#f9c1b2] to-[#956358] hover:from-[#956358] hover:to-[#f9c1b2]">
                Submit Application
              </Button>
            </form>
          </Card>
        </div>
      </section>

      {/* Process Timeline Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <Card className="p-8">
            <h2 className="text-3xl font-bold text-white text-center mb-12">Application Process</h2>
            <div className="grid md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-xl">1</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-3">Application</h3>
                <p className="text-gray-300 text-sm">
                  Submit your completed dealer application form with all required information
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-xl">2</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-3">Review</h3>
                <p className="text-gray-300 text-sm">
                  Our team reviews your application and conducts initial qualification assessment
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-xl">3</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-3">Interview</h3>
                <p className="text-gray-300 text-sm">
                  Phone or video interview to discuss partnership details and expectations
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-xl">4</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-3">Onboarding</h3>
                <p className="text-gray-300 text-sm">
                  Complete dealer agreement, training, and setup of your dealer account
                </p>
              </div>
            </div>
            <div className="text-center mt-8">
              <p className="text-gray-300">
                <strong>Timeline:</strong> The entire process typically takes 2-3 weeks from application to activation.
              </p>
            </div>
          </Card>
        </div>
      </section>
    </div>
  );
}
