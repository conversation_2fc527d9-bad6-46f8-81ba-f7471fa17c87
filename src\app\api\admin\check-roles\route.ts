import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    // This is a development-only endpoint for checking roles and admin users
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    const supabase = createSupabaseAdminClient();

    // Get all roles
    const { data: roles, error: rolesError } = await supabase
      .from('roles')
      .select('*');

    // Get all admin users
    const { data: adminUsers, error: adminError } = await supabase
      .from('admin_users')
      .select('*');

    return NextResponse.json({
      roles: {
        data: roles,
        error: rolesError
      },
      adminUsers: {
        data: adminUsers,
        error: adminError
      }
    });

  } catch (error) {
    console.error('Check roles error:', error);
    return NextResponse.json(
      { error: 'Check failed', details: error },
      { status: 500 }
    );
  }
}
