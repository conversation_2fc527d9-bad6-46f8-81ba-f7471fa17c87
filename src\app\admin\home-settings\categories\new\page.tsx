'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, Upload, Search, Plus } from 'lucide-react';

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface Product {
  id: string;
  name: string;
  price: number;
  images: string[];
  brand: string;
  model: string;
  category_id: string;
  show_price?: boolean;
}

export default function AddFeaturedCategoryPage() {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set());
  const [formData, setFormData] = useState({
    display_order: 0,
    custom_image: ''
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [featuredCount, setFeaturedCount] = useState(0);

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (selectedCategory && searchTerm) {
      const categoryProducts = products.filter(product => product.category_id === selectedCategory.id);
      const filtered = categoryProducts.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.model.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredProducts(filtered);
    } else if (selectedCategory) {
      const categoryProducts = products.filter(product => product.category_id === selectedCategory.id);
      setFilteredProducts(categoryProducts);
    } else {
      setFilteredProducts([]);
    }
  }, [searchTerm, products, selectedCategory]);

  const fetchData = async () => {
    try {
      // Fetch all categories
      const categoriesResponse = await fetch('/api/categories');
      
      // Fetch already featured categories
      const featuredResponse = await fetch('/api/admin/home/<USER>');
      
      // Fetch all products
      const productsResponse = await fetch('/api/products?limit=1000');
      
      if (categoriesResponse.ok && featuredResponse.ok && productsResponse.ok) {
        const categoriesData = await categoriesResponse.json();
        const featuredData = await featuredResponse.json();
        const productsData = await productsResponse.json();
        
        // Get IDs of categories already featured
        const featuredCategoryIds = new Set(
          featuredData.categories?.map((item: any) => item.category_id) || []
        );
        
        // Filter out already featured categories
        const availableCategories = (categoriesData.categories || []).filter(
          (category: Category) => !featuredCategoryIds.has(category.id)
        );
        
        setCategories(availableCategories);
        setProducts(productsData.products || []);
        setFeaturedCount(featuredData.categories?.length || 0);
        
        // Set next display order
        const maxOrder = Math.max(
          ...featuredData.categories?.map((item: any) => item.display_order) || [0]
        );
        setFormData(prev => ({ ...prev, display_order: maxOrder + 1 }));
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleProductSelection = (productId: string) => {
    const newSelected = new Set(selectedProducts);
    if (newSelected.has(productId)) {
      newSelected.delete(productId);
    } else {
      newSelected.add(productId);
    }
    setSelectedProducts(newSelected);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedCategory) {
      alert('Please select a category');
      return;
    }

    if (selectedProducts.size === 0) {
      alert('Please select at least one product for the gallery');
      return;
    }

    setIsSaving(true);
    try {
      // Create featured category
      const categoryResponse = await fetch('/api/admin/home/<USER>', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          category_id: selectedCategory.id,
          display_order: formData.display_order,
          custom_image: formData.custom_image || null
        }),
      });

      if (!categoryResponse.ok) {
        throw new Error('Failed to create featured category');
      }

      // Add selected products to category gallery
      const galleryPromises = Array.from(selectedProducts).map((productId, index) => 
        fetch('/api/admin/home/<USER>', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            category_id: selectedCategory.id,
            product_id: productId,
            display_order: index
          }),
        })
      );

      await Promise.all(galleryPromises);
      router.push('/admin/home-settings');
    } catch (error) {
      console.error('Error creating featured category:', error);
      alert('Error creating featured category');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-[var(--accent-primary)] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4 border-b border-[var(--shadow-grey)] pb-4">
        <button
          onClick={() => router.back()}
          className="p-2 hover:bg-[var(--shadow-grey)] rounded-md transition-colors"
        >
          <ArrowLeft className="h-5 w-5 text-[var(--text-secondary)]" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-[var(--text-primary)]">Add Featured Category</h1>
          <p className="text-[var(--text-secondary)] mt-1">
            Select a category and products to feature on the home page
          </p>
          <div className="flex items-center space-x-4 mt-2">
            <span className="text-sm text-[var(--text-secondary)]">
              Current featured: {featuredCount} categories
            </span>
            <span className="text-sm text-[var(--accent-primary)]">
              Next order: #{formData.display_order}
            </span>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Category Selection */}
        <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-[var(--text-primary)]">Select Category</h2>
            <div className="text-sm text-[var(--text-secondary)]">
              {categories.length} available • {featuredCount} featured
            </div>
          </div>
          
          {categories.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {categories.map((category) => (
                <div
                  key={category.id}
                  onClick={() => setSelectedCategory(category)}
                  className={`p-4 rounded-lg border cursor-pointer transition-all ${
                    selectedCategory?.id === category.id
                      ? 'border-[var(--accent-primary)] bg-[var(--accent-primary)]/10'
                      : 'border-[var(--shadow-grey)] hover:border-[var(--accent-primary)]/50'
                  }`}
                >
                  <h3 className="font-semibold text-[var(--text-primary)] mb-1">
                    {category.name}
                  </h3>
                  <p className="text-sm text-[var(--text-secondary)]">
                    {products.filter(p => p.category_id === category.id).length} products
                  </p>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Upload className="h-12 w-12 text-[var(--text-secondary)] mx-auto mb-4" />
              <p className="text-[var(--text-secondary)] mb-2">All categories are already featured</p>
              <p className="text-xs text-[var(--text-secondary)] mb-4">
                Remove categories from featured list to add different ones, or create new categories
              </p>
              <div className="flex justify-center space-x-4">
                <button
                  type="button"
                  onClick={() => router.push('/admin/categories/new')}
                  className="btn-primary flex items-center space-x-2"
                >
                  <Plus className="h-4 w-4" />
                  <span>Create New Category</span>
                </button>
                <button
                  type="button"
                  onClick={() => router.push('/admin/categories')}
                  className="px-4 py-2 border border-[var(--shadow-grey)] text-[var(--text-secondary)] rounded-md hover:bg-[var(--shadow-grey)] transition-colors"
                >
                  Manage Categories
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Configuration */}
        {selectedCategory && (
          <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
            <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-4">Configuration</h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  Display Order
                </label>
                <input
                  type="number"
                  value={formData.display_order}
                  readOnly
                  className="w-full px-3 py-2 bg-[var(--shadow-grey)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-secondary)] cursor-not-allowed"
                />
                <p className="text-xs text-[var(--text-secondary)] mt-1">
                  Auto-assigned as next available order
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  Custom Image URL (Optional)
                </label>
                <input
                  type="url"
                  value={formData.custom_image}
                  onChange={(e) => setFormData(prev => ({ ...prev, custom_image: e.target.value }))}
                  className="w-full px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
                  placeholder="https://example.com/category-image.jpg"
                />
                <p className="text-xs text-[var(--text-secondary)] mt-1">
                  Custom image for the category showcase
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Product Gallery Selection */}
        {selectedCategory && (
          <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
            <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-4">
              Select Products for Gallery ({selectedProducts.size} selected)
            </h2>
            
            {/* Search */}
            <div className="relative mb-4">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--text-secondary)]" />
              <input
                type="text"
                placeholder="Search products in this category..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
              />
            </div>

            {/* Product Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
              {filteredProducts.map((product) => (
                <div
                  key={product.id}
                  onClick={() => toggleProductSelection(product.id)}
                  className={`p-3 rounded-lg border cursor-pointer transition-all ${
                    selectedProducts.has(product.id)
                      ? 'border-[var(--accent-primary)] bg-[var(--accent-primary)]/10'
                      : 'border-[var(--shadow-grey)] hover:border-[var(--accent-primary)]/50'
                  }`}
                >
                  <div className="aspect-square bg-[var(--shadow-grey)] rounded-lg mb-2 overflow-hidden">
                    {product.images && product.images.length > 0 ? (
                      <img
                        src={product.images[0]}
                        alt={product.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-[var(--text-secondary)]">
                        <Upload className="h-6 w-6" />
                      </div>
                    )}
                  </div>
                  <h3 className="font-semibold text-[var(--text-primary)] text-xs line-clamp-1 mb-1">
                    {product.name}
                  </h3>
                  <p className="text-xs text-[var(--text-secondary)] line-clamp-1">
                    {product.brand} • {product.model}
                  </p>
                  {selectedProducts.has(product.id) && (
                    <div className="mt-2 flex items-center justify-center">
                      <div className="w-5 h-5 bg-[var(--accent-primary)] rounded-full flex items-center justify-center">
                        <Plus className="h-3 w-3 text-white rotate-45" />
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {filteredProducts.length === 0 && selectedCategory && (
              <div className="text-center py-8">
                <p className="text-[var(--text-secondary)]">
                  {searchTerm 
                    ? `No products found matching "${searchTerm}" in ${selectedCategory.name}`
                    : `No products found in ${selectedCategory.name} category`
                  }
                </p>
              </div>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-6 py-2 border border-[var(--shadow-grey)] text-[var(--text-secondary)] rounded-md hover:bg-[var(--shadow-grey)] transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={!selectedCategory || selectedProducts.size === 0 || isSaving}
            className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4" />
            <span>{isSaving ? 'Creating...' : 'Create Featured Category'}</span>
          </button>
        </div>
      </form>
    </div>
  );
}
