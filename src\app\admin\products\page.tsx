'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Package, Plus, Search, Edit, Trash2, ChevronDown, SortAsc, SortDesc, ChevronLeft, ChevronRight, Check, X } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  brand: string;
  model_name: string;
  price: number;
  is_active: boolean;
  images: string[];
  stock_quantity: number;
  low_stock_threshold: number;
  status: string;
  categories?: {
    id: string;
    name: string;
    slug: string;
  };
  brands?: {
    id: string;
    name: string;
    slug: string;
  };
  created_at: string;
}

export default function AdminProductsPage() {
  const router = useRouter();
  const [searchTerm, setSearchTerm] = useState('');
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [categories, setCategories] = useState<{id: string; name: string; slug: string}[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [deleteModal, setDeleteModal] = useState<{isOpen: boolean, product: Product | null}>({
    isOpen: false,
    product: null
  });
  const [isDeleting, setIsDeleting] = useState(false);

  // New filtering and sorting states
  const [brands, setBrands] = useState<{id: string; name: string; slug: string}[]>([]);
  const [selectedBrand, setSelectedBrand] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedStockStatus, setSelectedStockStatus] = useState('all');
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalProducts, setTotalProducts] = useState(0);
  const [itemsPerPage] = useState(10);

  // Quick edit states
  const [editingStock, setEditingStock] = useState<string | null>(null);
  const [editStockValue, setEditStockValue] = useState('');
  const [statusDropdown, setStatusDropdown] = useState<string | null>(null);
  const [isUpdatingStock, setIsUpdatingStock] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  // Bulk action states
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [bulkAction, setBulkAction] = useState('');
  const [bulkActionData, setBulkActionData] = useState({
    brand_id: '',
    category_id: '',
    status: '',
    stock_quantity: '',
    price: '',
    price_adjustment_type: 'set' // 'set', 'increase', 'decrease'
  });
  const [isBulkUpdating, setIsBulkUpdating] = useState(false);
  const [bulkDeleteModal, setBulkDeleteModal] = useState<{isOpen: boolean, productCount: number}>({
    isOpen: false,
    productCount: 0
  });

  // Refs for click outside detection
  const statusDropdownRef = useRef<HTMLDivElement>(null);
  const stockInputRef = useRef<HTMLInputElement>(null);

  const fetchProducts = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      if (selectedBrand !== 'all') params.append('brand', selectedBrand);
      if (selectedStatus !== 'all') params.append('status', selectedStatus);
      if (minPrice) params.append('minPrice', minPrice);
      if (maxPrice) params.append('maxPrice', maxPrice);
      params.append('sortBy', sortBy);
      params.append('sortOrder', sortOrder);
      params.append('page', currentPage.toString());
      params.append('limit', itemsPerPage.toString());

      const response = await fetch(`/api/admin/products?${params}`);
      if (response.ok) {
        const data = await response.json();
        setProducts(data.products || []);
        setTotalPages(data.pagination?.totalPages || 1);
        setTotalProducts(data.pagination?.total || 0);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setIsLoading(false);
    }
  }, [searchTerm, selectedCategory, selectedBrand, selectedStatus, minPrice, maxPrice, sortBy, sortOrder, currentPage, itemsPerPage]);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await fetch('/api/admin/brands');
      if (response.ok) {
        const data = await response.json();
        setBrands(data.brands || []);
      }
    } catch (error) {
      console.error('Error fetching brands:', error);
    }
  };

  useEffect(() => {
    fetchProducts();
    fetchCategories();
    fetchBrands();
  }, [fetchProducts]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
    setSelectedProducts([]); // Clear selections when filters change
  }, [searchTerm, selectedCategory, selectedBrand, selectedStatus, minPrice, maxPrice, sortBy, sortOrder]);

  const getActiveStatus = (product: Product) => {
    const status = product.status || (product.is_active ? 'active' : 'deactivated');
    switch (status) {
      case 'active':
        return { status: 'Active', color: 'bg-green-100 text-green-800' };
      case 'deactivated':
        return { status: 'Deactivated', color: 'bg-red-100 text-red-800' };
      case 'archived':
        return { status: 'Archived', color: 'bg-gray-100 text-gray-800' };
      case 'private':
        return { status: 'Private', color: 'bg-purple-100 text-purple-800' };
      default:
        return { status: 'Inactive', color: 'bg-gray-100 text-gray-800' };
    }
  };

  const getStockStatus = (product: Product) => {
    const stock = product.stock_quantity || 0;
    const threshold = product.low_stock_threshold || 5;

    if (stock === 0) {
      return { status: 'Out of Stock', color: 'text-red-600', indicator: 'bg-red-500' };
    } else if (stock <= threshold) {
      return { status: 'Low Stock', color: 'text-yellow-600', indicator: 'bg-yellow-500' };
    } else {
      return { status: 'In Stock', color: 'text-green-600', indicator: 'bg-green-500' };
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(price);
  };

  // Filter products by stock status on frontend (since API doesn't support this filter yet)
  const getFilteredProducts = () => {
    if (selectedStockStatus === 'all') return products;

    return products.filter(product => {
      const stock = product.stock_quantity || 0;
      const threshold = product.low_stock_threshold || 5;

      switch (selectedStockStatus) {
        case 'in_stock':
          return stock > threshold;
        case 'low_stock':
          return stock > 0 && stock <= threshold;
        case 'out_of_stock':
          return stock === 0;
        default:
          return true;
      }
    });
  };

  const handleDeleteClick = (product: Product) => {
    setDeleteModal({ isOpen: true, product });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteModal.product) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/admin/products/${deleteModal.product.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Remove product from local state
        setProducts(products.filter(p => p.id !== deleteModal.product!.id));
        setDeleteModal({ isOpen: false, product: null });
        alert('Product deleted successfully!');
      } else {
        const error = await response.json();

        if (response.status === 409 && error.canArchive) {
          // Product has references, offer to archive instead
          const shouldArchive = confirm(
            `${error.message}\n\nWould you like to archive this product instead? This will hide it from customers but preserve the data.`
          );

          if (shouldArchive) {
            await handleArchiveProduct(deleteModal.product);
          }
        } else {
          alert(`Error: ${error.message || error.error}`);
        }
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      alert('Failed to delete product');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleArchiveProduct = async (product: Product) => {
    try {
      // First, fetch the full product data to get the correct structure
      const getResponse = await fetch(`/api/admin/products/${product.id}`);
      if (!getResponse.ok) {
        throw new Error('Failed to fetch product data');
      }

      const { product: fullProduct } = await getResponse.json();

      const response = await fetch(`/api/admin/products/${product.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: fullProduct.name,
          brand_id: fullProduct.brand_id,
          condition_id: fullProduct.condition_id,
          description: fullProduct.description || '',
          price: fullProduct.price,
          category_id: fullProduct.category_id,
          specifications: fullProduct.specifications || {},
          special_features: fullProduct.special_features || [],
          additional_features: fullProduct.additional_features || {},
          is_active: false
        }),
      });

      if (response.ok) {
        // Update product in local state
        setProducts(products.map(p =>
          p.id === product.id ? { ...p, is_active: false } : p
        ));
        setDeleteModal({ isOpen: false, product: null });
        alert('Product archived successfully! It is now hidden from customers.');
      } else {
        const error = await response.json();
        alert(`Error archiving product: ${error.error}`);
      }
    } catch (error) {
      console.error('Error archiving product:', error);
      alert('Failed to archive product');
    }
  };

  const handleDeleteCancel = () => {
    setDeleteModal({ isOpen: false, product: null });
  };

  // Stock management functions
  const handleStockChange = async (productId: string, change: number) => {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const newStock = Math.max(0, (product.stock_quantity || 0) + change);
    await updateProductStock(productId, newStock);
  };

  const handleStockDoubleClick = (productId: string, currentStock: number) => {
    setEditingStock(productId);
    setEditStockValue(currentStock.toString());
    setTimeout(() => {
      stockInputRef.current?.focus();
      stockInputRef.current?.select();
    }, 0);
  };

  const handleStockInputSubmit = async () => {
    if (!editingStock) return;

    const newStock = parseInt(editStockValue);
    if (isNaN(newStock) || newStock < 0) {
      setEditingStock(null);
      return;
    }

    await updateProductStock(editingStock, newStock);
    setEditingStock(null);
  };

  const updateProductStock = async (productId: string, newStock: number) => {
    setIsUpdatingStock(true);
    try {
      const response = await fetch(`/api/admin/products/${productId}/stock`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ stock_quantity: newStock }),
      });

      if (response.ok) {
        setProducts(products.map(p =>
          p.id === productId
            ? { ...p, stock_quantity: newStock }
            : p
        ));
      } else {
        alert('Failed to update stock');
      }
    } catch (error) {
      console.error('Error updating stock:', error);
      alert('Error updating stock');
    } finally {
      setIsUpdatingStock(false);
    }
  };

  // Status management functions
  const handleStatusChange = async (productId: string, newStatus: string) => {
    setIsUpdatingStatus(true);
    try {
      const response = await fetch(`/api/admin/products/${productId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        setProducts(products.map(p =>
          p.id === productId
            ? { ...p, status: newStatus, is_active: newStatus === 'active' }
            : p
        ));
        setStatusDropdown(null);
      } else {
        alert('Failed to update status');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      alert('Error updating status');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  // Bulk action functions
  const handleSelectProduct = (productId: string) => {
    setSelectedProducts(prev =>
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const handleSelectAll = () => {
    const filteredProducts = getFilteredProducts();
    if (selectedProducts.length === filteredProducts.length) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(filteredProducts.map(p => p.id));
    }
  };

  const handleBulkActionSubmit = async () => {
    if (!bulkAction || selectedProducts.length === 0) return;

    // Show confirmation modal for delete action
    if (bulkAction === 'delete_products') {
      setBulkDeleteModal({
        isOpen: true,
        productCount: selectedProducts.length
      });
      return;
    }

    setIsBulkUpdating(true);
    try {
      const response = await fetch('/api/admin/products/bulk-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productIds: selectedProducts,
          action: bulkAction,
          data: bulkActionData
        }),
      });

      if (response.ok) {
        const result = await response.json();
        // Refresh products to show updated data
        await fetchProducts();
        setSelectedProducts([]);
        setShowBulkActions(false);
        setBulkAction('');
        setBulkActionData({
          brand_id: '',
          category_id: '',
          status: '',
          stock_quantity: '',
          price: '',
          price_adjustment_type: 'set'
        });
        alert(`Successfully updated ${result.updatedCount} products`);
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || error.error}`);
      }
    } catch (error) {
      console.error('Error updating products:', error);
      alert('Failed to update products');
    } finally {
      setIsBulkUpdating(false);
    }
  };

  const handleBulkDeleteConfirm = async () => {
    setIsBulkUpdating(true);
    setBulkDeleteModal({ isOpen: false, productCount: 0 });

    try {
      const response = await fetch('/api/admin/products/bulk-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          productIds: selectedProducts,
          action: 'delete_products',
          data: {}
        }),
      });

      if (response.ok) {
        const result = await response.json();
        // Refresh products to show updated data (some may be deleted, some archived)
        await fetchProducts();
        setSelectedProducts([]);
        setShowBulkActions(false);
        setBulkAction('');
        setBulkActionData({
          brand_id: '',
          category_id: '',
          status: '',
          stock_quantity: '',
          price: '',
          price_adjustment_type: 'set'
        });
        // Show detailed message for delete results
        let message = result.message;
        if (result.errors && result.errors.length > 0) {
          message += '\n\nErrors:\n' + result.errors.join('\n');
        }
        alert(message);
      } else {
        const error = await response.json();
        alert(`Error: ${error.message || error.error}`);
      }
    } catch (error) {
      console.error('Error deleting products:', error);
      alert('Failed to delete products');
    } finally {
      setIsBulkUpdating(false);
    }
  };

  const handleBulkDeleteCancel = () => {
    setBulkDeleteModal({ isOpen: false, productCount: 0 });
  };

  const cancelBulkAction = () => {
    setShowBulkActions(false);
    setBulkAction('');
    setBulkActionData({
      brand_id: '',
      category_id: '',
      status: '',
      stock_quantity: '',
      price: '',
      price_adjustment_type: 'set'
    });
  };

  // Update bulk actions visibility when selection changes
  useEffect(() => {
    setShowBulkActions(selectedProducts.length > 0);
  }, [selectedProducts]);

  // Click outside handler
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (statusDropdownRef.current && !statusDropdownRef.current.contains(event.target as Node)) {
        setStatusDropdown(null);
      }
      if (editingStock && stockInputRef.current && !stockInputRef.current.contains(event.target as Node)) {
        setEditingStock(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [editingStock]);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Products</h1>
          <p className="text-gray-600">Manage your product inventory</p>
        </div>
        <button
          onClick={() => router.push('/admin/products/new')}
          className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-[#956358] to-[#f9c1b2] text-white text-sm font-medium rounded-md hover:from-[#956358]/90 hover:to-[#f9c1b2]/90 transition-all duration-200"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Product
        </button>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="space-y-4">
          {/* Search and Sort Row */}
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#956358] focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#956358] focus:border-transparent text-sm"
              >
                <option value="created_at">Sort by Date</option>
                <option value="name">Sort by Name</option>
                <option value="price">Sort by Price</option>
                <option value="stock_quantity">Sort by Stock</option>
                <option value="brands.name">Sort by Brand</option>
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="px-3 py-2 border border-gray-300 rounded-md hover:bg-gray-50 focus:ring-2 focus:ring-[#956358] focus:border-transparent"
                title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
              >
                {sortOrder === 'asc' ? <SortAsc className="h-4 w-4" /> : <SortDesc className="h-4 w-4" />}
              </button>
            </div>
          </div>

          {/* Filters Row */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#956358] focus:border-transparent text-sm"
            >
              <option value="all">All Categories</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>

            <select
              value={selectedBrand}
              onChange={(e) => setSelectedBrand(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#956358] focus:border-transparent text-sm"
            >
              <option value="all">All Brands</option>
              {brands.map((brand) => (
                <option key={brand.id} value={brand.id}>
                  {brand.name}
                </option>
              ))}
            </select>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#956358] focus:border-transparent text-sm"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="deactivated">Deactivated</option>
              <option value="archived">Archived</option>
              <option value="private">Private</option>
            </select>

            <select
              value={selectedStockStatus}
              onChange={(e) => setSelectedStockStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#956358] focus:border-transparent text-sm"
            >
              <option value="all">All Stock</option>
              <option value="in_stock">In Stock</option>
              <option value="low_stock">Low Stock</option>
              <option value="out_of_stock">Out of Stock</option>
            </select>

            <div className="flex gap-2">
              <input
                type="number"
                placeholder="Min Price"
                value={minPrice}
                onChange={(e) => setMinPrice(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#956358] focus:border-transparent text-sm"
                min="0"
              />
              <input
                type="number"
                placeholder="Max Price"
                value={maxPrice}
                onChange={(e) => setMaxPrice(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#956358] focus:border-transparent text-sm"
                min="0"
              />
            </div>
          </div>

          {/* Clear Filters Button */}
          {(searchTerm || selectedCategory !== 'all' || selectedBrand !== 'all' || selectedStatus !== 'all' || selectedStockStatus !== 'all' || minPrice || maxPrice) && (
            <div className="flex justify-end">
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('all');
                  setSelectedBrand('all');
                  setSelectedStatus('all');
                  setSelectedStockStatus('all');
                  setMinPrice('');
                  setMaxPrice('');
                  setSortBy('created_at');
                  setSortOrder('desc');
                }}
                className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-md transition-colors"
              >
                Clear All Filters
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Bulk Actions Bar */}
      {showBulkActions && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <span className="text-sm font-medium text-blue-900">
                {selectedProducts.length} product{selectedProducts.length !== 1 ? 's' : ''} selected
              </span>

              <select
                value={bulkAction}
                onChange={(e) => setBulkAction(e.target.value)}
                className="px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white"
              >
                <option value="">Select Action</option>
                <option value="update_brand">Update Brand</option>
                <option value="update_category">Update Category</option>
                <option value="update_status">Update Status</option>
                <option value="update_stock">Update Stock</option>
                <option value="update_price">Update Price</option>
                <option value="delete_products">Delete Products</option>
              </select>

              {/* Action-specific inputs */}
              {bulkAction === 'update_brand' && (
                <select
                  value={bulkActionData.brand_id}
                  onChange={(e) => setBulkActionData(prev => ({ ...prev, brand_id: e.target.value }))}
                  className="px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white"
                >
                  <option value="">Select Brand</option>
                  {brands.map((brand) => (
                    <option key={brand.id} value={brand.id}>
                      {brand.name}
                    </option>
                  ))}
                </select>
              )}

              {bulkAction === 'update_category' && (
                <select
                  value={bulkActionData.category_id}
                  onChange={(e) => setBulkActionData(prev => ({ ...prev, category_id: e.target.value }))}
                  className="px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white"
                >
                  <option value="">Select Category</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              )}

              {bulkAction === 'update_status' && (
                <select
                  value={bulkActionData.status}
                  onChange={(e) => setBulkActionData(prev => ({ ...prev, status: e.target.value }))}
                  className="px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white"
                >
                  <option value="">Select Status</option>
                  <option value="active">Active</option>
                  <option value="deactivated">Deactivated</option>
                  <option value="archived">Archived</option>
                  <option value="private">Private</option>
                </select>
              )}

              {bulkAction === 'update_stock' && (
                <input
                  type="number"
                  placeholder="Stock quantity"
                  value={bulkActionData.stock_quantity}
                  onChange={(e) => setBulkActionData(prev => ({ ...prev, stock_quantity: e.target.value }))}
                  className="px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white w-32"
                  min="0"
                />
              )}

              {bulkAction === 'update_price' && (
                <div className="flex items-center space-x-2">
                  <select
                    value={bulkActionData.price_adjustment_type}
                    onChange={(e) => setBulkActionData(prev => ({ ...prev, price_adjustment_type: e.target.value }))}
                    className="px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white"
                  >
                    <option value="set">Set Price</option>
                    <option value="increase">Increase by</option>
                    <option value="decrease">Decrease by</option>
                  </select>
                  <input
                    type="number"
                    placeholder="Price"
                    value={bulkActionData.price}
                    onChange={(e) => setBulkActionData(prev => ({ ...prev, price: e.target.value }))}
                    className="px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm bg-white w-32"
                    min="0"
                    step="0.01"
                  />
                </div>
              )}

              {/* Warning message for delete action */}
              {bulkAction === 'delete_products' && (
                <div className="flex items-center px-3 py-2 bg-red-50 border border-red-200 rounded-md">
                  <Trash2 className="h-4 w-4 text-red-600 mr-2" />
                  <span className="text-sm text-red-700 font-medium">
                    This will permanently delete selected products
                  </span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={handleBulkActionSubmit}
                disabled={!bulkAction || isBulkUpdating}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isBulkUpdating ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <Check className="h-4 w-4 mr-2" />
                    Apply
                  </>
                )}
              </button>
              <button
                onClick={cancelBulkAction}
                disabled={isBulkUpdating}
                className="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 disabled:opacity-50"
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Products Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          {isLoading ? (
            <div className="flex items-center justify-center h-64">
              <div className="w-8 h-8 border-2 border-[#956358] border-t-transparent rounded-full animate-spin"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                      <input
                        type="checkbox"
                        checked={selectedProducts.length === getFilteredProducts().length && getFilteredProducts().length > 0}
                        onChange={handleSelectAll}
                        className="h-4 w-4 text-[#956358] focus:ring-[#956358] border-gray-300 rounded"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Product
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Stock
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {getFilteredProducts().map((product) => {
                    const activeStatus = getActiveStatus(product);
                    const stockStatus = getStockStatus(product);
                    return (
                      <tr key={product.id} className="hover:bg-gray-50 group">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="checkbox"
                            checked={selectedProducts.includes(product.id)}
                            onChange={() => handleSelectProduct(product.id)}
                            className="h-4 w-4 text-[#956358] focus:ring-[#956358] border-gray-300 rounded"
                          />
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="h-10 w-10 flex-shrink-0">
                              {product.images && product.images.length > 0 ? (
                                <Image
                                  className="h-10 w-10 rounded-lg object-cover"
                                  src={product.images[0]}
                                  alt={product.name}
                                  width={40}
                                  height={40}
                                />
                              ) : (
                                <div className="h-10 w-10 bg-gray-200 rounded-lg flex items-center justify-center">
                                  <Package className="h-5 w-5 text-gray-500" />
                                </div>
                              )}
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{product.name}</div>
                              <div className="text-xs text-gray-500">
                                ID: {product.id.slice(0, 6)} | <span className="font-bold">{product.brands?.name || product.brand}</span>
                              </div>
                              <div className="text-sm text-gray-500">
                                {product.model_name}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {product.categories?.name || 'Uncategorized'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatPrice(product.price)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex flex-col items-start space-y-1">
                            {/* Stock quantity with quick edit */}
                            <div className="flex items-center w-24 justify-center">
                              {editingStock === product.id ? (
                                <input
                                  ref={stockInputRef}
                                  type="number"
                                  value={editStockValue}
                                  onChange={(e) => setEditStockValue(e.target.value)}
                                  onBlur={handleStockInputSubmit}
                                  onKeyDown={(e) => e.key === 'Enter' && handleStockInputSubmit()}
                                  className="w-16 px-2 py-1 text-sm border border-[#956358] rounded focus:outline-none focus:ring-1 focus:ring-[#956358]"
                                  min="0"
                                />
                              ) : (
                                <div className="relative w-full h-8 flex items-center justify-center">
                                  {/* Show stock number when not hovering */}
                                  <div className="opacity-100 group-hover:opacity-0 transition-opacity absolute inset-0 flex items-center justify-center pointer-events-none">
                                    <span
                                      onDoubleClick={() => handleStockDoubleClick(product.id, product.stock_quantity || 0)}
                                      className="cursor-pointer hover:bg-gray-100 px-2 py-1 rounded text-sm font-medium pointer-events-auto"
                                    >
                                      {product.stock_quantity || 0}
                                    </span>
                                  </div>

                                  {/* Quick stock adjustment buttons - show on hover */}
                                  <div className="opacity-0 group-hover:opacity-100 transition-opacity absolute inset-0 flex items-center justify-center space-x-1">
                                    <button
                                      onClick={() => handleStockChange(product.id, -1)}
                                      disabled={isUpdatingStock}
                                      className="w-6 h-6 flex items-center justify-center bg-red-100 hover:bg-red-200 text-red-600 rounded text-xs font-bold disabled:opacity-50 z-10"
                                      title="Decrease stock"
                                    >
                                      -
                                    </button>

                                    <span
                                      onDoubleClick={() => handleStockDoubleClick(product.id, product.stock_quantity || 0)}
                                      className="cursor-pointer hover:bg-gray-100 px-2 py-1 rounded text-sm font-medium min-w-[2rem] text-center z-10"
                                    >
                                      {product.stock_quantity || 0}
                                    </span>

                                    <button
                                      onClick={() => handleStockChange(product.id, 1)}
                                      disabled={isUpdatingStock}
                                      className="w-6 h-6 flex items-center justify-center bg-green-100 hover:bg-green-200 text-green-600 rounded text-xs font-bold disabled:opacity-50 z-10"
                                      title="Increase stock"
                                    >
                                      +
                                    </button>
                                  </div>
                                </div>
                              )}
                            </div>

                            {/* Stock status indicator */}
                            <div className="flex items-center w-24 justify-center">
                              <span className={`text-xs font-medium ${stockStatus.color}`}>
                                {stockStatus.status}
                              </span>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap relative">
                          <div className="relative" ref={statusDropdown === product.id ? statusDropdownRef : null}>
                            <button
                              onClick={() => setStatusDropdown(statusDropdown === product.id ? null : product.id)}
                              disabled={isUpdatingStatus}
                              className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${activeStatus.color} hover:opacity-80 transition-opacity disabled:opacity-50`}
                            >
                              {activeStatus.status}
                              <ChevronDown className="ml-1 h-3 w-3" />
                            </button>

                            {/* Status dropdown */}
                            {statusDropdown === product.id && (
                              <div className="absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-50">
                                <div className="py-1">
                                  {['active', 'deactivated', 'archived', 'private'].map((status) => (
                                    <button
                                      key={status}
                                      onClick={() => handleStatusChange(product.id, status)}
                                      disabled={isUpdatingStatus}
                                      className="block w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-100 disabled:opacity-50 capitalize"
                                    >
                                      {status}
                                    </button>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => router.push(`/admin/products/${product.id}/edit`)}
                              className="text-[#956358] hover:text-[#956358]/80 p-1 rounded hover:bg-gray-100 transition-colors"
                              title="Edit Product"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteClick(product)}
                              className="text-red-600 hover:text-red-800 p-1 rounded hover:bg-gray-100 transition-colors"
                              title="Delete Product"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Pagination */}
        {!isLoading && totalPages > 1 && (
          <div className="px-6 py-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalProducts)} of {totalProducts} products
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="h-4 w-4" />
                </button>

                {/* Page numbers */}
                <div className="flex space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-2 text-sm font-medium rounded-md ${
                          currentPage === pageNum
                            ? 'bg-[#956358] text-white'
                            : 'text-gray-700 hover:bg-gray-50 border border-gray-300'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRight className="h-4 w-4" />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Empty State */}
      {!isLoading && getFilteredProducts().length === 0 && (
        <div className="bg-white shadow rounded-lg p-12 text-center">
          <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No products found</h3>
          <p className="text-gray-600 mb-6">
            {searchTerm || selectedCategory !== 'all' || selectedBrand !== 'all' || selectedStatus !== 'all' || selectedStockStatus !== 'all' || minPrice || maxPrice
              ? 'Try adjusting your search terms or filters.'
              : 'Get started by adding your first product.'}
          </p>
          {!searchTerm && selectedCategory === 'all' && selectedBrand === 'all' && selectedStatus === 'all' && selectedStockStatus === 'all' && !minPrice && !maxPrice && (
            <button
              onClick={() => router.push('/admin/products/new')}
              className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-[#956358] to-[#f9c1b2] text-white text-sm font-medium rounded-md hover:from-[#956358]/90 hover:to-[#f9c1b2]/90"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Product
            </button>
          )}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteModal.isOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <Trash2 className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Delete Product</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete &quot;{deleteModal.product?.name}&quot;?
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  <strong>Note:</strong> If this product has associated inquiries or other references,
                  you will be offered the option to archive it instead of deleting it.
                </p>
              </div>
              <div className="flex gap-4 px-4 py-3">
                <button
                  onClick={handleDeleteCancel}
                  disabled={isDeleting}
                  className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteConfirm}
                  disabled={isDeleting}
                  className="flex-1 px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
                >
                  {isDeleting ? (
                    <div className="flex items-center justify-center">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Deleting...
                    </div>
                  ) : (
                    'Delete'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Delete Confirmation Modal */}
      {bulkDeleteModal.isOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <Trash2 className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Delete Multiple Products</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete <strong>{bulkDeleteModal.productCount}</strong> selected product{bulkDeleteModal.productCount !== 1 ? 's' : ''}?
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  <strong>Warning:</strong> This action cannot be undone. If any products have associated inquiries or other references,
                  they will be offered for archiving instead of deletion.
                </p>
              </div>
              <div className="flex gap-4 px-4 py-3">
                <button
                  onClick={handleBulkDeleteCancel}
                  disabled={isBulkUpdating}
                  className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 text-base font-medium rounded-md shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300 disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleBulkDeleteConfirm}
                  disabled={isBulkUpdating}
                  className="flex-1 px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50"
                >
                  {isBulkUpdating ? (
                    <div className="flex items-center justify-center">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Deleting...
                    </div>
                  ) : (
                    `Delete ${bulkDeleteModal.productCount} Product${bulkDeleteModal.productCount !== 1 ? 's' : ''}`
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
