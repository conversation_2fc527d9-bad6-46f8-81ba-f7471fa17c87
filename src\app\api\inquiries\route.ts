import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@/lib/supabase/server';
import { emailService } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    const { 
      productId, 
      inquiryType = 'regular', 
      quantity = 1, 
      userDetails 
    } = await request.json();

    if (!productId || !userDetails) {
      return NextResponse.json(
        { error: 'Product ID and user details are required' },
        { status: 400 }
      );
    }

    // Validate user details
    const { fullName, email, phone, address } = userDetails;
    if (!fullName || !email || !phone) {
      return NextResponse.json(
        { error: 'Full name, email, and phone are required' },
        { status: 400 }
      );
    }

    const supabase = await getSupabaseServerClient();

    // Get or create user
    let userId = null;
    const { data: session } = await supabase.auth.getSession();
    
    if (session?.session?.user) {
      userId = session.session.user.id;
      
      // Update user details if they've changed
      await supabase
        .from('users')
        .upsert({
          id: userId,
          email,
          password_hash: '', // Required by schema but not used with Supabase Auth
          full_name: fullName,
          phone,
          address
        });
    } else {
      // Create guest user entry or find existing by email
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('email', email)
        .single();

      if (existingUser) {
        userId = existingUser.id;
      } else {
        const { data: newUser, error: userError } = await supabase
          .from('users')
          .insert({
            email,
            password_hash: '', // Required by schema but not used with Supabase Auth
            full_name: fullName,
            phone,
            address
          })
          .select('id')
          .single();

        if (userError) {
          console.error('Error creating user:', userError);
          return NextResponse.json(
            { error: 'Failed to create user record' },
            { status: 500 }
          );
        }

        userId = newUser.id;
      }
    }

    // Create inquiry
    const { data: inquiry, error: inquiryError } = await supabase
      .from('inquiries')
      .insert({
        user_id: userId,
        product_id: productId,
        type: inquiryType,
        subject: `Product Inquiry - ${inquiryType === 'bulk' ? 'Bulk Order' : 'Regular'}`,
        message: `Customer inquiry for product. Quantity: ${quantity}. Contact details: ${JSON.stringify(userDetails)}`,
        quantity,
        contact_details: userDetails,
        urgency_level: 'medium',
        preferred_contact_method: 'email',
        status: 'pending'
      })
      .select('*')
      .single();

    if (inquiryError) {
      console.error('Error creating inquiry:', inquiryError);
      return NextResponse.json(
        { error: 'Failed to create inquiry' },
        { status: 500 }
      );
    }

    // Send initial chat message
    await supabase
      .from('chat_messages')
      .insert({
        inquiry_id: inquiry.id,
        sender_type: 'user',
        sender_id: userId,
        message_type: 'text',
        message: `Hi! I'm interested in this ${inquiryType === 'bulk' ? 'bulk order' : 'product'}. ${quantity > 1 ? `Quantity: ${quantity}.` : ''} Please let me know about availability and pricing.`
      });

    // Send email notifications
    try {
      const inquiryData = {
        id: inquiry.id,
        inquiry_type: inquiryType,
        quantity,
        status: 'pending',
        user_details: {
          fullName: userDetails.fullName,
          email: userDetails.email,
          phone: userDetails.phone,
          address: userDetails.address
        },
        created_at: inquiry.created_at
      };

      // Send confirmation email to customer
      await emailService.sendInquiryConfirmation(inquiryData);

      // Send notification email to admin
      await emailService.sendInquiryNotificationToAdmin(inquiryData);
    } catch (emailError) {
      console.error('Error sending email notifications:', emailError);
      // Don't fail the inquiry creation if email fails
    }

    return NextResponse.json({
      message: 'Inquiry created successfully',
      inquiry
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await getSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const userId = user.id;
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Fetch user's inquiries
    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const { data: inquiries, error } = await supabase
      .from('inquiries')
      .select(`
        *,
        products (
          id,
          name,
          slug,
          brand,
          model,
          price,
          images,
          specifications,
          features,
          categories (
            id,
            name,
            slug
          )
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .order('id', { ascending: true }) // Add secondary sort for stable pagination
      .range(from, to);

    if (error) {
      console.error('Error fetching inquiries:', error);
      return NextResponse.json(
        { error: 'Failed to fetch inquiries' },
        { status: 500 }
      );
    }

    // Get total count
    const { count } = await supabase
      .from('inquiries')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    return NextResponse.json({
      inquiries,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
