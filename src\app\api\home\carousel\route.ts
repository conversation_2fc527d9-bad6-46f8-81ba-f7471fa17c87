import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET() {
  try {
    // Get carousel settings
    const { data: settings } = await supabase
      .from('home_carousel_settings')
      .select('*')
      .single();

    // Get global banner image from admin settings
    const { data: bannerSettings } = await supabase
      .from('admin_settings')
      .select('setting_value')
      .eq('setting_key', 'carousel_banner_image')
      .single();

    // Get carousel products
    const { data: carouselData, error: carouselError } = await supabase
      .from('home_carousel_products')
      .select(`
        id,
        product_id,
        display_order,
        custom_image,
        custom_description,
        custom_background_image,
        is_active,
        products (
          id,
          name,
          price,
          images,
          brand,
          model,
          description,
          show_price,
          specifications,
          additional_features,
          slug
        )
      `)
      .eq('is_active', true)
      .order('display_order');

    if (carouselError) {
      console.error('Error fetching carousel products:', carouselError);
      return NextResponse.json({ error: 'Failed to fetch carousel products' }, { status: 500 });
    }

    // Transform data for frontend
    const globalBannerImage = bannerSettings?.setting_value || '';
    const products = carouselData?.map(item => ({
      id: item.products.id,
      name: item.products.name,
      price: item.products.price,
      image: item.custom_image || (item.products.images && item.products.images.length > 0 ? item.products.images[0] : ''),
      background_image: item.custom_background_image || globalBannerImage || (item.products.images && item.products.images.length > 0 ? item.products.images[0] : ''),
      brand: item.products.brand,
      model: item.products.model,
      description: item.custom_description || item.products.description,
      show_price: item.products.show_price,
      specifications: item.products.specifications || {},
      additional_features: item.products.additional_features || {},
      slug: item.products.slug
    })) || [];

    return NextResponse.json({
      products,
      settings: {
        autoPlayInterval: settings?.auto_play_interval || 5000
      }
    });

  } catch (error) {
    console.error('Error in carousel API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
