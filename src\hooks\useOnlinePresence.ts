import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/lib/supabase/client';

interface OnlineUser {
  user_id: string;
  user_type: 'user' | 'admin';
  last_seen: string;
  inquiry_id: string;
}

interface UseOnlinePresenceProps {
  inquiryId: string;
  currentUserId?: string;
  isAdmin?: boolean;
}

export function useOnlinePresence({ inquiryId, currentUserId, isAdmin = false }: UseOnlinePresenceProps) {
  const [onlineUsers, setOnlineUsers] = useState<OnlineUser[]>([]);
  const [isOnline, setIsOnline] = useState(false);
  const channelRef = useRef<any>(null);
  const heartbeatRef = useRef<NodeJS.Timeout | null>(null);

  // Send presence update
  const updatePresence = useCallback((online: boolean = true) => {
    if (channelRef.current && currentUserId) {
      const presenceData = {
        user_id: currentUserId,
        user_type: isAdmin ? 'admin' : 'user',
        inquiry_id: inquiryId,
        last_seen: new Date().toISOString(),
        online
      };

      channelRef.current.track(presenceData);
    }
  }, [currentUserId, inquiryId, isAdmin]);

  // Set up presence tracking
  useEffect(() => {
    if (!inquiryId || !currentUserId) return;

    // Clean up previous subscription
    if (channelRef.current) {
      channelRef.current.unsubscribe();
    }

    const channel = supabase
      .channel(`presence_${inquiryId}`)
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState();
        const users: OnlineUser[] = [];
        
        Object.keys(state).forEach(key => {
          const presences = state[key] as any[];
          presences.forEach(presence => {
            if (presence.user_id !== currentUserId) {
              users.push(presence);
            }
          });
        });
        
        setOnlineUsers(users);
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        console.log('User joined:', newPresences);
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        console.log('User left:', leftPresences);
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          setIsOnline(true);
          updatePresence(true);
          
          // Set up heartbeat to maintain presence
          heartbeatRef.current = setInterval(() => {
            updatePresence(true);
          }, 30000); // Update every 30 seconds
        }
      });

    channelRef.current = channel;

    // Handle page visibility changes
    const handleVisibilityChange = () => {
      if (document.hidden) {
        updatePresence(false);
      } else {
        updatePresence(true);
      }
    };

    // Handle page unload
    const handleBeforeUnload = () => {
      updatePresence(false);
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      if (channelRef.current) {
        updatePresence(false);
        channelRef.current.unsubscribe();
      }
      
      if (heartbeatRef.current) {
        clearInterval(heartbeatRef.current);
      }
      
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
      setIsOnline(false);
    };
  }, [inquiryId, currentUserId, updatePresence]);

  // Check if other user is online
  const isOtherUserOnline = useCallback(() => {
    return onlineUsers.length > 0;
  }, [onlineUsers]);

  // Get other user type (admin or user)
  const getOtherUserType = useCallback(() => {
    if (onlineUsers.length > 0) {
      return onlineUsers[0].user_type;
    }
    return null;
  }, [onlineUsers]);

  return {
    onlineUsers,
    isOnline,
    isOtherUserOnline: isOtherUserOnline(),
    otherUserType: getOtherUserType(),
    updatePresence,
  };
}
