import Link from 'next/link'
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react'

const footerNavigation = {
  products: [
    { name: 'Laptops', href: '/products?category=laptops' },
    { name: 'Desktops', href: '/products?category=desktops' },
    { name: 'Workstations', href: '/products?category=workstations' },
    { name: 'Gaming PCs', href: '/products?category=gaming' },
  ],
  company: [
    { name: 'About Us', href: '/about' },
    { name: 'Services', href: '/services' },
    { name: 'Become Dealer', href: '/become-dealer' },
    { name: 'Contact', href: '/contact' },
  ],
  support: [
    { name: 'Help Center', href: '/help' },
    { name: 'Warranty', href: '/warranty' },
    { name: 'Returns', href: '/returns' },
    { name: 'Shipping Info', href: '/shipping' },
  ],
  legal: [
    { name: 'Privacy Policy', href: '/privacy' },
    { name: 'Terms of Service', href: '/terms' },
    { name: 'Cookie Policy', href: '/cookies' },
    { name: 'Refund Policy', href: '/refund' },
  ],
}

const socialLinks = [
  { name: 'Facebook', href: '#', icon: Facebook },
  { name: 'Twitter', href: '#', icon: Twitter },
  { name: 'Instagram', href: '#', icon: Instagram },
  { name: 'LinkedIn', href: '#', icon: Linkedin },
]

export function Footer() {
  return (
    <footer className="bg-[var(--deep-night-blue)] border-t border-[var(--shadow-grey)]/50 shadow-professional">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        {/* Main footer content */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
            {/* Company info */}
            <div className="lg:col-span-2 animate-fade-in">
              <div className="flex items-center space-x-3 mb-4 group">
                <img
                  src="/Images/Tisha_Logo.png"
                  alt="Tisha International Logo"
                  className="h-8 w-auto group-hover:scale-110 transition-transform duration-300"
                />
                <div className="text-2xl font-bold font-['Poppins'] text-gradient group-hover:animate-pulse-glow transition-all duration-300">
                  TISHA INTERNATIONAL
                </div>
              </div>
              <p className="text-[var(--soft-white)] text-sm leading-relaxed mb-6">
                Your trusted partner for premium refurbished laptops and desktops.
                We deliver quality, reliability, and exceptional value with every purchase.
              </p>

              {/* Contact info */}
              <div className="space-y-3">
                <div className="flex items-center text-sm text-[var(--soft-white)] hover:text-[var(--accent-secondary)] transition-colors duration-300 group">
                  <Mail className="h-4 w-4 mr-3 text-[var(--accent-secondary)] group-hover:scale-110 transition-transform duration-300" />
                  <EMAIL>
                </div>
                <div className="flex items-center text-sm text-[var(--soft-white)] hover:text-[var(--accent-secondary)] transition-colors duration-300 group">
                  <Phone className="h-4 w-4 mr-3 text-[var(--accent-secondary)] group-hover:scale-110 transition-transform duration-300" />
                  +****************
                </div>
                <div className="flex items-center text-sm text-[var(--soft-white)] hover:text-[var(--accent-secondary)] transition-colors duration-300 group">
                  <MapPin className="h-4 w-4 mr-3 text-[var(--accent-secondary)] group-hover:scale-110 transition-transform duration-300" />
                  123 Business St, Tech City, TC 12345
                </div>
              </div>
            </div>

            {/* Products */}
            <div className="animate-fade-in" style={{ animationDelay: '0.1s' }}>
              <h3 className="text-sm font-semibold text-[var(--accent-secondary)] tracking-wider uppercase mb-4 border-b border-[var(--accent-primary)]/30 pb-2">
                Products
              </h3>
              <ul className="space-y-2">
                {footerNavigation.products.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className="text-sm text-[var(--soft-white)] hover:text-[var(--accent-secondary)] transition-all duration-300 hover:translate-x-1 block"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company */}
            <div className="animate-fade-in" style={{ animationDelay: '0.2s' }}>
              <h3 className="text-sm font-semibold text-[var(--accent-secondary)] tracking-wider uppercase mb-4 border-b border-[var(--accent-primary)]/30 pb-2">
                Company
              </h3>
              <ul className="space-y-2">
                {footerNavigation.company.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className="text-sm text-[var(--soft-white)] hover:text-[var(--accent-secondary)] transition-all duration-300 hover:translate-x-1 block"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Support */}
            <div className="animate-fade-in" style={{ animationDelay: '0.3s' }}>
              <h3 className="text-sm font-semibold text-[var(--accent-secondary)] tracking-wider uppercase mb-4 border-b border-[var(--accent-primary)]/30 pb-2">
                Support
              </h3>
              <ul className="space-y-2">
                {footerNavigation.support.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className="text-sm text-[var(--soft-white)] hover:text-[var(--accent-secondary)] transition-all duration-300 hover:translate-x-1 block"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Legal */}
            <div className="animate-fade-in" style={{ animationDelay: '0.4s' }}>
              <h3 className="text-sm font-semibold text-[var(--accent-secondary)] tracking-wider uppercase mb-4 border-b border-[var(--accent-primary)]/30 pb-2">
                Legal
              </h3>
              <ul className="space-y-2">
                {footerNavigation.legal.map((item) => (
                  <li key={item.name}>
                    <Link
                      href={item.href}
                      className="text-sm text-[var(--soft-white)] hover:text-[var(--accent-secondary)] transition-all duration-300 hover:translate-x-1 block"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Newsletter signup */}
        <div className="border-t border-[var(--shadow-grey)]/50 py-8 animate-fade-in" style={{ animationDelay: '0.5s' }}>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div className="mb-4 md:mb-0">
              <h3 className="text-lg font-semibold text-[var(--accent-secondary)] mb-2">
                Stay updated with our latest offers
              </h3>
              <p className="text-sm text-[var(--soft-white)]">
                Get the best deals on refurbished computers delivered to your inbox.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 md:w-96">
              <input
                type="email"
                placeholder="Enter your email"
                className="input flex-1 focus-ring"
              />
              <button className="btn-primary whitespace-nowrap hover:animate-pulse-glow">
                Subscribe
              </button>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="border-t border-[var(--shadow-grey)]/50 py-6 animate-fade-in" style={{ animationDelay: '0.6s' }}>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            {/* Copyright */}
            <div className="text-sm text-[var(--muted-grey)] mb-4 md:mb-0">
              © {new Date().getFullYear()} Tisha International. All rights reserved.
            </div>

            {/* Social links */}
            <div className="flex space-x-4">
              {socialLinks.map((item) => {
                const Icon = item.icon
                return (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="text-[var(--muted-grey)] hover:text-[var(--accent-secondary)] transition-all duration-300 hover:scale-110 p-2 rounded-lg hover:bg-[var(--accent-primary)]/10"
                  >
                    <span className="sr-only">{item.name}</span>
                    <Icon className="h-5 w-5" />
                  </Link>
                )
              })}
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
