import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    // This is a development-only endpoint for fixing admin user roles
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    const supabase = createSupabaseAdminClient();

    // Update admin user with correct role_id
    const { error: adminError } = await supabase
      .from('admin_users')
      .update({ role: 'super_admin' })
      .eq('email', '<EMAIL>');

    // Update support user with correct role_id
    const { error: supportError } = await supabase
      .from('admin_users')
      .update({ role: 'support' })
      .eq('email', '<EMAIL>');

    // Verify the updates
    const { data: adminUser, error: adminFetchError } = await supabase
      .from('admin_users')
      .select('email, role_id')
      .eq('email', '<EMAIL>')
      .single();

    const { data: supportUser, error: supportFetchError } = await supabase
      .from('admin_users')
      .select('email, role_id')
      .eq('email', '<EMAIL>')
      .single();

    return NextResponse.json({
      message: 'Admin user roles fixed',
      adminError,
      supportError,
      adminUser,
      supportUser,
      adminFetchError,
      supportFetchError
    });

  } catch (error) {
    console.error('Role fix error:', error);
    return NextResponse.json(
      { error: 'Role fix failed', details: error },
      { status: 500 }
    );
  }
}
