export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string
          phone: string | null
          address: string | null
          company_name: string | null
          business_type: string | null
          tax_id: string | null
          website: string | null
          business_address: string | null
          business_phone: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          email: string
          full_name: string
          phone?: string | null
          address?: string | null
          company_name?: string | null
          business_type?: string | null
          tax_id?: string | null
          website?: string | null
          business_address?: string | null
          business_phone?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          phone?: string | null
          whatsapp_number?: string | null
          address?: string | null
          company_name?: string | null
          business_type?: string | null
          tax_id?: string | null
          website?: string | null
          business_address?: string | null
          business_phone?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      admin_users: {
        Row: {
          id: string
          email: string
          password_hash: string
          role_id: string
          full_name: string
          is_active: boolean
          created_at: string
        }
        Insert: {
          id?: string
          email: string
          password_hash: string
          role_id: string
          full_name: string
          is_active?: boolean
          created_at?: string
        }
        Update: {
          id?: string
          email?: string
          password_hash?: string
          role_id?: string
          full_name?: string
          is_active?: boolean
          created_at?: string
        }
      }
      roles: {
        Row: {
          id: string
          name: string
          permissions: Record<string, any>
          created_at: string
        }
        Insert: {
          id?: string
          name: string
          permissions: Record<string, any>
          created_at?: string
        }
        Update: {
          id?: string
          name?: string
          permissions?: Record<string, any>
          created_at?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          slug: string
          description: string | null
          image_url: string | null
          is_active: boolean
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string | null
          image_url?: string | null
          is_active?: boolean
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string | null
          image_url?: string | null
          is_active?: boolean
        }
      }
      products: {
        Row: {
          id: string
          category_id: string
          name: string
          slug: string
          brand: string
          model_name: string
          screen_size: string | null
          color: string | null
          storage: string | null
          ram: string | null
          operating_system: string | null
          graphics_card: string | null
          special_features: string[] | null
          additional_features: Record<string, any> | null
          description: string | null
          images: string[] | null
          price: number
          show_price: boolean
          stock_quantity: number
          low_stock_threshold: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          category_id: string
          name: string
          slug: string
          brand: string
          model_name: string
          screen_size?: string | null
          color?: string | null
          storage?: string | null
          ram?: string | null
          operating_system?: string | null
          graphics_card?: string | null
          special_features?: string[] | null
          additional_features?: Record<string, any> | null
          description?: string | null
          images?: string[] | null
          price: number
          show_price?: boolean
          stock_quantity?: number
          low_stock_threshold?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          category_id?: string
          name?: string
          slug?: string
          brand?: string
          model_name?: string
          screen_size?: string | null
          color?: string | null
          storage?: string | null
          ram?: string | null
          operating_system?: string | null
          graphics_card?: string | null
          special_features?: string[] | null
          additional_features?: Record<string, any> | null
          description?: string | null
          images?: string[] | null
          price?: number
          stock_quantity?: number
          low_stock_threshold?: number
          is_active?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      inquiries: {
        Row: {
          id: string
          user_id: string
          product_id: string
          inquiry_type: 'regular' | 'bulk' | 'hot'
          quantity: number
          status: 'pending' | 'in_progress' | 'accepted' | 'rejected' | 'completed'
          user_details: Record<string, any>
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          product_id: string
          inquiry_type: 'regular' | 'bulk' | 'hot'
          quantity: number
          status?: 'pending' | 'in_progress' | 'accepted' | 'rejected' | 'completed'
          user_details: Record<string, any>
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          product_id?: string
          inquiry_type?: 'regular' | 'bulk'
          quantity?: number
          status?: 'pending' | 'in_progress' | 'accepted' | 'rejected' | 'completed'
          user_details?: Record<string, any>
          created_at?: string
          updated_at?: string
        }
      }
      chat_messages: {
        Row: {
          id: string
          inquiry_id: string
          sender_type: 'user' | 'admin'
          sender_id: string
          message_type: 'text' | 'image' | 'link'
          content: string
          created_at: string
        }
        Insert: {
          id?: string
          inquiry_id: string
          sender_type: 'user' | 'admin'
          sender_id: string
          message_type: 'text' | 'image' | 'link'
          content: string
          created_at?: string
        }
        Update: {
          id?: string
          inquiry_id?: string
          sender_type?: 'user' | 'admin'
          sender_id?: string
          message_type?: 'text' | 'image' | 'link'
          content?: string
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
