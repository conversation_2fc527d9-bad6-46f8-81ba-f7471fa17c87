import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function POST() {
  try {
    const supabase = createSupabaseAdminClient();

    // Create the product-images bucket
    const { data: bucket, error: bucketError } = await supabase.storage
      .createBucket('product-images', {
        public: true,
        fileSizeLimit: 10485760, // 10MB
        allowedMimeTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
      });

    if (bucketError) {
      console.error('Error creating bucket:', bucketError);
      
      // Check if bucket already exists
      if (bucketError.message?.includes('already exists')) {
        return NextResponse.json({
          message: 'Storage bucket already exists',
          bucket: 'product-images'
        });
      }
      
      return NextResponse.json(
        { error: 'Failed to create storage bucket', details: bucketError },
        { status: 500 }
      );
    }

    // Set up bucket policy for public access
    const { error: policyError } = await supabase.storage
      .from('product-images')
      .createSignedUrl('test', 60); // Test if bucket is accessible

    return NextResponse.json({
      message: 'Storage bucket created successfully',
      bucket: bucket,
      bucketName: 'product-images'
    });

  } catch (error) {
    console.error('Setup storage error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const supabase = createSupabaseAdminClient();

    // List all buckets
    const { data: buckets, error } = await supabase.storage.listBuckets();

    if (error) {
      console.error('Error listing buckets:', error);
      return NextResponse.json(
        { error: 'Failed to list buckets', details: error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      buckets: buckets || [],
      hasProductImagesBucket: buckets?.some(b => b.name === 'product-images') || false
    });

  } catch (error) {
    console.error('List buckets error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    );
  }
}
