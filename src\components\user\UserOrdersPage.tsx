'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { formatPrice } from '@/lib/product-utils';
import { 
  Package, 
  Calendar, 
  MessageCircle, 
  Eye,
  User,
  Settings,
  History,
  LogOut,
  ChevronRight,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

interface Inquiry {
  id: string;
  product_id: string;
  type: 'regular' | 'bulk';
  quantity: number;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  subject: string;
  message: string;
  contact_details: {
    fullName: string;
    email: string;
    phone: string;
    whatsappNumber: string;
    address: string;
  };
  created_at: string;
  updated_at: string;
  products: {
    id: string;
    name: string;
    brand: string;
    model: string;
    price: number;
    images: string[];
    specifications?: Record<string, any>;
    features?: string[];
  };
}

export function UserOrdersPage() {
  const { user, signOut, loading } = useAuth();
  const router = useRouter();
  const [inquiries, setInquiries] = useState<Inquiry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin?redirect=/user/orders');
    }
  }, [user, loading, router]);

  // Fetch user inquiries
  useEffect(() => {
    const fetchInquiries = async () => {
      if (!user) return;

      try {
        const response = await fetch('/api/inquiries');
        if (response.ok) {
          const data = await response.json();
          setInquiries(data.inquiries || []);
        } else {
          throw new Error('Failed to fetch inquiries');
        }
      } catch (error) {
        console.error('Error fetching inquiries:', error);
        setError('Failed to load order history');
      } finally {
        setIsLoading(false);
      }
    };

    fetchInquiries();
  }, [user]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'in_progress':
        return 'text-blue-400 bg-blue-400/10 border-blue-400/20';
      case 'completed':
        return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'cancelled':
        return 'text-red-400 bg-red-400/10 border-red-400/20';
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'in_progress':
        return <AlertCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  if (loading || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#1a2b4a] flex items-center justify-center">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#1a2b4a] py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">Order History</h1>
          <p className="text-gray-300">Track and manage your product inquiries</p>
        </div>

        <div className="grid gap-8 lg:grid-cols-4">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-[#1a2b4a]/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-[#956358] to-[#f9c1b2] rounded-full mx-auto mb-4 flex items-center justify-center">
                  <User className="h-8 w-8 text-white" />
                </div>
                <h2 className="text-lg font-semibold text-white">{user.email}</h2>
              </div>

              <nav className="space-y-2">
                <button 
                  onClick={() => router.push('/user/profile')}
                  className="w-full flex items-center space-x-3 px-4 py-3 text-left text-gray-300 hover:text-white hover:bg-[#0a1e3d] rounded-lg transition-colors"
                >
                  <Settings className="h-5 w-5" />
                  <span>Profile Settings</span>
                </button>
                
                <button className="w-full flex items-center space-x-3 px-4 py-3 text-left text-white bg-[#956358]/20 rounded-lg border border-[#956358]/30">
                  <History className="h-5 w-5" />
                  <span>Order History</span>
                </button>

                <button 
                  onClick={handleSignOut}
                  className="w-full flex items-center space-x-3 px-4 py-3 text-left text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-colors"
                >
                  <LogOut className="h-5 w-5" />
                  <span>Sign Out</span>
                </button>
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-[#1a2b4a]/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl shadow-2xl">
              {error ? (
                <div className="p-8 text-center">
                  <div className="text-red-400 mb-4">{error}</div>
                  <button 
                    onClick={() => window.location.reload()}
                    className="text-[#f9c1b2] hover:text-[#956358] transition-colors"
                  >
                    Try Again
                  </button>
                </div>
              ) : inquiries.length === 0 ? (
                <div className="p-8 text-center">
                  <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-white mb-2">No Orders Yet</h3>
                  <p className="text-gray-400 mb-6">You haven&apos;t made any product inquiries yet.</p>
                  <button
                    onClick={() => router.push('/products')}
                    className="bg-gradient-to-r from-[#956358] to-[#f9c1b2] text-white px-6 py-3 rounded-lg hover:shadow-lg hover:shadow-[#956358]/25 transition-all duration-300 transform hover:scale-[1.02]"
                  >
                    Browse Products
                  </button>
                </div>
              ) : (
                <div className="p-6">
                  <h3 className="text-xl font-semibold text-white mb-6">
                    Your Inquiries ({inquiries.length})
                  </h3>
                  
                  <div className="space-y-4">
                    {inquiries.map((inquiry) => (
                      <div
                        key={inquiry.id}
                        className="bg-[#0a1e3d]/50 border border-gray-700/50 rounded-xl p-6 hover:border-[#956358]/30 transition-all duration-300"
                      >
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-start space-x-4">
                            <div className="w-16 h-16 bg-gray-700 rounded-lg overflow-hidden">
                              {inquiry.products?.images?.[0] ? (
                                <img
                                  src={inquiry.products.images[0]}
                                  alt={inquiry.products.name || 'Product'}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <Package className="h-8 w-8 text-gray-400" />
                                </div>
                              )}
                            </div>

                            <div className="flex-1">
                              <h4 className="text-lg font-semibold text-white mb-1">
                                {inquiry.products?.brand || 'Unknown Brand'} {inquiry.products?.model || ''}
                              </h4>
                              <p className="text-gray-400 text-sm mb-2">
                                {inquiry.products?.name || 'Product information unavailable'}
                              </p>
                              <div className="flex items-center space-x-4 text-sm text-gray-300">
                                <span>Quantity: {inquiry.quantity}</span>
                                <span>•</span>
                                <span>Type: {inquiry.type === 'regular' ? 'Regular' : 'Bulk Order'}</span>
                                <span>•</span>
                                <span className="flex items-center space-x-1">
                                  <Calendar className="h-4 w-4" />
                                  <span>{formatDate(inquiry.created_at)}</span>
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="text-right">
                            <div className="text-xl font-bold text-white mb-2">
                              {inquiry.products?.price ? formatPrice(inquiry.products.price * inquiry.quantity) : 'Price unavailable'}
                            </div>
                            <div className={`inline-flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(inquiry.status)}`}>
                              {getStatusIcon(inquiry.status)}
                              <span className="capitalize">{inquiry.status.replace('_', ' ')}</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center justify-between pt-4 border-t border-gray-700/50">
                          <div className="text-sm text-gray-400">
                            Inquiry ID: {inquiry.id.slice(0, 8)}...
                          </div>
                          
                          <div className="flex items-center space-x-3">
                            <button
                              onClick={() => router.push(`/user/orders/${inquiry.id}`)}
                              className="flex items-center space-x-1 text-[#f9c1b2] hover:text-[#956358] transition-colors"
                            >
                              <MessageCircle className="h-4 w-4" />
                              <span>Chat</span>
                            </button>
                            
                            <button
                              onClick={() => router.push(`/user/orders/${inquiry.id}`)}
                              className="flex items-center space-x-1 text-gray-400 hover:text-white transition-colors"
                            >
                              <Eye className="h-4 w-4" />
                              <span>View Details</span>
                              <ChevronRight className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
