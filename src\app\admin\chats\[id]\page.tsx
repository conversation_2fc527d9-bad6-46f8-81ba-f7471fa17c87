import { AdminChatDetail } from '@/components/admin/AdminChatDetail';

interface AdminChatDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function AdminChatDetailPage({ params }: AdminChatDetailPageProps) {
  const { id } = await params;
  return <AdminChatDetail inquiryId={id} />;
}

export const metadata = {
  title: 'Chat Detail - Tisha International Admin',
  description: 'View and respond to customer inquiry chat',
};
