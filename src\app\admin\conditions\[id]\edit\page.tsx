'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { ArrowLeft, Save } from 'lucide-react';

interface Condition {
  id: string;
  name: string;
  slug: string;
  description?: string;
  is_active: boolean;
}

export default function EditConditionPage() {
  const router = useRouter();
  const params = useParams();
  const conditionId = params.id as string;
  
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: true
  });

  useEffect(() => {
    if (conditionId) {
      fetchCondition();
    }
  }, [conditionId]);

  const fetchCondition = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/conditions/${conditionId}`);
      if (response.ok) {
        const data = await response.json();
        const condition = data.condition;
        setFormData({
          name: condition.name || '',
          description: condition.description || '',
          is_active: condition.is_active ?? true
        });
      } else {
        console.error('Failed to fetch condition');
        router.push('/admin/conditions');
      }
    } catch (error) {
      console.error('Error fetching condition:', error);
      router.push('/admin/conditions');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      alert('Condition name is required');
      return;
    }

    try {
      setIsLoading(true);

      const response = await fetch(`/api/admin/conditions/${conditionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        alert('Condition updated successfully!');
        router.push(`/admin/conditions/${conditionId}`);
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error('Error updating condition:', error);
      alert('Failed to update condition. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#956358]"></div>
        <p className="ml-3 text-gray-600">Loading condition...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => router.back()}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <ArrowLeft className="h-5 w-5 text-gray-600" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Condition</h1>
          <p className="text-gray-600">Update condition information</p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-white shadow-lg rounded-lg border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-6">Condition Information</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Condition Name */}
            <div className="md:col-span-2">
              <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                Condition Name *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-[#956358] focus:border-transparent"
                placeholder="Enter condition name (e.g., New, Used, Refurbished)"
              />
            </div>

            {/* Description */}
            <div className="md:col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-2 focus:ring-[#956358] focus:border-transparent"
                placeholder="Enter condition description"
              />
              <p className="mt-1 text-sm text-gray-500">
                Describe what this condition means for products
              </p>
            </div>

            {/* Status */}
            <div className="md:col-span-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-[#956358] focus:ring-[#956358] border-gray-300 rounded"
                />
                <span className="ml-2 text-sm font-medium text-gray-700">
                  Active (condition will be available for selection)
                </span>
              </label>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="bg-white shadow-lg rounded-lg border border-gray-200 p-6">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="text-lg font-semibold text-gray-900">Ready to update condition?</h4>
              <p className="text-sm text-gray-600 mt-1">Review your changes before updating the condition</p>
            </div>
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="px-6 py-3 border-2 border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading}
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#956358] to-[#f9c1b2] text-white font-medium rounded-lg hover:from-[#956358]/90 hover:to-[#f9c1b2]/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#956358] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Update Condition
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
