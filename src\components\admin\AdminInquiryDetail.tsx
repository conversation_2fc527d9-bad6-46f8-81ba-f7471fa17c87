'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChatModal } from '@/components/admin/ChatModal';
import {
  ArrowLeft,
  Package,
  Calendar,
  User,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Phone,
  Mail,
  MapPin,
  Hash,
  ShoppingCart,
  Save,
  MessageCircle,
  TrendingUp
} from 'lucide-react';

interface Inquiry {
  id: string;
  product_id: string;
  type: 'regular' | 'bulk' | 'hot';
  quantity: number;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'accepted' | 'rejected';
  contact_details: {
    fullName: string;
    email: string;
    phone: string;
    whatsappNumber: string;
    address: string;
    companyName?: string;
  };
  created_at: string;
  updated_at: string;
  product: {
    id: string;
    name: string;
    brand: string;
    model: string;
    price: number;
    images: string[];
    description?: string;
    specifications?: Record<string, unknown>;
  };
}

interface AdminInquiryDetailProps {
  inquiryId: string;
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  in_progress: 'bg-blue-100 text-blue-800 border-blue-200',
  completed: 'bg-green-100 text-green-800 border-green-200',
  cancelled: 'bg-red-100 text-red-800 border-red-200',
  accepted: 'bg-green-100 text-green-800 border-green-200',
  rejected: 'bg-red-100 text-red-800 border-red-200',
};

const statusIcons = {
  pending: Clock,
  in_progress: TrendingUp,
  completed: CheckCircle,
  cancelled: XCircle,
  accepted: CheckCircle,
  rejected: XCircle,
};

export function AdminInquiryDetail({ inquiryId }: AdminInquiryDetailProps) {
  const router = useRouter();
  const { admin, checkPermission } = useAdminAuth();
  const [inquiry, setInquiry] = useState<Inquiry | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);
  const [newStatus, setNewStatus] = useState<string>('');
  const [isChatModalOpen, setIsChatModalOpen] = useState(false);

  // Fetch inquiry details
  useEffect(() => {
    const fetchInquiry = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/admin/inquiries/${inquiryId}`, {
          credentials: 'include',
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch inquiry: ${response.statusText}`);
        }

        const data = await response.json();
        setInquiry(data.inquiry);
        setNewStatus(data.inquiry.status);
      } catch (err) {
        console.error('Error fetching inquiry:', err);
        setError(err instanceof Error ? err.message : 'Failed to load inquiry');
      } finally {
        setIsLoading(false);
      }
    };

    fetchInquiry();
  }, [inquiryId]);

  const handleStatusUpdate = async () => {
    if (!checkPermission('inquiries', 'update') || !inquiry) {
      alert('You do not have permission to update inquiry status');
      return;
    }

    try {
      setIsUpdatingStatus(true);
      const response = await fetch(`/api/admin/inquiries/${inquiryId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        setInquiry(prev => prev ? { ...prev, status: newStatus as any, updated_at: new Date().toISOString() } : null);
      } else {
        console.error('Failed to update status');
        alert('Failed to update status');
      }
    } catch (error) {
      console.error('Error updating status:', error);
      alert('Error updating status');
    } finally {
      setIsUpdatingStatus(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#956358] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading inquiry details...</p>
        </div>
      </div>
    );
  }

  if (error || !inquiry) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 mb-4">{error || 'Inquiry not found'}</div>
          <Button onClick={() => router.back()}>Go Back</Button>
        </div>
      </div>
    );
  }

  const StatusIcon = statusIcons[inquiry.status];

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Inquiries
          </Button>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mb-2">Inquiry Details</h1>
                <p className="text-gray-600">
                  Inquiry ID: {inquiry.id.slice(0, 8)}... • Customer: {inquiry.contact_details.fullName}
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <Button
                  onClick={() => setIsChatModalOpen(true)}
                  className="bg-gradient-to-r from-[#956358] to-[#f9c1b2] hover:shadow-lg hover:shadow-[#956358]/25"
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Open Chat
                </Button>
              </div>
            </div>
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {/* Inquiry Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Status and Basic Info */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Inquiry Information</h3>
                <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-sm font-medium border ${statusColors[inquiry.status]}`}>
                  <StatusIcon className="h-4 w-4" />
                  <span className="capitalize">{inquiry.status.replace('_', ' ')}</span>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Hash className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">Inquiry ID</div>
                      <div className="font-medium text-gray-900">{inquiry.id}</div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <ShoppingCart className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">Type & Quantity</div>
                      <div className="font-medium text-gray-900">
                        {inquiry.type === 'bulk' ? 'Bulk Order' : inquiry.type === 'hot' ? 'Hot' : 'Regular'} • Qty: {inquiry.quantity}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">Created</div>
                      <div className="font-medium text-gray-900">{new Date(inquiry.created_at).toLocaleDateString()}</div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Clock className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">Last Updated</div>
                      <div className="font-medium text-gray-900">{new Date(inquiry.updated_at).toLocaleDateString()}</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status Update Section */}
              {checkPermission('inquiries', 'update') && (
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h4 className="text-lg font-medium text-gray-900 mb-4">Update Status</h4>
                  <div className="flex items-center space-x-4">
                    <select
                      value={newStatus}
                      onChange={(e) => setNewStatus(e.target.value)}
                      className="px-3 py-2 border border-gray-300 rounded-lg text-gray-900 bg-white focus:ring-2 focus:ring-[#956358] focus:border-[#956358]"
                    >
                      <option value="pending">Pending</option>
                      <option value="in_progress">In Progress</option>
                      <option value="accepted">Accepted</option>
                      <option value="rejected">Rejected</option>
                      <option value="completed">Completed</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                    <Button
                      onClick={handleStatusUpdate}
                      disabled={isUpdatingStatus || newStatus === inquiry.status}
                      className="bg-[#956358] hover:bg-[#7d5249]"
                    >
                      <Save className="h-4 w-4 mr-2" />
                      {isUpdatingStatus ? 'Updating...' : 'Update Status'}
                    </Button>
                  </div>
                </div>
              )}
            </Card>

            {/* Customer Information */}
            <Card className="p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Customer Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <User className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">Full Name</div>
                      <div className="font-medium text-gray-900">{inquiry.contact_details.fullName}</div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">Email</div>
                      <div className="font-medium text-gray-900">{inquiry.contact_details.email}</div>
                    </div>
                  </div>

                  {inquiry.contact_details.companyName && (
                    <div className="flex items-center space-x-3">
                      <Package className="h-5 w-5 text-gray-400" />
                      <div>
                        <div className="text-sm text-gray-500">Company</div>
                        <div className="font-medium text-gray-900">{inquiry.contact_details.companyName}</div>
                      </div>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-500">Phone</div>
                      <div className="font-medium text-gray-900">{inquiry.contact_details.phone}</div>
                    </div>
                  </div>

                  {inquiry.contact_details.whatsappNumber && (
                    <div className="flex items-center space-x-3">
                      <Phone className="h-5 w-5 text-gray-400" />
                      <div>
                        <div className="text-sm text-gray-500">WhatsApp</div>
                        <div className="font-medium text-gray-900">{inquiry.contact_details.whatsappNumber}</div>
                      </div>
                    </div>
                  )}

                  {inquiry.contact_details.address && (
                    <div className="flex items-center space-x-3">
                      <MapPin className="h-5 w-5 text-gray-400" />
                      <div>
                        <div className="text-sm text-gray-500">Address</div>
                        <div className="font-medium text-gray-900">{inquiry.contact_details.address}</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Card>
          </div>

          {/* Product Information */}
          {inquiry.product && (
            <div className="lg:col-span-1">
              <Card className="p-6 sticky top-24">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">Product Details</h3>
                
                <div className="space-y-4">
                  <div className="aspect-square relative rounded-lg overflow-hidden bg-gray-100">
                    <Image
                      src={inquiry.product.images?.[0] || '/api/placeholder-image'}
                      alt={inquiry.product.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-gray-900 text-lg">{inquiry.product.name}</h4>
                    <p className="text-gray-600">{inquiry.product.brand} {inquiry.product.model}</p>
                    <p className="text-2xl font-bold text-[#956358] mt-2">
                      {formatPrice(inquiry.product.price * inquiry.quantity)}
                    </p>
                    <p className="text-sm text-gray-500">
                      {formatPrice(inquiry.product.price)} × {inquiry.quantity}
                    </p>
                  </div>

                  {inquiry.product.description && (
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2">Description</h5>
                      <p className="text-gray-600 text-sm">{inquiry.product.description}</p>
                    </div>
                  )}

                  {inquiry.product.specifications && Object.keys(inquiry.product.specifications).length > 0 && (
                    <div>
                      <h5 className="font-medium text-gray-900 mb-2">Specifications</h5>
                      <div className="space-y-1">
                        {Object.entries(inquiry.product.specifications).map(([key, value]) => (
                          <div key={key} className="flex justify-between text-sm">
                            <span className="text-gray-600">{key}:</span>
                            <span className="text-gray-900">{String(value)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          )}
        </div>
      </div>

      {/* Chat Modal */}
      <ChatModal
        isOpen={isChatModalOpen}
        onClose={() => setIsChatModalOpen(false)}
        inquiryId={inquiryId}
        customerName={inquiry.contact_details.fullName}
      />
    </div>
  );
}
