import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    // This is a development-only endpoint for debugging admin users
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    const supabase = createSupabaseAdminClient();

    // Check if admin_users table exists and get all records
    const { data: adminUsers, error: adminError } = await supabase
      .from('admin_users')
      .select('id, email, full_name, is_active, role_id, created_at');

    // Check if roles table exists and get all records
    const { data: roles, error: rolesError } = await supabase
      .from('roles')
      .select('*');

    return NextResponse.json({
      adminUsers: {
        data: adminUsers,
        error: adminError
      },
      roles: {
        data: roles,
        error: rolesError
      },
      message: 'Debug info for admin authentication'
    });

  } catch (error) {
    console.error('Debug error:', error);
    return NextResponse.json(
      { error: 'Debug failed', details: error },
      { status: 500 }
    );
  }
}
