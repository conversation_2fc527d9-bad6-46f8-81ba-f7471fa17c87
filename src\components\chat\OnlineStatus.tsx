import React from 'react';
import { Circle } from 'lucide-react';

interface OnlineStatusProps {
  isOnline: boolean;
  isOtherUserOnline: boolean;
  otherUserType?: 'user' | 'admin' | null;
  isAdmin?: boolean;
}

export function OnlineStatus({ isOnline, isOtherUserOnline, otherUserType, isAdmin = false }: OnlineStatusProps) {
  const getOtherUserLabel = () => {
    if (!otherUserType) return 'Other user';
    return isAdmin ? 'Customer' : 'Support Team';
  };

  return (
    <div className="flex items-center justify-between px-4 py-2 bg-[#0f1629] border-b border-gray-700">
      {/* Current user status */}
      <div className="flex items-center space-x-2">
        <div className="relative">
          <Circle 
            className={`h-3 w-3 ${isOnline ? 'text-green-500 fill-green-500' : 'text-gray-500 fill-gray-500'}`} 
          />
          {isOnline && (
            <div className="absolute inset-0 h-3 w-3 bg-green-500 rounded-full animate-ping opacity-75"></div>
          )}
        </div>
        <span className="text-xs text-gray-400">
          {isOnline ? 'Online' : 'Offline'}
        </span>
      </div>

      {/* Other user status */}
      <div className="flex items-center space-x-2">
        <span className="text-xs text-gray-400">
          {getOtherUserLabel()}
        </span>
        <div className="relative">
          <Circle 
            className={`h-3 w-3 ${isOtherUserOnline ? 'text-green-500 fill-green-500' : 'text-gray-500 fill-gray-500'}`} 
          />
          {isOtherUserOnline && (
            <div className="absolute inset-0 h-3 w-3 bg-green-500 rounded-full animate-ping opacity-75"></div>
          )}
        </div>
        <span className="text-xs text-gray-400">
          {isOtherUserOnline ? 'Online' : 'Offline'}
        </span>
      </div>
    </div>
  );
}
