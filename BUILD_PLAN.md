# Tisha International - E-commerce Build Plan

## Project Overview
**Project Name:** Tisha International  
**Type:** E-commerce website for refurbished laptops and desktops  
**Tech Stack:** Next.js, Tailwind CSS, Supabase (Database & Auth)

## Core Features

### 1. User-Facing Pages
- **Home Page** - Landing page with featured products and categories
- **Category Page** - Product listings with filters and search
- **Services** - Company services information
- **Become Dealer** - Dealer registration and information
- **About Us** - Company information and story
- **Contact Us** - Contact form and company details
- **Single Product Page** - Detailed product view with specifications
- **Order History** - User's inquiry history with chat feature

### 2. Product Specifications Structure
- **Basic Info:** Brand, Model Name, Screen Size, Color, Storage, RAM
- **Technical:** Operating System, Graphics Card
- **Features:** Special Features (bullet points)
- **Additional:** Webcam, Fingerprint Sensor, Keyboard, Pointer Device
- **Description:** Collapsible accordion with detailed points
- **Related Products:** Similar product recommendations

### 3. Inquiry System
- **Regular Inquiry** - Single product inquiry with quantity selection
- **Bulk Order** - Special bulk order inquiries
- **Personal Information Collection** - Name, address, WhatsApp number
- **Status Tracking** - Pending, In Progress, Accepted, Rejected, Completed

### 4. Chat System
- **User-Admin Communication** - Real-time messaging per inquiry
- **Media Support** - Images, links, and text messages
- **Admin Response** - Admin can respond from admin panel

### 5. Admin Panel Features
- **Authentication** - Separate admin login system
- **Role Management** - Different permission levels
- **Product Management** - Add, edit, delete products
- **Inventory Management** - Track stock levels, low stock alerts, inventory history
- **Inquiry Management** - View and update inquiry status
- **User Management** - Manage user accounts
- **Chat Management** - Respond to user inquiries
- **Email Notifications** - Automated emails for order updates and chat messages

## Database Schema Design

### Users Table
```sql
- id (UUID, Primary Key)
- email (String, Unique)
- full_name (String)
- phone (String)
- address (Text)
- created_at (Timestamp)
- updated_at (Timestamp)
```

### Admin Users Table
```sql
- id (UUID, Primary Key)
- email (String, Unique)
- password_hash (String)
- role_id (UUID, Foreign Key)
- full_name (String)
- is_active (Boolean)
- created_at (Timestamp)
```

### Roles Table
```sql
- id (UUID, Primary Key)
- name (String)
- permissions (JSONB)
- created_at (Timestamp)
```

### Categories Table
```sql
- id (UUID, Primary Key)
- name (String)
- slug (String, Unique)
- description (Text)
- image_url (String)
- is_active (Boolean)
```

### Products Table
```sql
- id (UUID, Primary Key)
- category_id (UUID, Foreign Key)
- name (String)
- slug (String, Unique)
- brand (String)
- model_name (String)
- screen_size (String)
- color (String)
- storage (String)
- ram (String)
- operating_system (String)
- graphics_card (String)
- special_features (JSONB)
- additional_features (JSONB)
- description (Text)
- images (JSONB)
- price (Decimal)
- stock_quantity (Integer)
- low_stock_threshold (Integer)
- is_active (Boolean)
- created_at (Timestamp)
- updated_at (Timestamp)
```

### Inventory Table
```sql
- id (UUID, Primary Key)
- product_id (UUID, Foreign Key)
- transaction_type (Enum: 'stock_in', 'stock_out', 'adjustment')
- quantity_change (Integer)
- previous_quantity (Integer)
- new_quantity (Integer)
- reason (String)
- admin_id (UUID, Foreign Key)
- created_at (Timestamp)
```

### Email Notifications Table
```sql
- id (UUID, Primary Key)
- recipient_email (String)
- subject (String)
- body (Text)
- template_type (Enum: 'inquiry_update', 'chat_message', 'low_stock_alert')
- related_id (UUID) -- inquiry_id or product_id
- status (Enum: 'pending', 'sent', 'failed')
- sent_at (Timestamp)
- created_at (Timestamp)
```

### Inquiries Table
```sql
- id (UUID, Primary Key)
- user_id (UUID, Foreign Key)
- product_id (UUID, Foreign Key)
- inquiry_type (Enum: 'regular', 'bulk')
- quantity (Integer)
- status (Enum: 'pending', 'in_progress', 'accepted', 'rejected', 'completed')
- user_details (JSONB)
- created_at (Timestamp)
- updated_at (Timestamp)
```

### Chat Messages Table
```sql
- id (UUID, Primary Key)
- inquiry_id (UUID, Foreign Key)
- sender_type (Enum: 'user', 'admin')
- sender_id (UUID)
- message_type (Enum: 'text', 'image', 'link')
- content (Text)
- created_at (Timestamp)
```

## Design System & UI/UX Specifications

### Color Palette (Based on Logo)
```css
:root {
  /* Primary Brand Colors */
  --primary-gradient-dark: #956358;
  --primary-gradient-light: #f9c1b2;
  --background-primary: #03132a;
  --text-primary: #ffffff;

  /* Secondary Colors */
  --background-secondary: #0a1e3d;
  --background-card: #1a2b4a;
  --accent-gold: #d4af37;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;

  /* Neutral Colors */
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
}
```

### Typography System
```css
/* Font Families */
--font-primary: 'Inter', sans-serif; /* Clean, modern for body text */
--font-heading: 'Poppins', sans-serif; /* Bold, elegant for headings */
--font-mono: 'JetBrains Mono', monospace; /* For code/specs */

/* Font Sizes */
--text-xs: 0.75rem;    /* 12px */
--text-sm: 0.875rem;   /* 14px */
--text-base: 1rem;     /* 16px */
--text-lg: 1.125rem;   /* 18px */
--text-xl: 1.25rem;    /* 20px */
--text-2xl: 1.5rem;    /* 24px */
--text-3xl: 1.875rem;  /* 30px */
--text-4xl: 2.25rem;   /* 36px */
--text-5xl: 3rem;      /* 48px */
```

### Animation & Motion Design
```css
/* Smooth Transitions */
--transition-fast: 150ms ease-in-out;
--transition-normal: 300ms ease-in-out;
--transition-slow: 500ms ease-in-out;

/* Easing Functions */
--ease-in-out-cubic: cubic-bezier(0.4, 0, 0.2, 1);
--ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
--ease-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);

/* Shadows */
--shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
--shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
--shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
--shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
--shadow-glow: 0 0 20px rgba(249, 193, 178, 0.3);
```

### Component Design Specifications

#### Buttons
- **Primary Button**: Gradient background (#956358 to #f9c1b2), white text, smooth hover animations
- **Secondary Button**: Transparent with gradient border, hover fill effect
- **Ghost Button**: Text only with underline animation on hover

#### Cards
- **Product Cards**: Dark background (#1a2b4a), subtle glow on hover, smooth scale animation
- **Info Cards**: Glass morphism effect with backdrop blur
- **Admin Cards**: Clean white background with subtle shadows

#### Navigation
- **Header**: Fixed position with backdrop blur, gradient accent line
- **Sidebar**: Smooth slide animations, active state indicators
- **Breadcrumbs**: Elegant separator with smooth transitions

#### Forms
- **Input Fields**: Dark background with gradient focus border
- **Validation**: Smooth error state transitions with color changes
- **File Upload**: Drag & drop with visual feedback animations

### Animation Specifications

#### Page Transitions
```typescript
const pageVariants = {
  initial: { opacity: 0, y: 20 },
  in: { opacity: 1, y: 0 },
  out: { opacity: 0, y: -20 }
};

const pageTransition = {
  type: "tween",
  ease: "anticipate",
  duration: 0.5
};
```

#### Micro-interactions
- **Hover Effects**: Subtle scale (1.02x), glow, and color transitions
- **Click Feedback**: Quick scale down (0.98x) then back to normal
- **Loading States**: Elegant skeleton loaders with shimmer effect
- **Success States**: Smooth checkmark animations with bounce

#### Scroll Animations
- **Parallax Effects**: Subtle background movement on hero sections
- **Fade In**: Elements appear as they enter viewport
- **Stagger Animations**: Product grids animate in sequence

### Responsive Design Breakpoints
```css
/* Mobile First Approach */
--breakpoint-sm: 640px;   /* Small devices */
--breakpoint-md: 768px;   /* Medium devices */
--breakpoint-lg: 1024px;  /* Large devices */
--breakpoint-xl: 1280px;  /* Extra large devices */
--breakpoint-2xl: 1536px; /* 2X large devices */
```

### Accessibility Features
- **High Contrast Mode**: Alternative color scheme for better visibility
- **Focus Indicators**: Clear, visible focus states with gradient borders
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Keyboard Navigation**: Full keyboard accessibility for all interactions

## Technical Implementation Plan

### Phase 1: Project Setup & Foundation (Week 1)
1. **Initialize Next.js Project**
   - Set up Next.js with TypeScript
   - Configure Tailwind CSS
   - Set up project structure and folders

2. **Supabase Configuration**
   - Create Supabase project
   - Set up database tables
   - Configure authentication
   - Set up Row Level Security (RLS) policies

3. **Basic Layout & Navigation**
   - Create header with navigation
   - Set up footer
   - Implement responsive design foundation

### Phase 2: Core Pages & Product System (Week 2-3)
1. **Static Pages**
   - Home page with hero section
   - About Us page
   - Services page
   - Contact Us page
   - Become Dealer page

2. **Product System**
   - Category page with filtering
   - Single product page with specifications
   - Product search functionality
   - Related products section

### Phase 3: User System & Inquiries (Week 4)
1. **User Authentication**
   - User registration/login
   - Profile management
   - Order history page

2. **Inquiry System**
   - Product inquiry form
   - Bulk order functionality
   - Inquiry submission flow
   - Success page implementation

### Phase 4: Admin Panel (Week 5-6)
1. **Admin Authentication & Roles**
   - Admin login system
   - Role-based access control
   - Permission management

2. **Admin Features**
   - Product management (CRUD)
   - Inquiry management
   - User management
   - Dashboard with analytics

### Phase 5: Chat System (Week 7)
1. **Real-time Chat**
   - Chat interface for inquiries
   - Real-time messaging with Supabase
   - Media upload functionality
   - Admin chat management

### Phase 6: Testing & Deployment (Week 8)
1. **Testing**
   - Unit tests for components
   - Integration tests for API routes
   - User acceptance testing

2. **Deployment**
   - Production deployment setup
   - Environment configuration
   - Performance optimization

## File Structure
```
tisha-international/
├── components/
│   ├── ui/
│   ├── layout/
│   ├── product/
│   ├── admin/
│   └── chat/
├── pages/
│   ├── api/
│   ├── admin/
│   ├── products/
│   └── user/
├── lib/
│   ├── supabase/
│   ├── utils/
│   └── types/
├── styles/
├── public/
└── hooks/
```

## Key Dependencies
- Next.js 14
- TypeScript
- Tailwind CSS
- Supabase JS Client
- React Hook Form
- Zustand (State Management)
- React Query (Data Fetching)
- Lucide React (Icons)
- Next Image Optimization

## Security Considerations
- Row Level Security (RLS) policies
- Input validation and sanitization
- Secure file upload handling
- Rate limiting for API endpoints
- CSRF protection
- Secure admin authentication

## Performance Optimizations
- Image optimization with Next.js
- Lazy loading for product images
- Pagination for product listings
- Caching strategies
- Database query optimization

## Detailed Implementation Steps

### Step 1: Project Initialization
```bash
npx create-next-app@latest tisha-international --typescript --tailwind --eslint --app
cd tisha-international
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs
npm install react-hook-form @hookform/resolvers zod
npm install zustand @tanstack/react-query
npm install lucide-react clsx tailwind-merge
npm install @radix-ui/react-accordion @radix-ui/react-dialog
npm install @radix-ui/react-dropdown-menu @radix-ui/react-tabs
npm install framer-motion @next/font
npm install nodemailer @types/nodemailer
npm install react-hot-toast sonner
```

### Step 2: Environment Setup
Create `.env.local`:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password
FROM_EMAIL=<EMAIL>

# Site Configuration
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### Step 3: Supabase Database Schema
```sql
-- Enable RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE inquiries ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own data" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Public can view active products" ON products FOR SELECT USING (is_active = true);
CREATE POLICY "Users can view own inquiries" ON inquiries FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view own chat messages" ON chat_messages FOR SELECT USING (
  EXISTS (SELECT 1 FROM inquiries WHERE inquiries.id = chat_messages.inquiry_id AND inquiries.user_id = auth.uid())
);
```

### Step 4: Core Components Structure
```
components/
├── ui/
│   ├── Button.tsx
│   ├── Input.tsx
│   ├── Card.tsx
│   ├── Badge.tsx
│   ├── Accordion.tsx
│   └── Dialog.tsx
├── layout/
│   ├── Header.tsx
│   ├── Footer.tsx
│   ├── Navigation.tsx
│   └── Layout.tsx
├── product/
│   ├── ProductCard.tsx
│   ├── ProductGrid.tsx
│   ├── ProductFilter.tsx
│   ├── ProductSpecifications.tsx
│   ├── ProductDescription.tsx
│   ├── RelatedProducts.tsx
│   └── InquiryForm.tsx
├── admin/
│   ├── AdminLayout.tsx
│   ├── ProductManager.tsx
│   ├── InventoryManager.tsx
│   ├── InquiryManager.tsx
│   ├── UserManager.tsx
│   ├── RoleManager.tsx
│   ├── EmailTemplates.tsx
│   └── Dashboard.tsx
└── chat/
    ├── ChatWindow.tsx
    ├── MessageList.tsx
    ├── MessageInput.tsx
    └── FileUpload.tsx
```

### Step 5: API Routes Structure
```
app/api/
├── auth/
│   ├── login/route.ts
│   ├── register/route.ts
│   └── logout/route.ts
├── products/
│   ├── route.ts
│   ├── [id]/route.ts
│   ├── categories/route.ts
│   └── search/route.ts
├── inquiries/
│   ├── route.ts
│   ├── [id]/route.ts
│   └── bulk/route.ts
├── admin/
│   ├── products/route.ts
│   ├── inventory/route.ts
│   ├── inquiries/route.ts
│   ├── users/route.ts
│   └── roles/route.ts
├── email/
│   ├── send/route.ts
│   └── templates/route.ts
└── chat/
    ├── messages/route.ts
    └── upload/route.ts
```

### Step 6: Page Structure
```
app/
├── page.tsx (Home)
├── about/page.tsx
├── services/page.tsx
├── contact/page.tsx
├── become-dealer/page.tsx
├── products/
│   ├── page.tsx (Category listing)
│   ├── [category]/page.tsx
│   └── [category]/[slug]/page.tsx (Single product)
├── user/
│   ├── profile/page.tsx
│   ├── orders/page.tsx
│   └── orders/[id]/page.tsx (Order details with chat)
└── admin/
    ├── page.tsx (Dashboard)
    ├── products/page.tsx
    ├── inquiries/page.tsx
    ├── users/page.tsx
    └── roles/page.tsx
```

### Step 7: State Management Setup
```typescript
// stores/useAuthStore.ts
interface AuthState {
  user: User | null;
  isLoading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
}

// stores/useProductStore.ts
interface ProductState {
  products: Product[];
  categories: Category[];
  filters: ProductFilters;
  setFilters: (filters: ProductFilters) => void;
}

// stores/useInquiryStore.ts
interface InquiryState {
  inquiries: Inquiry[];
  currentInquiry: Inquiry | null;
  createInquiry: (inquiry: CreateInquiryData) => Promise<void>;
}
```

### Step 8: Type Definitions
```typescript
// lib/types/index.ts
export interface Product {
  id: string;
  name: string;
  slug: string;
  brand: string;
  model_name: string;
  screen_size: string;
  color: string;
  storage: string;
  ram: string;
  operating_system: string;
  graphics_card: string;
  special_features: string[];
  additional_features: {
    webcam: boolean;
    fingerprint_sensor: boolean;
    keyboard: string;
    pointer_device: string;
  };
  description: string;
  images: string[];
  price: number;
  category_id: string;
  is_active: boolean;
}

export interface Inquiry {
  id: string;
  user_id: string;
  product_id: string;
  inquiry_type: 'regular' | 'bulk';
  quantity: number;
  status: 'pending' | 'in_progress' | 'accepted' | 'rejected' | 'completed';
  user_details: {
    full_name: string;
    email: string;
    phone: string;
    address: string;
  };
  created_at: string;
  updated_at: string;
}
```

### Step 9: User Flow Implementation

#### User Journey Flow:
1. **Landing** → Home page with featured products
2. **Browse** → Category pages with filters
3. **Select** → Single product page with specifications
4. **Inquire** → Fill inquiry form with quantity selection
5. **Details** → Personal information and address form
6. **Submit** → Inquiry success page
7. **Track** → Order history with chat feature

#### Key User Features:
- Product browsing with advanced filters
- Detailed product specifications with accordion
- Inquiry form with regular/bulk options
- Personal information collection
- Real-time chat with admin
- Order history and status tracking

### Step 10: Admin Panel Implementation

#### Admin Features:
- **Dashboard** - Overview of inquiries, products, users
- **Product Management** - CRUD operations with image upload
- **Inquiry Management** - Status updates and bulk actions
- **User Management** - View and manage user accounts
- **Role Management** - Create roles with specific permissions
- **Chat Management** - Respond to user inquiries

#### Permission System:
```typescript
interface Permission {
  resource: 'products' | 'inquiries' | 'users' | 'roles' | 'chat';
  actions: ('create' | 'read' | 'update' | 'delete')[];
}

interface Role {
  id: string;
  name: string;
  permissions: Permission[];
}
```

### Step 11: Chat System Implementation

#### Real-time Features:
- WebSocket connection via Supabase Realtime
- Message types: text, image, link
- File upload for images
- Message status indicators
- Admin notification system

#### Chat Components:
```typescript
// Chat message structure
interface ChatMessage {
  id: string;
  inquiry_id: string;
  sender_type: 'user' | 'admin';
  sender_id: string;
  message_type: 'text' | 'image' | 'link';
  content: string;
  created_at: string;
}
```

### Step 12: Testing Strategy

#### Unit Tests:
- Component testing with Jest and React Testing Library
- API route testing
- Utility function testing
- State management testing

#### Integration Tests:
- User flow testing
- Admin panel functionality
- Chat system testing
- Database operations

#### E2E Tests:
- Complete user journey
- Admin workflows
- Cross-browser compatibility

### Step 13: Deployment Checklist

#### Pre-deployment:
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] RLS policies tested
- [ ] Image optimization configured
- [ ] SEO meta tags added
- [ ] Performance optimization completed

#### Production Setup:
- [ ] Vercel deployment configured
- [ ] Supabase production database
- [ ] CDN for image delivery
- [ ] Error monitoring (Sentry)
- [ ] Analytics setup (Google Analytics)

### Step 14: Security Implementation

#### Authentication Security:
- JWT token validation
- Secure session management
- Password hashing (handled by Supabase)
- Rate limiting on auth endpoints

#### Data Security:
- Input validation and sanitization
- SQL injection prevention (Supabase handles)
- XSS protection
- CSRF tokens for forms
- File upload validation

#### Admin Security:
- Role-based access control
- Admin session timeout
- Audit logging for admin actions
- Secure admin routes

### Step 15: Performance Optimization

#### Frontend Optimization:
- Next.js Image component for optimized images
- Lazy loading for product grids
- Code splitting for admin panel
- Caching strategies for API calls

#### Database Optimization:
- Proper indexing on frequently queried columns
- Query optimization for product searches
- Pagination for large datasets
- Connection pooling

#### SEO Optimization:
- Dynamic meta tags for products
- Structured data for products
- Sitemap generation
- Open Graph tags for social sharing

## Development Timeline

### Week 1: Foundation
- Project setup and configuration
- Database schema creation
- Basic layout and navigation
- Authentication setup

### Week 2: Core Features
- Product listing and filtering
- Single product page
- User registration and profile
- Basic inquiry system

### Week 3: Advanced Features
- Admin panel foundation
- Product management
- Inquiry management
- Role and permission system

### Week 4: Chat & Polish
- Real-time chat implementation
- UI/UX improvements
- Mobile responsiveness
- Testing and bug fixes

### Week 5: Deployment & Launch
- Production deployment
- Performance optimization
- Security audit
- Go-live preparation

## Maintenance & Future Enhancements

### Immediate Post-Launch:
- Monitor system performance
- User feedback collection
- Bug fixes and improvements
- Analytics review

### Future Features:
- Payment gateway integration
- Inventory management
- Email notifications
- Mobile app development
- Advanced analytics dashboard

This comprehensive build plan provides a complete roadmap for developing the Tisha International e-commerce platform with all requested features, security considerations, and scalability in mind.
