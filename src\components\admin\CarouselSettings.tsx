'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Settings, Clock, Save, RotateCcw } from 'lucide-react';

interface CarouselSettingsProps {
  onSettingsChange?: (settings: { delay: number; enabled: boolean }) => void;
}

export default function CarouselSettings({ onSettingsChange }: CarouselSettingsProps) {
  const [delay, setDelay] = useState(5000); // Default 5 seconds
  const [enabled, setEnabled] = useState(true);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState('');

  // Load current settings
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/settings/carousel');
      if (response.ok) {
        const data = await response.json();
        setDelay(data.delay);
        setEnabled(data.enabled);
      }
    } catch (error) {
      console.error('Error loading carousel settings:', error);
      setMessage('Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    setSaving(true);
    setMessage('');
    
    try {
      const response = await fetch('/api/admin/settings/carousel', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ delay, enabled }),
      });

      if (response.ok) {
        setMessage('Settings saved successfully!');
        onSettingsChange?.({ delay, enabled });
        setTimeout(() => setMessage(''), 3000);
      } else {
        const error = await response.json();
        setMessage(error.error || 'Failed to save settings');
      }
    } catch (error) {
      console.error('Error saving carousel settings:', error);
      setMessage('Failed to save settings');
    } finally {
      setSaving(false);
    }
  };

  const resetToDefault = () => {
    setDelay(5000);
    setEnabled(true);
    setMessage('Reset to default values');
    setTimeout(() => setMessage(''), 2000);
  };

  const delayInSeconds = delay / 1000;

  return (
    <Card className="p-6 bg-gradient-card border-shadow-grey/20">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3">
          <div className="p-2 bg-gradient-primary rounded-lg">
            <Settings className="w-5 h-5 text-white" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-soft-white">Carousel Settings</h3>
            <p className="text-sm text-muted-grey">Configure automatic carousel behavior</p>
          </div>
        </div>

        {/* Settings Form */}
        <div className="space-y-4">
          {/* Enable/Disable Auto-play */}
          <div className="flex items-center justify-between p-4 bg-shadow-grey/10 rounded-lg border border-shadow-grey/20">
            <div className="space-y-1">
              <Label htmlFor="carousel-enabled" className="text-soft-white font-medium">
                Auto-play Carousel
              </Label>
              <p className="text-sm text-muted-grey">
                Enable automatic carousel sliding
              </p>
            </div>
            <Switch
              id="carousel-enabled"
              checked={enabled}
              onCheckedChange={setEnabled}
              disabled={loading}
            />
          </div>

          {/* Delay Setting */}
          <div className="space-y-3">
            <Label htmlFor="carousel-delay" className="text-soft-white font-medium flex items-center gap-2">
              <Clock className="w-4 h-4" />
              Slide Delay
            </Label>
            <div className="space-y-2">
              <div className="flex items-center gap-3">
                <Input
                  id="carousel-delay"
                  type="number"
                  min="1"
                  max="30"
                  step="0.5"
                  value={delayInSeconds}
                  onChange={(e) => setDelay(parseFloat(e.target.value) * 1000)}
                  disabled={loading || !enabled}
                  className="w-24 bg-shadow-grey/20 border-shadow-grey/30 text-soft-white"
                />
                <span className="text-sm text-muted-grey">seconds</span>
              </div>
              <div className="text-xs text-muted-grey">
                Range: 1-30 seconds. Current: {delayInSeconds}s
              </div>
            </div>
          </div>

          {/* Preview */}
          <div className="p-4 bg-shadow-grey/10 rounded-lg border border-shadow-grey/20">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-accent-secondary rounded-full"></div>
              <span className="text-sm font-medium text-soft-white">Preview</span>
            </div>
            <p className="text-sm text-muted-grey">
              {enabled 
                ? `Carousel will automatically change slides every ${delayInSeconds} seconds`
                : 'Carousel auto-play is disabled - manual navigation only'
              }
            </p>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center gap-3 pt-4 border-t border-shadow-grey/20">
          <Button
            onClick={saveSettings}
            disabled={saving || loading}
            className="bg-gradient-primary hover:bg-gradient-primary/90 text-white"
          >
            {saving ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Settings
              </>
            )}
          </Button>
          
          <Button
            onClick={resetToDefault}
            variant="outline"
            disabled={loading || saving}
            className="border-shadow-grey/30 text-soft-white hover:bg-shadow-grey/20"
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset to Default
          </Button>
        </div>

        {/* Status Message */}
        {message && (
          <div className={`p-3 rounded-lg text-sm ${
            message.includes('success') || message.includes('Reset')
              ? 'bg-green-500/20 text-green-300 border border-green-500/30'
              : 'bg-red-500/20 text-red-300 border border-red-500/30'
          }`}>
            {message}
          </div>
        )}
      </div>
    </Card>
  );
}
