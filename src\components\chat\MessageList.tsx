'use client';

import { useEffect, useRef } from 'react';
import Image from 'next/image';
import { User, Users, Link } from 'lucide-react';

interface ChatMessage {
  id: string;
  sender_type: 'user' | 'admin';
  sender_id: string;
  message_type: 'text' | 'image' | 'link';
  content?: string;  // New messages use content
  message?: string;  // Old messages use message
  created_at: string;
  sender_name?: string;
}

interface MessageListProps {
  messages: ChatMessage[];
  currentUserId?: string;
  isAdmin?: boolean;
  customerName?: string;
}

export function MessageList({ messages, currentUserId, isAdmin = false, customerName }: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);



  useEffect(() => {
    // Only scroll if there are messages
    if (messages.length > 0) {
      scrollToBottom();
    }
  }, [messages]);

  const scrollToBottom = () => {
    // Use a more controlled scroll that doesn't affect the page
    if (chatContainerRef.current) {
      // Scroll the chat container to the bottom
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    } else if (messagesEndRef.current) {
      // Fallback to scrollIntoView but with block: 'nearest' to minimize page scroll
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'nearest'
      });
    }
  };

  const formatMessageTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Determine if message is from current user (for positioning)
  const isOwnMessage = (message: ChatMessage) => {
    if (isAdmin) {
      // For admin: their own messages (admin messages) go on the right
      return message.sender_type === 'admin';
    } else {
      // For user: their own messages are user messages
      return message.sender_type === 'user' && message.sender_id === currentUserId;
    }
  };

  // Determine if message is from customer (for admin view styling)
  const isCustomerMessage = (message: ChatMessage) => {
    return isAdmin && message.sender_type === 'user';
  };

  // Get display name for message sender
  const getSenderName = (message: ChatMessage) => {
    if (isAdmin) {
      // Admin perspective
      if (message.sender_type === 'admin') {
        return message.sender_id === currentUserId ? 'You' : 'Support Team';
      } else {
        return customerName || 'Customer';
      }
    } else {
      // User perspective
      if (message.sender_type === 'admin') {
        return 'Support Team';
      } else {
        return 'You';
      }
    }
  };

  if (messages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-3 sm:p-6">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Users className="h-8 w-8 text-gray-400" />
          </div>
          <p className="text-gray-400">No messages yet</p>
          <p className="text-gray-500 text-sm mt-1">
            Start a conversation with our support team
          </p>
        </div>
      </div>
    );
  }

  return (
    <div ref={chatContainerRef} className="flex-1 overflow-y-auto p-3 sm:p-6 space-y-3 sm:space-y-4">
      {messages.map((message) => {
        const isOwn = isOwnMessage(message);
        const isCustomer = isCustomerMessage(message);
        const senderName = getSenderName(message);

        // Determine message styling based on sender type and admin view
        let messageStyle = '';
        if (isAdmin) {
          // Admin view styling
          if (message.sender_type === 'admin') {
            // Support team messages - right side with dark color
            messageStyle = 'bg-[#1a2b4a] text-white border border-gray-700';
          } else {
            // Customer messages - left side with copper color
            messageStyle = 'bg-gradient-to-r from-[#956358] to-[#f9c1b2] text-white';
          }
        } else {
          // User view styling
          if (isOwn) {
            // User's own messages
            messageStyle = 'bg-gradient-to-r from-[#956358] to-[#f9c1b2] text-white';
          } else {
            // Support team messages
            messageStyle = 'bg-[#1a2b4a] text-white border border-gray-700';
          }
        }

        return (
          <div
            key={message.id}
            className={`flex ${isOwn ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[85%] sm:max-w-[70%] rounded-lg p-3 sm:p-4 ${messageStyle}`}
            >
              <div className="flex items-center space-x-2 mb-1">
                {message.sender_type === 'admin' ? (
                  <Users className="h-4 w-4" />
                ) : (
                  <User className="h-4 w-4" />
                )}
                <span className="text-xs font-medium opacity-90">
                  {senderName}
                </span>
                <span className="text-xs opacity-75">
                  {formatMessageTime(message.created_at)}
                </span>
              </div>
            
            <div className="space-y-2">
              {message.message_type === 'text' && (
                <p className="whitespace-pre-wrap">{message.content || message.message}</p>
              )}
              {message.message_type === 'image' && (
                <div>
                  <Image
                    src={message.content || message.message || ''}
                    alt="Shared image"
                    width={300}
                    height={200}
                    className="max-w-full rounded-lg"
                  />
                </div>
              )}
              {message.message_type === 'link' && (
                <div className="flex items-center space-x-2">
                  <Link className="h-4 w-4" />
                  <a
                    href={message.content || message.message || ''}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="underline hover:no-underline"
                  >
                    {message.content || message.message}
                  </a>
                </div>
              )}
              </div>
            </div>
          </div>
        );
      })}
      <div ref={messagesEndRef} />
    </div>
  );
}
