import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';
import { generateSlug } from '@/lib/product-utils';

export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseAdminClient();
    
    // Get all products that don't have slugs or have empty slugs
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('id, name, slug')
      .or('slug.is.null,slug.eq.');

    if (fetchError) {
      console.error('Error fetching products without slugs:', fetchError);
      return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 });
    }

    if (!products || products.length === 0) {
      return NextResponse.json({ 
        message: 'All products already have slugs',
        updated: 0 
      });
    }

    const updates = [];
    const slugMap = new Map();

    // Generate unique slugs for each product
    for (const product of products) {
      let baseSlug = generateSlug(product.name);
      let finalSlug = baseSlug;
      let counter = 1;

      // Ensure slug uniqueness
      while (slugMap.has(finalSlug)) {
        finalSlug = `${baseSlug}-${counter}`;
        counter++;
      }

      // Check if slug already exists in database
      const { data: existingProduct } = await supabase
        .from('products')
        .select('id')
        .eq('slug', finalSlug)
        .neq('id', product.id)
        .single();

      // If slug exists, add counter
      while (existingProduct) {
        finalSlug = `${baseSlug}-${counter}`;
        counter++;
        
        const { data: checkAgain } = await supabase
          .from('products')
          .select('id')
          .eq('slug', finalSlug)
          .neq('id', product.id)
          .single();
        
        if (!checkAgain) break;
      }

      slugMap.set(finalSlug, product.id);
      updates.push({
        id: product.id,
        name: product.name,
        slug: finalSlug
      });
    }

    // Update products with generated slugs
    const updatePromises = updates.map(update => 
      supabase
        .from('products')
        .update({ slug: update.slug })
        .eq('id', update.id)
    );

    const results = await Promise.all(updatePromises);
    
    // Check for any errors
    const errors = results.filter(result => result.error);
    if (errors.length > 0) {
      console.error('Errors updating slugs:', errors);
      return NextResponse.json({ 
        error: 'Some products failed to update',
        errors: errors.map(e => e.error),
        updated: results.length - errors.length
      }, { status: 500 });
    }

    return NextResponse.json({
      message: 'Successfully generated slugs for products',
      updated: updates.length,
      products: updates
    });

  } catch (error) {
    console.error('Unexpected error in slug migration:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
