'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious, type CarouselApi } from '@/components/ui/carousel';
import { ShoppingCart, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import Image from 'next/image';

interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  background_image?: string;
  brand: string;
  model: string;
  show_price?: boolean;
  status?: string;
  stock_quantity?: number;
  description?: string;
  specifications?: Record<string, any>;
  additional_features?: Record<string, any>;
}

export default function EnhancedProductCarousel() {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [api, setApi] = useState<CarouselApi>();
  const [carouselSettings, setCarouselSettings] = useState({ delay: 5000, enabled: true });
  const intervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    fetchCarouselProducts();
    fetchCarouselSettings();
  }, []);

  const fetchCarouselProducts = async () => {
    try {
      const response = await fetch('/api/home/<USER>');
      if (response.ok) {
        const data = await response.json();
        setProducts(data.products || []);
      }
    } catch (error) {
      console.error('Error fetching carousel products:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCarouselSettings = async () => {
    try {
      const response = await fetch('/api/admin/settings/carousel');
      if (response.ok) {
        const data = await response.json();
        setCarouselSettings({ delay: data.delay, enabled: data.enabled });
      }
    } catch (error) {
      console.error('Error fetching carousel settings:', error);
    }
  };

  // Auto-play functionality
  const startAutoPlay = useCallback(() => {
    if (!api || !carouselSettings.enabled || products.length <= 1) return;

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(() => {
      api.scrollNext();
    }, carouselSettings.delay);
  }, [api, carouselSettings.delay, carouselSettings.enabled, products.length]);

  const stopAutoPlay = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = undefined;
    }
  }, []);

  // Set up auto-play when API is ready
  useEffect(() => {
    if (!api) return;

    startAutoPlay();

    // Stop auto-play on user interaction
    const handleUserInteraction = () => {
      stopAutoPlay();
      // Restart auto-play after 3 seconds of inactivity
      setTimeout(startAutoPlay, 3000);
    };

    api.on('select', handleUserInteraction);

    return () => {
      stopAutoPlay();
      api.off('select', handleUserInteraction);
    };
  }, [api, startAutoPlay, stopAutoPlay]);

  // Restart auto-play when settings change
  useEffect(() => {
    if (api && carouselSettings.enabled) {
      startAutoPlay();
    } else {
      stopAutoPlay();
    }
  }, [carouselSettings, startAutoPlay, stopAutoPlay, api]);

  // Clean up on unmount
  useEffect(() => {
    return () => stopAutoPlay();
  }, [stopAutoPlay]);

  if (isLoading) {
    return (
      <div className="w-full">
        <Carousel className="w-full max-w-6xl mx-auto">
          <CarouselContent className="-ml-2 md:-ml-4">
            {Array.from({ length: 4 }).map((_, index) => (
              <CarouselItem key={index} className="pl-2 md:pl-4 md:basis-1/2 lg:basis-1/3">
                <Card className="bg-gradient-card border-shadow-grey/20 hover:border-accent-secondary/30 transition-all duration-300">
                  <CardContent className="p-0">
                    <Skeleton className="h-48 w-full rounded-t-xl" />
                    <div className="p-6 space-y-3">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-3 w-1/2" />
                      <Skeleton className="h-6 w-1/3" />
                      <div className="flex gap-2">
                        <Skeleton className="h-9 flex-1" />
                        <Skeleton className="h-9 w-9" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="bg-muted-steel-blue border-shadow-grey text-soft-white hover:bg-terra-cotta-rose hover:border-terra-cotta-rose" />
          <CarouselNext className="bg-muted-steel-blue border-shadow-grey text-soft-white hover:bg-terra-cotta-rose hover:border-terra-cotta-rose" />
        </Carousel>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <Card className="max-w-md mx-auto bg-gradient-card border-shadow-grey/20">
          <CardContent className="p-8">
            <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
              <ShoppingCart className="w-8 h-8 text-white" />
            </div>
            <h3 className="text-lg font-semibold text-soft-white mb-2">No Featured Products</h3>
            <p className="text-muted-grey">Check back soon for amazing deals on refurbished devices!</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="w-full">
      <Carousel
        className="w-full max-w-5xl mx-auto"
        setApi={setApi}
        opts={{
          align: "start",
          loop: true,
          skipSnaps: false,
          dragFree: false
        }}
      >
        <CarouselContent className="-ml-4">
          {products.map((product) => (
            <CarouselItem key={product.id} className="pl-4 basis-full">
              <Card className="group relative bg-gradient-card border-shadow-grey/20 hover:border-accent-secondary/30 transition-all duration-500 hover:shadow-glow overflow-hidden min-h-[500px]">
                <CardContent className="p-0 h-full">
                  {/* Background Image with Black Overlay */}
                  <div className="absolute inset-0">
                    <Image
                      src={product.background_image || product.image || '/api/placeholder/800/500'}
                      alt={`${product.brand} ${product.model} background`}
                      fill
                      className="object-cover transition-transform duration-700 group-hover:scale-105"
                      priority
                    />
                    {/* Black Overlay */}
                    <div className="absolute inset-0 bg-black/70 transition-opacity duration-500 group-hover:bg-black/80" />
                  </div>

                  {/* Content Container */}
                  <div className="relative z-10 flex h-full min-h-[500px]">

                    {/* Left Side - Product Image */}
                    <div className="w-1/3 p-8 flex items-center justify-center">
                      <div className="relative w-full max-w-[280px] aspect-square rounded-2xl overflow-hidden transition-all duration-500 group-hover:shadow-[0_0_40px_rgba(149,99,88,0.6)]">
                        <Image
                          src={product.image || '/api/placeholder/280/280'}
                          alt={`${product.brand} ${product.model}`}
                          fill
                          className="object-cover transition-transform duration-500 group-hover:scale-110 drop-shadow-2xl"
                          priority
                        />
                      </div>
                    </div>

                    {/* Right Side - Featured Tag (Always Visible) */}
                    <div className="flex-1 relative">
                      {/* Featured Badge - Top Right */}
                      <div className="absolute top-4 right-4 z-20">
                        <Badge className="bg-gradient-primary text-white border-0 shadow-lg text-sm px-3 py-1">
                          ⭐ Featured
                        </Badge>
                      </div>

                      {/* Product Details - Show on Hover */}
                      <div className="absolute inset-0 p-8 flex flex-col justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform translate-x-8 group-hover:translate-x-0">

                        {/* Product Name, Brand and Model */}
                        <div className="space-y-3 mb-6">
                          <h3 className="text-3xl lg:text-4xl font-bold text-white drop-shadow-lg">
                            {product.name}
                          </h3>
                          <div className="flex items-center gap-3">
                            <span className="text-lg font-medium text-accent-secondary uppercase tracking-wider">
                              {product.brand}
                            </span>
                            <div className="h-1 w-12 bg-gradient-primary rounded-full"></div>
                            <span className="text-lg text-gray-200">
                              {product.model}
                            </span>
                          </div>
                        </div>

                        {/* Technical Specifications */}
                        {(product.specifications || product.additional_features) && (
                          <div className="space-y-4 mb-6">
                            <h4 className="text-sm font-semibold text-white uppercase tracking-wider">
                              Technical Specifications
                            </h4>
                            <div className="grid grid-cols-2 gap-3">
                              {(() => {
                                // Combine specifications and additional features
                                const allSpecs = { ...product.specifications, ...product.additional_features };

                                // Filter out empty values and get first 4 specifications
                                const validSpecs = Object.entries(allSpecs)
                                  .filter(([key, value]) => value && value.toString().trim() !== '' && value !== 'N/A')
                                  .slice(0, 4);

                                return validSpecs.map(([key, value], index) => (
                                  <div key={key} className={`flex items-center gap-2 p-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 ${validSpecs.length === 3 && index === 2 ? 'col-span-2' : ''}`}>
                                    <div className="w-2 h-2 bg-accent-secondary rounded-full"></div>
                                    <span className="text-sm text-gray-200">
                                      <span className="text-white font-medium">{key}:</span> {value}
                                    </span>
                                  </div>
                                ));
                              })()}
                            </div>
                          </div>
                        )}



                        {/* Action Buttons */}
                        <div className="flex justify-center">
                          <Link href={`/products/${product.slug}`}>
                            <Button size="default" className="bg-gradient-primary hover:bg-gradient-primary/90 text-white shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-sm border border-white/20">
                              <Eye className="w-4 h-4 mr-2" />
                              View Product
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </CarouselItem>
          ))}
        </CarouselContent>
        <CarouselPrevious className="border-shadow-grey/30 bg-gradient-card hover:bg-accent-primary hover:border-accent-primary hover:scale-110 hover:shadow-xl text-soft-white hover:text-white -left-16 w-12 h-12 cursor-pointer transition-all duration-300" />
        <CarouselNext className="border-shadow-grey/30 bg-gradient-card hover:bg-accent-primary hover:border-accent-primary hover:scale-110 hover:shadow-xl text-soft-white hover:text-white -right-16 w-12 h-12 cursor-pointer transition-all duration-300" />
      </Carousel>
    </div>
  );
}
