'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { ArrowLeft, Save, Upload, X, Loader2 } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  price: number;
  images: string[];
  brand: string;
  model: string;
}

interface CarouselProduct {
  id: string;
  product_id: string;
  display_order: number;
  custom_image?: string;
  custom_description?: string;
  is_active: boolean;
  products: Product;
}

export default function EditCarouselProductPage() {
  const router = useRouter();
  const params = useParams();
  const [carouselProduct, setCarouselProduct] = useState<CarouselProduct | null>(null);
  const [customImage, setCustomImage] = useState('');
  const [customDescription, setCustomDescription] = useState('');
  const [customBackgroundImage, setCustomBackgroundImage] = useState('');
  const [isActive, setIsActive] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (params.id) {
      fetchCarouselProduct();
    }
  }, [params.id]);

  const fetchCarouselProduct = async () => {
    try {
      const response = await fetch(`/api/admin/home/<USER>/${params.id}`);
      if (response.ok) {
        const data = await response.json();
        setCarouselProduct(data);
        setCustomImage(data.custom_image || '');
        setCustomDescription(data.custom_description || '');
        setCustomBackgroundImage(data.custom_background_image || '');
        setIsActive(data.is_active);
      } else {
        alert('Failed to fetch carousel product');
        router.push('/admin/home-settings');
      }
    } catch (error) {
      console.error('Error fetching carousel product:', error);
      alert('Error fetching carousel product');
      router.push('/admin/home-settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      const response = await fetch(`/api/admin/home/<USER>/${params.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          custom_image: customImage,
          custom_description: customDescription,
          custom_background_image: customBackgroundImage,
          is_active: isActive,
        }),
      });

      if (response.ok) {
        alert('Carousel product updated successfully!');
        router.push('/admin/home-settings');
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to update carousel product');
      }
    } catch (error) {
      console.error('Error updating carousel product:', error);
      alert('Error updating carousel product');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-[var(--accent-primary)] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!carouselProduct) {
    return (
      <div className="text-center py-8">
        <p className="text-[var(--text-secondary)]">Carousel product not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => router.push('/admin/home-settings')}
          className="p-2 bg-[var(--background-card)] border border-[var(--shadow-grey)] rounded-md hover:bg-[var(--shadow-grey)]/20 transition-colors"
        >
          <ArrowLeft className="h-4 w-4 text-[var(--text-secondary)]" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-[var(--text-primary)]">Edit Carousel Product</h1>
          <p className="text-[var(--text-secondary)]">
            Customize how this product appears in the carousel
          </p>
        </div>
      </div>

      <div className="grid lg:grid-cols-2 gap-6">
        {/* Product Info */}
        <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
          <h2 className="text-lg font-semibold text-[var(--text-primary)] mb-4">Product Information</h2>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-[var(--shadow-grey)] rounded-lg overflow-hidden">
                {carouselProduct.products.images && carouselProduct.products.images.length > 0 ? (
                  <img
                    src={carouselProduct.products.images[0]}
                    alt={carouselProduct.products.name}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-[var(--text-secondary)]">
                    <Upload className="h-6 w-6" />
                  </div>
                )}
              </div>
              <div>
                <h3 className="font-semibold text-[var(--text-primary)]">{carouselProduct.products.name}</h3>
                <p className="text-sm text-[var(--text-secondary)]">
                  {carouselProduct.products.brand} • {carouselProduct.products.model}
                </p>
                <p className="text-sm text-[var(--accent-primary)] font-medium">
                  ₹{carouselProduct.products.price.toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Customization Form */}
        <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
          <h2 className="text-lg font-semibold text-[var(--text-primary)] mb-4">Carousel Customization</h2>
          
          <div className="space-y-4">
            {/* Custom Image */}
            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                Custom Image URL (Optional)
              </label>
              <input
                type="url"
                value={customImage}
                onChange={(e) => setCustomImage(e.target.value)}
                placeholder="https://example.com/custom-image.jpg"
                className="w-full px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
              />
              <p className="text-xs text-[var(--text-secondary)] mt-1">
                Leave empty to use the product's default image
              </p>
              {customImage && (
                <div className="mt-2">
                  <img
                    src={customImage}
                    alt="Custom preview"
                    className="w-full h-32 object-cover rounded border border-[var(--shadow-grey)]"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              )}
            </div>

            {/* Custom Description */}
            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                Custom Description (Optional)
              </label>
              <textarea
                value={customDescription}
                onChange={(e) => setCustomDescription(e.target.value)}
                placeholder="Custom description for this carousel item..."
                rows={3}
                className="w-full px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] resize-none"
              />
              <p className="text-xs text-[var(--text-secondary)] mt-1">
                Leave empty to use the product's default description
              </p>
            </div>

            {/* Custom Background Image */}
            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                Custom Background Image (Optional)
              </label>
              <input
                type="url"
                value={customBackgroundImage}
                onChange={(e) => setCustomBackgroundImage(e.target.value)}
                placeholder="https://example.com/background-image.jpg"
                className="w-full px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
              />
              <p className="text-xs text-[var(--text-secondary)] mt-1">
                Custom background image for this carousel slide (overrides global banner image)
              </p>
              {customBackgroundImage && (
                <div className="mt-2">
                  <img
                    src={customBackgroundImage}
                    alt="Background preview"
                    className="w-full h-32 object-cover rounded border border-[var(--shadow-grey)]"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                    }}
                  />
                </div>
              )}
            </div>

            {/* Active Status */}
            <div className="flex items-center justify-between p-3 bg-[var(--background-primary)]/50 rounded-lg border border-[var(--shadow-grey)]">
              <div>
                <h3 className="text-sm font-medium text-[var(--text-primary)]">Active in Carousel</h3>
                <p className="text-xs text-[var(--text-secondary)]">
                  Show this product in the carousel
                </p>
              </div>
              <label className="relative inline-flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={isActive}
                  onChange={(e) => setIsActive(e.target.checked)}
                  className="sr-only peer"
                />
                <div className="w-11 h-6 bg-[var(--shadow-grey)] peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[var(--accent-primary)]/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[var(--accent-primary)]"></div>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex items-center justify-end space-x-4 pt-6 border-t border-[var(--shadow-grey)]">
        <button
          onClick={() => router.push('/admin/home-settings')}
          className="px-4 py-2 bg-[var(--background-card)] border border-[var(--shadow-grey)] text-[var(--text-secondary)] rounded-md hover:bg-[var(--shadow-grey)]/20 transition-colors"
        >
          Cancel
        </button>
        <button
          onClick={handleSave}
          disabled={isSaving}
          className="btn-primary flex items-center space-x-2"
        >
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              <span>Saving...</span>
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              <span>Save Changes</span>
            </>
          )}
        </button>
      </div>
    </div>
  );
}
