'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Settings,
  Image as ImageIcon,
  Clock,
  Save,
  Plus,
  Trash2,
  Edit,
  Eye,
  Grid,
  Layers,
  GripVertical,
  ArrowUp,
  ArrowDown
} from 'lucide-react';

interface CarouselProduct {
  id: string;
  product_id: string;
  display_order: number;
  custom_image?: string;
  custom_description?: string;
  is_active: boolean;
  products: {
    id: string;
    name: string;
    price: number;
    images: string[];
    brand: string;
    model: string;
  };
}

interface CategoryData {
  id: string;
  category_id: string;
  custom_image?: string;
  display_order: number;
  is_active: boolean;
  categories: {
    id: string;
    name: string;
    slug: string;
  };
}

export default function HomeSettingsPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<'carousel' | 'categories'>('carousel');
  const [carouselProducts, setCarouselProducts] = useState<CarouselProduct[]>([]);
  const [categories, setCategories] = useState<CategoryData[]>([]);
  const [carouselSettings, setCarouselSettings] = useState({
    auto_play_interval: 5000,
    enabled: true,
    banner_image: ''
  });
  const [gallerySettings, setGallerySettings] = useState({
    products_per_category: 8,
    grid_columns: 2,
    show_price: true,
    hover_animation: true,
    auto_hide_delay: 3000
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      // Fetch carousel data
      const carouselResponse = await fetch('/api/admin/home/<USER>');
      if (carouselResponse.ok) {
        const carouselData = await carouselResponse.json();
        setCarouselProducts(carouselData.products || []);
        setCarouselSettings(carouselData.settings || { auto_play_interval: 5000, enabled: true });
      }

      // Fetch carousel settings from admin settings
      const settingsResponse = await fetch('/api/admin/settings/carousel');
      if (settingsResponse.ok) {
        const settingsData = await settingsResponse.json();
        setCarouselSettings(prev => ({
          ...prev,
          auto_play_interval: settingsData.delay,
          enabled: settingsData.enabled,
          banner_image: settingsData.banner_image || ''
        }));
      }

      // Fetch gallery settings
      const gallerySettingsResponse = await fetch('/api/admin/settings/category-gallery');
      if (gallerySettingsResponse.ok) {
        const galleryData = await gallerySettingsResponse.json();
        setGallerySettings(prev => ({
          ...prev,
          ...galleryData
        }));
      }

      // Fetch categories data
      const categoriesResponse = await fetch('/api/admin/home/<USER>');
      if (categoriesResponse.ok) {
        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData.categories || []);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveCarouselSettings = async () => {
    setIsSaving(true);
    try {
      const response = await fetch('/api/admin/settings/carousel', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          delay: carouselSettings.auto_play_interval,
          enabled: carouselSettings.enabled,
          banner_image: carouselSettings.banner_image
        }),
      });

      if (response.ok) {
        alert('Carousel settings saved successfully!');
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to save carousel settings');
      }
    } catch (error) {
      console.error('Error saving carousel settings:', error);
      alert('Error saving carousel settings');
    } finally {
      setIsSaving(false);
    }
  };

  const saveGallerySettings = async () => {
    setIsSaving(true);
    try {
      const response = await fetch('/api/admin/settings/category-gallery', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(gallerySettings),
      });

      if (response.ok) {
        alert('Gallery settings saved successfully!');
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to save gallery settings');
      }
    } catch (error) {
      console.error('Error saving gallery settings:', error);
      alert('Error saving gallery settings');
    } finally {
      setIsSaving(false);
    }
  };

  const toggleCarouselProduct = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/home/<USER>/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_active: !isActive }),
      });

      if (response.ok) {
        setCarouselProducts(prev => 
          prev.map(item => 
            item.id === id ? { ...item, is_active: !isActive } : item
          )
        );
      }
    } catch (error) {
      console.error('Error toggling carousel product:', error);
    }
  };

  const toggleCategory = async (id: string, isActive: boolean) => {
    try {
      const response = await fetch(`/api/admin/home/<USER>/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_active: !isActive }),
      });

      if (response.ok) {
        setCategories(prev =>
          prev.map(item =>
            item.id === id ? { ...item, is_active: !isActive } : item
          )
        );
      }
    } catch (error) {
      console.error('Error toggling category:', error);
    }
  };

  const moveCarouselProduct = async (id: string, direction: 'up' | 'down') => {
    const currentIndex = carouselProducts.findIndex(item => item.id === id);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= carouselProducts.length) return;

    // Create new array with swapped positions
    const newProducts = [...carouselProducts];
    [newProducts[currentIndex], newProducts[newIndex]] = [newProducts[newIndex], newProducts[currentIndex]];

    // Update display orders
    const updates = [
      { id: newProducts[currentIndex].id, display_order: currentIndex },
      { id: newProducts[newIndex].id, display_order: newIndex }
    ];

    try {
      // Update both products
      await Promise.all(updates.map(update =>
        fetch(`/api/admin/home/<USER>/${update.id}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ display_order: update.display_order }),
        })
      ));

      // Update local state
      setCarouselProducts(newProducts.map((item, index) => ({
        ...item,
        display_order: index
      })));
    } catch (error) {
      console.error('Error reordering carousel products:', error);
    }
  };

  const moveCategory = async (id: string, direction: 'up' | 'down') => {
    const currentIndex = categories.findIndex(item => item.id === id);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= categories.length) return;

    // Create new array with swapped positions
    const newCategories = [...categories];
    [newCategories[currentIndex], newCategories[newIndex]] = [newCategories[newIndex], newCategories[currentIndex]];

    // Update display orders
    const updates = [
      { id: newCategories[currentIndex].id, display_order: currentIndex },
      { id: newCategories[newIndex].id, display_order: newIndex }
    ];

    try {
      // Update both categories
      await Promise.all(updates.map(update =>
        fetch(`/api/admin/home/<USER>/${update.id}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ display_order: update.display_order }),
        })
      ));

      // Update local state
      setCategories(newCategories.map((item, index) => ({
        ...item,
        display_order: index
      })));
    } catch (error) {
      console.error('Error reordering categories:', error);
    }
  };

  const deleteCarouselProduct = async (id: string) => {
    if (!confirm('Are you sure you want to remove this product from the carousel?')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/home/<USER>/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setCarouselProducts(prev => prev.filter(item => item.id !== id));
      } else {
        alert('Failed to delete carousel product');
      }
    } catch (error) {
      console.error('Error deleting carousel product:', error);
      alert('Error deleting carousel product');
    }
  };

  const deleteFeaturedCategory = async (id: string) => {
    if (!confirm('Are you sure you want to remove this category from featured list? This will also remove all products from its gallery.')) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/home/<USER>/${id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setCategories(prev => prev.filter(item => item.id !== id));
      } else {
        alert('Failed to delete featured category');
      }
    } catch (error) {
      console.error('Error deleting featured category:', error);
      alert('Error deleting featured category');
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-[var(--accent-primary)] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-[var(--shadow-grey)] pb-4">
        <h1 className="text-3xl font-bold text-[var(--text-primary)]">Home Page Settings</h1>
        <p className="text-[var(--text-secondary)] mt-1">
          Manage carousel products and featured categories for the home page
        </p>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-[var(--background-card)] p-1 rounded-lg border border-[var(--shadow-grey)]">
        <button
          onClick={() => setActiveTab('carousel')}
          className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'carousel'
              ? 'bg-[var(--accent-primary)] text-[var(--text-primary)]'
              : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'
          }`}
        >
          <ImageIcon className="h-4 w-4" />
          <span>Product Carousel</span>
        </button>
        <button
          onClick={() => setActiveTab('categories')}
          className={`flex-1 flex items-center justify-center space-x-2 px-4 py-2 rounded-md transition-colors ${
            activeTab === 'categories'
              ? 'bg-[var(--accent-primary)] text-[var(--text-primary)]'
              : 'text-[var(--text-secondary)] hover:text-[var(--text-primary)]'
          }`}
        >
          <Layers className="h-4 w-4" />
          <span>Featured Categories</span>
        </button>
      </div>

      {/* Carousel Tab */}
      {activeTab === 'carousel' && (
        <div className="space-y-6">
          {/* Carousel Settings */}
          <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
            <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-4 flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>Carousel Settings</span>
            </h2>
            
            <div className="space-y-6">
              {/* Enable/Disable Auto-play */}
              <div className="flex items-center justify-between p-4 bg-[var(--background-primary)]/50 rounded-lg border border-[var(--shadow-grey)]">
                <div>
                  <h3 className="text-sm font-medium text-[var(--text-primary)]">Auto-play Carousel</h3>
                  <p className="text-xs text-[var(--text-secondary)] mt-1">
                    Enable automatic carousel sliding
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={carouselSettings.enabled}
                    onChange={(e) => setCarouselSettings(prev => ({
                      ...prev,
                      enabled: e.target.checked
                    }))}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-[var(--shadow-grey)] peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[var(--accent-primary)]/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[var(--accent-primary)]"></div>
                </label>
              </div>

              {/* Auto-play Interval */}
              <div className={`transition-opacity ${carouselSettings.enabled ? 'opacity-100' : 'opacity-50'}`}>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  Auto-play Interval
                </label>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-[var(--text-secondary)]" />
                  <input
                    type="number"
                    value={carouselSettings.auto_play_interval ? carouselSettings.auto_play_interval / 1000 : 5}
                    onChange={(e) => setCarouselSettings(prev => ({
                      ...prev,
                      auto_play_interval: (parseFloat(e.target.value) || 5) * 1000
                    }))}
                    disabled={!carouselSettings.enabled}
                    className="flex-1 px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] disabled:opacity-50 disabled:cursor-not-allowed"
                    min="1"
                    max="30"
                    step="0.5"
                  />
                  <span className="text-sm text-[var(--text-secondary)]">seconds</span>
                </div>
                <p className="text-xs text-[var(--text-secondary)] mt-1">
                  Time between automatic slide transitions (1-30 seconds)
                </p>
              </div>

              {/* Banner Image */}
              <div>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  Custom Banner Image
                </label>
                <div className="flex items-center space-x-2">
                  <ImageIcon className="h-4 w-4 text-[var(--text-secondary)]" />
                  <input
                    type="url"
                    value={carouselSettings.banner_image || ''}
                    onChange={(e) => setCarouselSettings(prev => ({
                      ...prev,
                      banner_image: e.target.value
                    }))}
                    placeholder="https://example.com/banner-image.jpg"
                    className="flex-1 px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
                  />
                </div>
                <p className="text-xs text-[var(--text-secondary)] mt-1">
                  Optional: Custom background image for carousel slides (leave empty for product images)
                </p>
                {carouselSettings.banner_image && (
                  <div className="mt-2 p-2 bg-[var(--background-primary)]/30 rounded border border-[var(--shadow-grey)]/50">
                    <img
                      src={carouselSettings.banner_image}
                      alt="Banner preview"
                      className="w-full h-20 object-cover rounded"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none';
                      }}
                    />
                  </div>
                )}
              </div>

              {/* Preview */}
              <div className="p-4 bg-[var(--background-primary)]/30 rounded-lg border border-[var(--shadow-grey)]/50">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-2 h-2 bg-[var(--accent-primary)] rounded-full"></div>
                  <span className="text-sm font-medium text-[var(--text-primary)]">Preview</span>
                </div>
                <p className="text-sm text-[var(--text-secondary)]">
                  {carouselSettings.enabled
                    ? `Carousel will automatically change slides every ${carouselSettings.auto_play_interval / 1000} seconds`
                    : 'Carousel auto-play is disabled - manual navigation only'
                  }
                </p>
              </div>

              {/* Save Button */}
              <div className="flex justify-end">
                <button
                  onClick={saveCarouselSettings}
                  disabled={isSaving}
                  className="btn-primary flex items-center space-x-2"
                >
                  <Save className="h-4 w-4" />
                  <span>{isSaving ? 'Saving...' : 'Save Settings'}</span>
                </button>
              </div>
            </div>
          </div>

          {/* Carousel Products */}
          <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h2 className="text-xl font-semibold text-[var(--text-primary)] flex items-center space-x-2">
                  <Grid className="h-5 w-5" />
                  <span>Carousel Products</span>
                </h2>
                <p className="text-sm text-[var(--text-secondary)] mt-1">
                  {carouselProducts.length} products • {carouselProducts.filter(p => p.is_active).length} active
                </p>
              </div>
              <button
                onClick={() => router.push('/admin/home-settings/carousel/new')}
                className="btn-primary flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Add Product</span>
              </button>
            </div>

            <div className="space-y-4">
              {carouselProducts.map((item) => (
                <div
                  key={item.id}
                  className={`flex items-center space-x-4 p-4 rounded-lg border transition-colors ${
                    item.is_active 
                      ? 'border-[var(--accent-primary)]/30 bg-[var(--accent-primary)]/5' 
                      : 'border-[var(--shadow-grey)] bg-[var(--background-primary)]/50'
                  }`}
                >
                  <div className="flex items-center space-x-3 flex-shrink-0">
                    {/* Order number badge */}
                    <div className="w-8 h-8 bg-[var(--accent-primary)] text-[var(--text-primary)] rounded-full flex items-center justify-center text-sm font-bold">
                      {item.display_order + 1}
                    </div>

                    <div className="w-16 h-16 bg-[var(--shadow-grey)] rounded-lg overflow-hidden">
                      {item.custom_image || (item.products.images && item.products.images.length > 0) ? (
                        <img
                          src={item.custom_image || item.products.images[0]}
                          alt={item.products.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-[var(--text-secondary)]">
                          <ImageIcon className="h-6 w-6" />
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex-1">
                    <h3 className="font-semibold text-[var(--text-primary)]">{item.products.name}</h3>
                    <p className="text-sm text-[var(--text-secondary)]">
                      {item.products.brand} • {item.products.model}
                    </p>
                    {item.custom_description && (
                      <p className="text-xs text-[var(--text-secondary)] mt-1 line-clamp-1">
                        Custom: {item.custom_description}
                      </p>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* Reorder buttons */}
                    <div className="flex flex-col space-y-1">
                      <button
                        onClick={() => moveCarouselProduct(item.id, 'up')}
                        disabled={item.display_order === 0}
                        className="p-1 rounded text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:bg-[var(--shadow-grey)] transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
                        title="Move up"
                      >
                        <ArrowUp className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => moveCarouselProduct(item.id, 'down')}
                        disabled={item.display_order === carouselProducts.length - 1}
                        className="p-1 rounded text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:bg-[var(--shadow-grey)] transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
                        title="Move down"
                      >
                        <ArrowDown className="h-3 w-3" />
                      </button>
                    </div>

                    <button
                      onClick={() => toggleCarouselProduct(item.id, item.is_active)}
                      className={`p-2 rounded-md transition-colors ${
                        item.is_active
                          ? 'bg-[var(--success)] text-white hover:bg-[var(--success)]/80'
                          : 'bg-[var(--shadow-grey)] text-[var(--text-secondary)] hover:bg-[var(--shadow-grey)]/80'
                      }`}
                      title={item.is_active ? 'Active' : 'Inactive'}
                    >
                      <Eye className="h-4 w-4" />
                    </button>

                    <button
                      onClick={() => router.push(`/admin/home-settings/carousel/${item.id}/edit`)}
                      className="p-2 bg-[var(--accent-primary)] text-white rounded-md hover:bg-[var(--accent-primary)]/80 transition-colors"
                      title="Edit"
                    >
                      <Edit className="h-4 w-4" />
                    </button>

                    <button
                      onClick={() => deleteCarouselProduct(item.id)}
                      className="p-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                      title="Remove from carousel"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}

              {carouselProducts.length === 0 && (
                <div className="text-center py-8">
                  <ImageIcon className="h-12 w-12 text-[var(--text-secondary)] mx-auto mb-4" />
                  <p className="text-[var(--text-secondary)]">No carousel products configured</p>
                  <button
                    onClick={() => router.push('/admin/home-settings/carousel/new')}
                    className="btn-primary mt-4"
                  >
                    Add First Product
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Categories Tab */}
      {activeTab === 'categories' && (
        <div className="space-y-6">
          {/* Gallery Settings */}
          <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
            <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-4 flex items-center space-x-2">
              <Grid className="h-5 w-5" />
              <span>Category Gallery Settings</span>
            </h2>
            <p className="text-sm text-[var(--text-secondary)] mb-6">
              Configure how product galleries appear when users hover over category cards.
            </p>

            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Products per Category */}
              <div>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  Products per Gallery
                </label>
                <input
                  type="number"
                  value={gallerySettings.products_per_category}
                  onChange={(e) => setGallerySettings(prev => ({
                    ...prev,
                    products_per_category: parseInt(e.target.value) || 8
                  }))}
                  className="w-full px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
                  min="4"
                  max="20"
                />
              </div>

              {/* Grid Columns */}
              <div>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  Grid Layout
                </label>
                <select
                  value={gallerySettings.grid_columns}
                  onChange={(e) => setGallerySettings(prev => ({
                    ...prev,
                    grid_columns: parseInt(e.target.value)
                  }))}
                  className="w-full px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
                >
                  <option value="2">2 Columns</option>
                  <option value="3">3 Columns</option>
                </select>
              </div>

              {/* Show Price Toggle */}
              <div>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  Show Prices
                </label>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={gallerySettings.show_price}
                    onChange={(e) => setGallerySettings(prev => ({
                      ...prev,
                      show_price: e.target.checked
                    }))}
                    className="sr-only peer"
                  />
                  <div className="w-11 h-6 bg-[var(--shadow-grey)] peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-[var(--accent-primary)]/25 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[var(--accent-primary)]"></div>
                </label>
              </div>
            </div>

            {/* Save Button */}
            <div className="flex justify-end mt-6">
              <button
                onClick={saveGallerySettings}
                disabled={isSaving}
                className="btn-primary flex items-center space-x-2"
              >
                <Save className="h-4 w-4" />
                <span>{isSaving ? 'Saving...' : 'Save Gallery Settings'}</span>
              </button>
            </div>
          </div>

          {/* Featured Categories */}
          <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h2 className="text-xl font-semibold text-[var(--text-primary)] flex items-center space-x-2">
                  <Layers className="h-5 w-5" />
                  <span>Featured Categories</span>
                </h2>
                <p className="text-sm text-[var(--text-secondary)] mt-1">
                  {categories.length} categories • {categories.filter(c => c.is_active).length} active
                </p>
              </div>
              <button
                onClick={() => router.push('/admin/home-settings/categories/new')}
                className="btn-primary flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Add Category</span>
              </button>
            </div>

            <div className="space-y-4">
              {categories.map((item) => (
                <div
                  key={item.id}
                  className={`flex items-center space-x-4 p-4 rounded-lg border transition-colors ${
                    item.is_active
                      ? 'border-[var(--accent-primary)]/30 bg-[var(--accent-primary)]/5'
                      : 'border-[var(--shadow-grey)] bg-[var(--background-primary)]/50'
                  }`}
                >
                  <div className="flex items-center space-x-3 flex-shrink-0">
                    {/* Order number badge */}
                    <div className="w-8 h-8 bg-[var(--accent-primary)] text-[var(--text-primary)] rounded-full flex items-center justify-center text-sm font-bold">
                      {item.display_order + 1}
                    </div>

                    <div className="w-16 h-16 bg-[var(--shadow-grey)] rounded-lg overflow-hidden">
                      {item.custom_image ? (
                        <img
                          src={item.custom_image}
                          alt={item.categories.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-[var(--text-secondary)]">
                          <Layers className="h-6 w-6" />
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex-1">
                    <h3 className="font-semibold text-[var(--text-primary)]">{item.categories.name}</h3>
                    <p className="text-sm text-[var(--text-secondary)]">
                      Category • Order: {item.display_order + 1}
                    </p>
                  </div>

                  <div className="flex items-center space-x-2">
                    {/* Reorder buttons */}
                    <div className="flex flex-col space-y-1">
                      <button
                        onClick={() => moveCategory(item.id, 'up')}
                        disabled={item.display_order === 0}
                        className="p-1 rounded text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:bg-[var(--shadow-grey)] transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
                        title="Move up"
                      >
                        <ArrowUp className="h-3 w-3" />
                      </button>
                      <button
                        onClick={() => moveCategory(item.id, 'down')}
                        disabled={item.display_order === categories.length - 1}
                        className="p-1 rounded text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:bg-[var(--shadow-grey)] transition-colors disabled:opacity-30 disabled:cursor-not-allowed"
                        title="Move down"
                      >
                        <ArrowDown className="h-3 w-3" />
                      </button>
                    </div>

                    <button
                      onClick={() => toggleCategory(item.id, item.is_active)}
                      className={`px-3 py-1 rounded-md transition-colors text-sm ${
                        item.is_active
                          ? 'bg-[var(--success)] text-white hover:bg-[var(--success)]/80'
                          : 'bg-[var(--shadow-grey)] text-[var(--text-secondary)] hover:bg-[var(--shadow-grey)]/80'
                      }`}
                    >
                      {item.is_active ? 'Active' : 'Inactive'}
                    </button>

                    <button
                      onClick={() => router.push(`/admin/home-settings/categories/${item.id}/edit`)}
                      className="p-2 bg-[var(--accent-primary)] text-white rounded-md hover:bg-[var(--accent-primary)]/80 transition-colors"
                      title="Edit"
                    >
                      <Edit className="h-4 w-4" />
                    </button>

                    <button
                      onClick={() => deleteFeaturedCategory(item.id)}
                      className="p-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                      title="Remove from featured"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}

              {categories.length === 0 && (
                <div className="text-center py-8">
                  <Layers className="h-12 w-12 text-[var(--text-secondary)] mx-auto mb-4" />
                  <p className="text-[var(--text-secondary)]">No featured categories configured</p>
                  <button
                    onClick={() => router.push('/admin/home-settings/categories/new')}
                    className="btn-primary mt-4"
                  >
                    Add First Category
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}


    </div>
  );
}
