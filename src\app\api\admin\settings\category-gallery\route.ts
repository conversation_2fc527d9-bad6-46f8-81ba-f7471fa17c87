import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET() {
  try {
    // Get category gallery settings
    const { data: settings, error } = await supabase
      .from('admin_settings')
      .select('setting_value')
      .eq('setting_key', 'category_gallery_settings')
      .single();

    if (error && error.code !== 'PGRST116') {
      console.error('Error fetching category gallery settings:', error);
      return NextResponse.json({ error: 'Failed to fetch settings' }, { status: 500 });
    }

    // Default settings if none exist
    const defaultSettings = {
      products_per_category: 8,
      grid_columns: 2,
      show_price: true,
      hover_animation: true,
      auto_hide_delay: 3000
    };

    let currentSettings = defaultSettings;
    if (settings?.setting_value) {
      try {
        currentSettings = JSON.parse(settings.setting_value);
      } catch (parseError) {
        console.error('Error parsing settings JSON:', parseError);
      }
    }

    return NextResponse.json(currentSettings);

  } catch (error) {
    console.error('Error in category gallery settings API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate the settings
    const validatedSettings = {
      products_per_category: Math.max(4, Math.min(20, parseInt(body.products_per_category) || 8)),
      grid_columns: [2, 3].includes(parseInt(body.grid_columns)) ? parseInt(body.grid_columns) : 2,
      show_price: Boolean(body.show_price),
      hover_animation: Boolean(body.hover_animation),
      auto_hide_delay: Math.max(0, Math.min(10000, parseInt(body.auto_hide_delay) || 3000))
    };

    // Check if setting exists
    const { data: existingSetting } = await supabase
      .from('admin_settings')
      .select('id')
      .eq('setting_key', 'category_gallery_settings')
      .single();

    let error;
    if (existingSetting) {
      // Update existing setting
      const { error: updateError } = await supabase
        .from('admin_settings')
        .update({
          setting_value: JSON.stringify(validatedSettings),
          updated_at: new Date().toISOString()
        })
        .eq('setting_key', 'category_gallery_settings');
      error = updateError;
    } else {
      // Insert new setting
      const { error: insertError } = await supabase
        .from('admin_settings')
        .insert({
          setting_key: 'category_gallery_settings',
          setting_value: JSON.stringify(validatedSettings),
          enabled: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
      error = insertError;
    }

    if (error) {
      console.error('Error saving category gallery settings:', error);
      return NextResponse.json({ error: 'Failed to save settings' }, { status: 500 });
    }

    return NextResponse.json({
      message: 'Category gallery settings saved successfully',
      settings: validatedSettings
    });

  } catch (error) {
    console.error('Error in category gallery settings PUT:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
