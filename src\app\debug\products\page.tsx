'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface Product {
  id: string;
  name: string;
  slug: string;
  status: string;
  is_active: boolean;
}

export default function DebugProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [migrationResult, setMigrationResult] = useState<any>(null);
  const [isRunningMigration, setIsRunningMigration] = useState(false);

  useEffect(() => {
    fetchProducts();
  }, []);

  const fetchProducts = async () => {
    try {
      const response = await fetch('/api/debug/products');
      if (response.ok) {
        const data = await response.json();
        setProducts(data.products || []);
      } else {
        console.error('Failed to fetch products');
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const runMigration = async () => {
    setIsRunningMigration(true);
    try {
      const response = await fetch('/api/admin/migrate-database', {
        method: 'POST',
      });
      const data = await response.json();
      setMigrationResult(data);
      
      // Refresh products after migration
      await fetchProducts();
    } catch (error) {
      console.error('Error running migration:', error);
      setMigrationResult({ error: 'Failed to run migration' });
    } finally {
      setIsRunningMigration(false);
    }
  };

  const testProductUrl = async (slug: string) => {
    try {
      const response = await fetch(`/api/products/${slug}`);
      const data = await response.json();
      console.log(`Test result for ${slug}:`, data);
      alert(`Test result for ${slug}: ${response.ok ? 'SUCCESS' : 'FAILED'}\n${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      console.error(`Error testing ${slug}:`, error);
      alert(`Error testing ${slug}: ${error}`);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#03132a] text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Debug: Products & Slugs</h1>
        
        {/* Migration Section */}
        <Card className="p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Database Migration</h2>
          <Button 
            onClick={runMigration} 
            disabled={isRunningMigration}
            className="mb-4"
          >
            {isRunningMigration ? 'Running Migration...' : 'Run Database Migration'}
          </Button>
          
          {migrationResult && (
            <div className="bg-gray-800 p-4 rounded-lg">
              <h3 className="font-semibold mb-2">Migration Result:</h3>
              <pre className="text-sm overflow-auto">
                {JSON.stringify(migrationResult, null, 2)}
              </pre>
            </div>
          )}
        </Card>

        {/* Products List */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Products in Database</h2>
          
          {isLoading ? (
            <p>Loading products...</p>
          ) : products.length === 0 ? (
            <p>No products found in database.</p>
          ) : (
            <div className="space-y-4">
              {products.map((product) => (
                <div key={product.id} className="border border-gray-600 p-4 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-center">
                    <div>
                      <strong>Name:</strong> {product.name}
                    </div>
                    <div>
                      <strong>Slug:</strong> {product.slug || 'NO SLUG'}
                    </div>
                    <div>
                      <strong>Status:</strong> {product.status || 'NO STATUS'} / {product.is_active ? 'Active' : 'Inactive'}
                    </div>
                    <div>
                      {product.slug && (
                        <Button 
                          size="sm" 
                          onClick={() => testProductUrl(product.slug)}
                          className="mr-2"
                        >
                          Test API
                        </Button>
                      )}
                      {product.slug && (
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => window.open(`/products/${product.slug}`, '_blank')}
                        >
                          View Page
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          <div className="mt-6">
            <Button onClick={fetchProducts} variant="outline">
              Refresh Products
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
}
