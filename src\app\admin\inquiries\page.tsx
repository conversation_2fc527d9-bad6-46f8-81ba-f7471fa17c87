'use client';

import { useState, useEffect } from 'react';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import { 
  Search, 
  Filter, 
  Eye, 
  MessageSquare, 
  Package,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  TrendingUp
} from 'lucide-react';
import Link from 'next/link';

interface Inquiry {
  id: string;
  user_id: string;
  product_id: string;
  type: 'regular' | 'bulk' | 'hot';
  quantity: number;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'accepted' | 'rejected';
  contact_details: {
    fullName?: string;
    email?: string;
    phone?: string;
    whatsappNumber?: string;
    companyName?: string;
  };
  products: {
    id: string;
    name: string;
    brand: string;
    model: string;
    price: number;
    images: string[];
  };
  users: {
    id: string;
    full_name: string;
    email: string;
    phone: string;
  };
  created_at: string;
  updated_at: string;
  message_count?: number;
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800',
  in_progress: 'bg-blue-100 text-blue-800',
  completed: 'bg-green-100 text-green-800',
  cancelled: 'bg-red-100 text-red-800',
  accepted: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800',
};

const statusIcons = {
  pending: Clock,
  in_progress: TrendingUp,
  completed: CheckCircle,
  cancelled: XCircle,
  accepted: CheckCircle,
  rejected: XCircle,
};

export default function AdminInquiriesPage() {
  const { checkPermission } = useAdminAuth();
  const [inquiries, setInquiries] = useState<Inquiry[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');

  useEffect(() => {
    fetchInquiries();
  }, []);

  const fetchInquiries = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/inquiries', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch inquiries: ${response.statusText}`);
      }

      const data = await response.json();
      setInquiries(data.inquiries || []);
    } catch (error) {
      console.error('Error fetching inquiries:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusUpdate = async (inquiryId: string, newStatus: string) => {
    if (!checkPermission('inquiries', 'update')) {
      alert('You do not have permission to update inquiry status');
      return;
    }

    try {
      const response = await fetch(`/api/admin/inquiries/${inquiryId}/status`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (response.ok) {
        // Update local state
        setInquiries(inquiries.map(inquiry =>
          inquiry.id === inquiryId
            ? { ...inquiry, status: newStatus as 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'accepted' | 'rejected', updated_at: new Date().toISOString() }
            : inquiry
        ));
      } else {
        console.error('Failed to update status');
      }
    } catch (error) {
      console.error('Error updating status:', error);
    }
  };

  const filteredInquiries = inquiries.filter(inquiry => {
    if (!searchTerm) {
      // If no search term, just filter by status and type
      const matchesStatus = statusFilter === 'all' || inquiry.status === statusFilter;
      const matchesType = typeFilter === 'all' || inquiry.type === typeFilter;
      return matchesStatus && matchesType;
    }

    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = (inquiry.contact_details?.fullName?.toLowerCase().includes(searchLower) || false) ||
                         (inquiry.contact_details?.email?.toLowerCase().includes(searchLower) || false) ||
                         (inquiry.products?.name?.toLowerCase().includes(searchLower) || false);

    const matchesStatus = statusFilter === 'all' || inquiry.status === statusFilter;
    const matchesType = typeFilter === 'all' || inquiry.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-[#956358] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                  <MessageSquare className="h-8 w-8 mr-3 text-[#956358]" />
                  Inquiry Management
                </h1>
                <p className="text-lg text-gray-600 mt-2">
                  Manage customer inquiries and track their status
                </p>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold text-[#956358]">{inquiries.length}</div>
                <div className="text-sm text-gray-600">Total Inquiries</div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white p-6 rounded-lg shadow-lg border border-gray-200">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Filter & Search</h3>
              <p className="text-sm text-gray-600">Find specific inquiries using the filters below</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              {/* Search */}
              <div className="relative">
                <label className="block text-sm font-semibold text-gray-800 mb-2">Search</label>
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search inquiries..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-3 w-full border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                />
              </div>

              {/* Status Filter */}
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">Status</label>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 bg-white focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                >
                  <option value="all" className="text-gray-900">All Status</option>
                  <option value="pending" className="text-gray-900">Pending</option>
                  <option value="in_progress" className="text-gray-900">In Progress</option>
                  <option value="accepted" className="text-gray-900">Accepted</option>
                  <option value="rejected" className="text-gray-900">Rejected</option>
                  <option value="completed" className="text-gray-900">Completed</option>
                  <option value="cancelled" className="text-gray-900">Cancelled</option>
                </select>
              </div>

              {/* Type Filter */}
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">Type</label>
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 bg-white focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                >
                  <option value="all" className="text-gray-900">All Types</option>
                  <option value="regular" className="text-gray-900">Regular</option>
                  <option value="bulk" className="text-gray-900">Bulk Order</option>
                  <option value="hot" className="text-gray-900">Hot</option>
                </select>
              </div>

              {/* Results Count */}
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">Results</label>
                <div className="flex items-center justify-center h-12 bg-gray-50 rounded-lg border border-gray-200">
                  <Filter className="h-5 w-5 mr-2 text-[#956358]" />
                  <span className="text-sm font-medium text-gray-900">
                    {filteredInquiries.length} of {inquiries.length}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Inquiries Table */}
          <div className="bg-white shadow-lg rounded-lg overflow-hidden border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
              <h3 className="text-lg font-semibold text-gray-900">Customer Inquiries</h3>
              <p className="text-sm text-gray-600 mt-1">View and manage all customer inquiries</p>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">
                      Customer & Product
                    </th>
                    <th className="px-6 py-4 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">
                      Type & Quantity
                    </th>
                    <th className="px-6 py-4 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-4 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">
                      Messages
                    </th>
                    <th className="px-6 py-4 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-4 text-left text-sm font-bold text-gray-800 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredInquiries.map((inquiry) => {
                const StatusIcon = statusIcons[inquiry.status];
                return (
                  <tr key={inquiry.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-12 w-12">
                          <div className="h-12 w-12 bg-gray-200 rounded-lg flex items-center justify-center">
                            <Package className="h-6 w-6 text-gray-500" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {inquiry.contact_details?.fullName || inquiry.contact_details?.phone || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {inquiry.contact_details?.email || 'N/A'}
                          </div>
                          <div className="text-sm text-gray-500">
                            {inquiry.products ? `${inquiry.products.brand} ${inquiry.products.model}` :
                             inquiry.type === 'hot' ? 'Private Products Access Request' : 'General Inquiry'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          inquiry.type === 'bulk' ? 'bg-purple-100 text-purple-800' :
                          inquiry.type === 'hot' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                        }`}>
                          {inquiry.type === 'bulk' ? 'Bulk Order' : inquiry.type === 'hot' ? 'Hot' : 'Regular'}
                        </span>
                      </div>
                      <div className="text-sm text-gray-500">
                        Qty: {inquiry.quantity}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <StatusIcon className="h-4 w-4 mr-2" />
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusColors[inquiry.status]}`}>
                          {inquiry.status.replace('_', ' ')}
                        </span>
                      </div>
                      {checkPermission('inquiries', 'update') && (
                        <select
                          value={inquiry.status}
                          onChange={(e) => handleStatusUpdate(inquiry.id, e.target.value)}
                          className="mt-2 text-sm border-2 border-gray-300 rounded-lg px-3 py-1 text-gray-900 bg-white focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                        >
                          <option value="pending" className="text-gray-900">Pending</option>
                          <option value="in_progress" className="text-gray-900">In Progress</option>
                          <option value="accepted" className="text-gray-900">Accepted</option>
                          <option value="rejected" className="text-gray-900">Rejected</option>
                          <option value="completed" className="text-gray-900">Completed</option>
                          <option value="cancelled" className="text-gray-900">Cancelled</option>
                        </select>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900">
                        <MessageSquare className="h-4 w-4 mr-2 text-gray-400" />
                        {inquiry.message_count || 0}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2" />
                        {new Date(inquiry.created_at).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-400">
                        Updated: {new Date(inquiry.updated_at).toLocaleDateString()}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end">
                        <Link
                          href={`/admin/inquiries/${inquiry.id}`}
                          className="inline-flex items-center px-3 py-2 text-sm font-medium text-[#956358] bg-[#956358]/10 rounded-lg hover:bg-[#956358]/20 transition-colors"
                          title="View Details"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Link>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Empty State */}
      {filteredInquiries.length === 0 && (
        <div className="bg-white shadow-lg rounded-lg border border-gray-200 p-12">
          <div className="text-center">
            <AlertCircle className="mx-auto h-16 w-16 text-gray-400" />
            <h3 className="mt-4 text-xl font-semibold text-gray-900">No inquiries found</h3>
            <p className="mt-2 text-lg text-gray-600">
              {searchTerm || statusFilter !== 'all' || typeFilter !== 'all'
                ? 'Try adjusting your search or filter criteria.'
                : 'No inquiries have been submitted yet.'}
            </p>
            <div className="mt-6">
              <button
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('all');
                  setTypeFilter('all');
                }}
                className="inline-flex items-center px-4 py-2 bg-[#956358] text-white font-medium rounded-lg hover:bg-[#7d5249] transition-colors"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      )}
        </div>
      </div>
    </div>
  );
}
