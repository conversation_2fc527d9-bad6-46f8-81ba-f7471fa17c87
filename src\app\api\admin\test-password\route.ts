import { NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    // This is a development-only endpoint for testing password hashing
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    const supabase = createSupabaseAdminClient();

    // Get the admin user from database
    const { data: adminUser, error } = await supabase
      .from('admin_users')
      .select('email, password_hash')
      .eq('email', '<EMAIL>')
      .single();

    if (error || !adminUser) {
      return NextResponse.json({ error: 'Admin user not found', details: error });
    }

    // Test password comparison
    const testPassword = 'admin123';
    const isValid = await bcrypt.compare(testPassword, adminUser.password_hash);

    // Create a new hash for comparison
    const newHash = await bcrypt.hash(testPassword, 10);
    const isNewHashValid = await bcrypt.compare(testPassword, newHash);

    return NextResponse.json({
      message: 'Password test results',
      email: adminUser.email,
      storedHashLength: adminUser.password_hash.length,
      storedHashPrefix: adminUser.password_hash.substring(0, 10),
      testPassword: testPassword,
      isValidWithStoredHash: isValid,
      newHashTest: isNewHashValid,
      newHashLength: newHash.length,
      newHashPrefix: newHash.substring(0, 10)
    });

  } catch (error) {
    console.error('Password test error:', error);
    return NextResponse.json(
      { error: 'Password test failed', details: error },
      { status: 500 }
    );
  }
}
