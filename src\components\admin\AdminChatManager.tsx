'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  MessageCircle,
  Search,
  Calendar,
  User,
  RefreshCw,
  Eye
} from 'lucide-react';
import { Input } from '@/components/ui/input';

interface InquiryWithChat {
  id: string;
  product_id: string;
  inquiry_type: 'regular' | 'bulk';
  quantity: number;
  status: 'pending' | 'in_progress' | 'accepted' | 'rejected' | 'completed';
  user_details: {
    full_name: string;
    email: string;
    phone: string;
    whatsapp_number: string;
    address: string;
  };
  created_at: string;
  updated_at: string;
  product: {
    id: string;
    name: string;
    brand: string;
    model_name: string;
    price: number;
    images: string[];
  };
  message_count: number;
  last_message_at?: string;
  unread_messages: number;
}

export function AdminChatManager() {
  const [inquiries, setInquiries] = useState<InquiryWithChat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  // Fetch inquiries with chat data
  const fetchInquiries = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/admin/chats', {
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch inquiries: ${response.statusText}`);
      }

      const data = await response.json();
      setInquiries(data.inquiries || []);
    } catch (err) {
      console.error('Error fetching inquiries:', err);
      setError(err instanceof Error ? err.message : 'Failed to load inquiries');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchInquiries();
  }, []);

  // Filter inquiries based on search and status
  const filteredInquiries = inquiries.filter(inquiry => {
    const matchesSearch = 
      inquiry.user_details.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      inquiry.user_details.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      inquiry.product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      inquiry.id.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' || inquiry.status === statusFilter;

    return matchesSearch && matchesStatus;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'in_progress':
        return 'text-blue-400 bg-blue-400/10 border-blue-400/20';
      case 'accepted':
        return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'completed':
        return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'rejected':
        return 'text-red-400 bg-red-400/10 border-red-400/20';
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#1a2b4a] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading chat inquiries...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#1a2b4a] py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Chat Management</h1>
              <p className="text-gray-300">Manage customer inquiries and chat conversations</p>
            </div>
            <Button
              onClick={fetchInquiries}
              className="bg-gradient-to-r from-[#956358] to-[#f9c1b2] hover:shadow-lg hover:shadow-[#956358]/25"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>

          {/* Filters */}
          <Card className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by customer name, email, product, or ID..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-[#1a2b4a] border-gray-600 focus:border-[#f9c1b2] text-white"
                  />
                </div>
              </div>
              
              <div className="sm:w-48">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="w-full px-3 py-2 bg-[#1a2b4a] border border-gray-600 rounded-lg text-white focus:border-[#f9c1b2] focus:outline-none"
                >
                  <option value="all">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="in_progress">In Progress</option>
                  <option value="accepted">Accepted</option>
                  <option value="completed">Completed</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
            </div>
          </Card>
        </div>

        {/* Error State */}
        {error && (
          <Card className="p-6 mb-6">
            <div className="text-center text-red-400">
              <p>{error}</p>
              <Button
                onClick={fetchInquiries}
                className="mt-4 bg-gradient-to-r from-[#956358] to-[#f9c1b2]"
              >
                Try Again
              </Button>
            </div>
          </Card>
        )}

        {/* Inquiries List */}
        <div className="space-y-4">
          {filteredInquiries.length === 0 ? (
            <Card className="p-12">
              <div className="text-center">
                <MessageCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-white mb-2">No Inquiries Found</h3>
                <p className="text-gray-400">
                  {searchQuery || statusFilter !== 'all' 
                    ? 'Try adjusting your search or filters'
                    : 'No customer inquiries to display'}
                </p>
              </div>
            </Card>
          ) : (
            filteredInquiries.map((inquiry) => (
              <Card key={inquiry.id} className="p-6 hover:bg-[#1e2f4f] transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    {/* Product Image */}
                    <div className="w-16 h-16 bg-gray-700 rounded-lg overflow-hidden flex-shrink-0">
                      {inquiry.product?.images?.[0] ? (
                        <Image
                          src={inquiry.product.images[0]}
                          alt={inquiry.product.name || 'Product'}
                          width={64}
                          height={64}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <MessageCircle className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Inquiry Details */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-white truncate">
                          {inquiry.user_details.full_name}
                        </h3>
                        <Badge className={`px-3 py-1 text-xs font-medium rounded-full border ${getStatusColor(inquiry.status)}`}>
                          {inquiry.status.replace('_', ' ').toUpperCase()}
                        </Badge>
                        {inquiry.unread_messages > 0 && (
                          <Badge className="bg-red-500 text-white">
                            {inquiry.unread_messages} new
                          </Badge>
                        )}
                      </div>

                      <p className="text-gray-300 text-sm mb-1">
                        {inquiry.product?.brand || 'Unknown Brand'} {inquiry.product?.model_name || ''}
                      </p>

                      <div className="flex items-center space-x-4 text-sm text-gray-400">
                        <span className="flex items-center space-x-1">
                          <User className="h-4 w-4" />
                          <span>{inquiry.user_details.email}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <MessageCircle className="h-4 w-4" />
                          <span>{inquiry.message_count} messages</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(inquiry.created_at)}</span>
                        </span>
                      </div>
                    </div>

                    {/* Price and Actions */}
                    <div className="text-right flex-shrink-0">
                      <div className="text-lg font-bold text-[#f9c1b2] mb-3">
                        {inquiry.product?.price ? formatPrice(inquiry.product.price * inquiry.quantity) : 'Price unavailable'}
                      </div>
                      <Button
                        onClick={() => window.open(`/admin/chats/${inquiry.id}`, '_blank')}
                        className="bg-gradient-to-r from-[#956358] to-[#f9c1b2] hover:shadow-lg hover:shadow-[#956358]/25"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Chat
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>

        {/* Summary Stats */}
        {filteredInquiries.length > 0 && (
          <Card className="p-6 mt-8">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-white">{filteredInquiries.length}</div>
                <div className="text-sm text-gray-400">Total Inquiries</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-yellow-400">
                  {filteredInquiries.filter(i => i.status === 'pending').length}
                </div>
                <div className="text-sm text-gray-400">Pending</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-400">
                  {filteredInquiries.filter(i => i.status === 'in_progress').length}
                </div>
                <div className="text-sm text-gray-400">In Progress</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-400">
                  {filteredInquiries.reduce((sum, i) => sum + i.unread_messages, 0)}
                </div>
                <div className="text-sm text-gray-400">Unread Messages</div>
              </div>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}
