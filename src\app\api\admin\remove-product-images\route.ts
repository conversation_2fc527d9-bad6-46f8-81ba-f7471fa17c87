import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';
import { verifyAdminAuth } from '@/lib/auth/admin';

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createSupabaseAdminClient();
    const body = await request.json();
    const { 
      removeFromDatabase = true, 
      deleteFromStorage = false, 
      backupData = true,
      productIds = [] // Optional: specify specific product IDs, empty array means all products
    } = body;

    let results = {
      success: false,
      message: '',
      backup: null as any,
      databaseUpdated: false,
      storageDeleted: false,
      errors: [] as string[]
    };

    // Step 1: Get all products with images (for backup and processing)
    let query = supabase
      .from('products')
      .select('id, name, images');

    // If specific product IDs are provided, filter by them
    if (productIds.length > 0) {
      query = query.in('id', productIds);
    }

    const { data: products, error: fetchError } = await query;

    if (fetchError) {
      console.error('Error fetching products:', fetchError);
      return NextResponse.json(
        { error: 'Failed to fetch products', details: fetchError },
        { status: 500 }
      );
    }

    if (!products || products.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No products found to process',
        backup: null,
        databaseUpdated: false,
        storageDeleted: false
      });
    }

    // Step 2: Create backup of image data
    let backupData_result = null;
    if (backupData) {
      backupData_result = {
        timestamp: new Date().toISOString(),
        totalProducts: products.length,
        productsWithImages: products.filter(p => p.images && Array.isArray(p.images) && p.images.length > 0).length,
        products: products.map(product => ({
          id: product.id,
          name: product.name,
          images: product.images || []
        }))
      };
      results.backup = backupData_result;
    }

    // Step 3: Collect all image URLs for storage deletion
    let allImageUrls: string[] = [];
    if (deleteFromStorage) {
      products.forEach(product => {
        if (product.images && Array.isArray(product.images)) {
          allImageUrls.push(...product.images);
        }
      });
    }

    // Step 4: Remove images from database
    if (removeFromDatabase) {
      try {
        let updateQuery = supabase
          .from('products')
          .update({ 
            images: [],
            updated_at: new Date().toISOString()
          });

        // If specific product IDs are provided, filter by them
        if (productIds.length > 0) {
          updateQuery = updateQuery.in('id', productIds);
        } else {
          // Update all products
          updateQuery = updateQuery.neq('id', '00000000-0000-0000-0000-000000000000'); // This will match all products
        }

        const { error: updateError } = await updateQuery;

        if (updateError) {
          console.error('Error updating products:', updateError);
          results.errors.push(`Database update failed: ${updateError.message}`);
        } else {
          results.databaseUpdated = true;
        }
      } catch (error) {
        console.error('Database update error:', error);
        results.errors.push(`Database update error: ${error}`);
      }
    }

    // Step 5: Delete images from storage
    if (deleteFromStorage && allImageUrls.length > 0) {
      try {
        // Extract file paths from URLs
        const imagePaths = allImageUrls.map((imageUrl: string) => {
          // Extract the file path from the Supabase storage URL
          // URL format: https://[project].supabase.co/storage/v1/object/public/product-images/[path]
          const urlParts = imageUrl.split('/storage/v1/object/public/product-images/');
          return urlParts.length > 1 ? urlParts[1] : null;
        }).filter(Boolean); // Remove null values

        if (imagePaths.length > 0) {
          const { error: storageError } = await supabase.storage
            .from('product-images')
            .remove(imagePaths);

          if (storageError) {
            console.error('Error deleting images from storage:', storageError);
            results.errors.push(`Storage deletion failed: ${storageError.message}`);
          } else {
            results.storageDeleted = true;
          }
        }
      } catch (error) {
        console.error('Storage deletion error:', error);
        results.errors.push(`Storage deletion error: ${error}`);
      }
    }

    // Step 6: Determine overall success
    results.success = results.errors.length === 0;
    
    if (results.success) {
      let message = 'Image removal completed successfully. ';
      if (results.databaseUpdated) message += 'Database updated. ';
      if (results.storageDeleted) message += 'Storage files deleted. ';
      if (results.backup) message += 'Backup created. ';
      results.message = message.trim();
    } else {
      results.message = 'Image removal completed with errors. Check the errors array for details.';
    }

    return NextResponse.json(results);

  } catch (error) {
    console.error('Remove images error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error,
        success: false,
        message: 'Failed to remove images due to server error'
      },
      { status: 500 }
    );
  }
}
