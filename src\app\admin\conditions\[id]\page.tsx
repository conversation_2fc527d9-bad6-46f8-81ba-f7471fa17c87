'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { ArrowLeft, Edit, Trash2, Star, Calendar, Globe } from 'lucide-react';

interface Condition {
  id: string;
  name: string;
  slug: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export default function ConditionViewPage() {
  const router = useRouter();
  const params = useParams();
  const conditionId = params.id as string;
  
  const [condition, setCondition] = useState<Condition | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleteModal, setDeleteModal] = useState(false);

  useEffect(() => {
    if (conditionId) {
      fetchCondition();
    }
  }, [conditionId]);

  const fetchCondition = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/conditions/${conditionId}`);
      if (response.ok) {
        const data = await response.json();
        setCondition(data.condition);
      } else {
        console.error('Failed to fetch condition');
        router.push('/admin/conditions');
      }
    } catch (error) {
      console.error('Error fetching condition:', error);
      router.push('/admin/conditions');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!condition) return;
    
    try {
      const response = await fetch(`/api/admin/conditions/${condition.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        alert('Condition deleted successfully!');
        router.push('/admin/conditions');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error('Error deleting condition:', error);
      alert('Failed to delete condition');
    } finally {
      setDeleteModal(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-[#956358]"></div>
        <p className="ml-3 text-gray-600">Loading condition...</p>
      </div>
    );
  }

  if (!condition) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900">Condition not found</h2>
        <p className="text-gray-600 mt-2">The condition you're looking for doesn't exist.</p>
        <button
          onClick={() => router.push('/admin/conditions')}
          className="mt-4 inline-flex items-center px-4 py-2 bg-gradient-to-r from-[#956358] to-[#f9c1b2] text-white text-sm font-medium rounded-md hover:from-[#956358]/90 hover:to-[#f9c1b2]/90"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Conditions
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{condition.name}</h1>
            <p className="text-gray-600">Condition Details</p>
          </div>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => router.push(`/admin/conditions/${condition.id}/edit`)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
          >
            <Edit className="h-4 w-4 mr-2" />
            Edit Condition
          </button>
          <button
            onClick={() => setDeleteModal(true)}
            className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 transition-colors"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </button>
        </div>
      </div>

      {/* Condition Information */}
      <div className="bg-white shadow-lg rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Condition Information</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Condition Name and Icon */}
            <div className="md:col-span-2">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  <div className="h-16 w-16 rounded-full bg-gradient-to-r from-[#956358] to-[#f9c1b2] flex items-center justify-center">
                    <Star className="h-8 w-8 text-white" />
                  </div>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-900">{condition.name}</h2>
                  <p className="text-gray-500 flex items-center mt-1">
                    <Globe className="h-4 w-4 mr-1" />
                    {condition.slug}
                  </p>
                </div>
              </div>
            </div>

            {/* Description */}
            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-900">
                  {condition.description || 'No description provided'}
                </p>
              </div>
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <span className={`inline-flex px-3 py-1 text-sm font-semibold rounded-full ${
                condition.is_active
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {condition.is_active ? 'Active' : 'Inactive'}
              </span>
            </div>

            {/* Created Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Created Date
              </label>
              <div className="flex items-center text-gray-900">
                <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                {new Date(condition.created_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
            </div>

            {/* Updated Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Last Updated
              </label>
              <div className="flex items-center text-gray-900">
                <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                {new Date(condition.updated_at).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {deleteModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <Trash2 className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Delete Condition</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete "{condition.name}"? This action cannot be undone.
                </p>
              </div>
              <div className="flex justify-center space-x-4 mt-4">
                <button
                  onClick={() => setDeleteModal(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-800 text-sm font-medium rounded-md hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700"
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
