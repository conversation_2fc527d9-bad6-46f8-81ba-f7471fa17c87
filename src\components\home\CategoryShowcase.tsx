'use client';

import { useState } from 'react';
import Link from 'next/link';
import { X, ExternalLink } from 'lucide-react';
import { getProductUrl } from '@/lib/product-utils';

interface Product {
  id: string;
  name: string;
  price: number;
  images: string[];
  brand: string;
  model: string;
  show_price?: boolean;
  slug: string;
}

interface CategoryData {
  id: string;
  name: string;
  slug: string;
  image: string;
  products: Product[];
}

interface CategoryShowcaseProps {
  categories: CategoryData[];
}

export function CategoryShowcase({ categories }: CategoryShowcaseProps) {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);

  const handleProductClick = (product: Product) => {
    setSelectedProduct(product);
  };

  const closeProductModal = () => {
    setSelectedProduct(null);
  };

  if (!categories || categories.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-[var(--text-secondary)]">No categories available</p>
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {categories.map((category) => (
          <div
            key={category.id}
            className="relative group"
            onMouseEnter={() => setHoveredCategory(category.id)}
            onMouseLeave={() => setHoveredCategory(null)}
          >
            {/* Category Card */}
            <div className="relative h-80 bg-[var(--background-card)] rounded-2xl overflow-hidden shadow-2xl border border-[var(--shadow-grey)] transition-all duration-500 group-hover:shadow-glow group-hover:scale-105">
              {/* Category Image */}
              <div className="absolute inset-0">
                <img
                  src={category.image}
                  alt={category.name}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-[var(--background-primary)]/80 via-transparent to-transparent" />
              </div>

              {/* Category Info */}
              <div className="absolute bottom-0 left-0 right-0 p-6 z-10">
                <h3 className="text-2xl font-bold text-[var(--text-primary)] mb-2">
                  {category.name}
                </h3>
                <p className="text-[var(--text-secondary)]">
                  {category.products.length} products available
                </p>
              </div>

              {/* Hover Overlay */}
              <div className={`absolute inset-0 bg-[var(--background-primary)]/90 transition-opacity duration-300 ${
                hoveredCategory === category.id ? 'opacity-100' : 'opacity-0 pointer-events-none'
              }`}>
                <div className="p-4 h-full overflow-y-auto">
                  <h4 className="text-lg font-bold text-[var(--text-primary)] mb-4 sticky top-0 bg-[var(--background-primary)] py-2">
                    {category.name} Products
                  </h4>
                  
                  {/* Product Gallery */}
                  <div className="grid grid-cols-2 gap-3">
                    {category.products.slice(0, 10).map((product) => (
                      <div
                        key={product.id}
                        onClick={() => handleProductClick(product)}
                        className="bg-[var(--background-card)] rounded-lg p-3 cursor-pointer transition-all duration-300 hover:bg-[var(--shadow-grey)] hover:scale-105 group/product"
                      >
                        <div className="aspect-square bg-[var(--shadow-grey)] rounded-lg mb-2 overflow-hidden">
                          {product.images && product.images.length > 0 ? (
                            <img
                              src={product.images[0]}
                              alt={product.name}
                              className="w-full h-full object-cover transition-transform duration-300 group-hover/product:scale-110"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center text-[var(--text-secondary)]">
                              <span className="text-2xl">📱</span>
                            </div>
                          )}
                        </div>
                        <h5 className="text-xs font-semibold text-[var(--text-primary)] line-clamp-1 mb-1">
                          {product.name}
                        </h5>
                        <p className="text-xs text-[var(--text-secondary)] line-clamp-1 mb-1">
                          {product.brand}
                        </p>
                        {product.show_price ? (
                          <p className="text-xs font-bold text-[var(--accent-primary)]">
                            ₹{product.price.toLocaleString('en-IN')}
                          </p>
                        ) : (
                          <p className="text-xs font-bold text-[var(--accent-primary)]">
                            Contact for Price
                          </p>
                        )}
                      </div>
                    ))}
                  </div>

                  {category.products.length > 10 && (
                    <div className="mt-4 text-center">
                      <Link href={`/products?category=${category.slug}`}>
                        <button className="btn-primary text-sm px-4 py-2">
                          View All {category.products.length} Products
                        </button>
                      </Link>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Product Modal */}
      {selectedProduct && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-[var(--background-card)] rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto border border-[var(--shadow-grey)] shadow-2xl">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-[var(--shadow-grey)]">
              <h3 className="text-xl font-bold text-[var(--text-primary)]">Product Details</h3>
              <button
                onClick={closeProductModal}
                className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-[var(--shadow-grey)] text-[var(--text-secondary)] hover:text-[var(--text-primary)] transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              <div className="grid md:grid-cols-2 gap-6">
                {/* Product Image */}
                <div className="aspect-square bg-[var(--shadow-grey)] rounded-lg overflow-hidden">
                  {selectedProduct.images && selectedProduct.images.length > 0 ? (
                    <img
                      src={selectedProduct.images[0]}
                      alt={selectedProduct.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-[var(--text-secondary)]">
                      <span className="text-6xl">📱</span>
                    </div>
                  )}
                </div>

                {/* Product Info */}
                <div className="space-y-4">
                  <div>
                    <h4 className="text-2xl font-bold text-[var(--text-primary)] mb-2">
                      {selectedProduct.name}
                    </h4>
                    <p className="text-lg text-[var(--accent-primary)]">
                      {selectedProduct.brand} • {selectedProduct.model}
                    </p>
                  </div>

                  {selectedProduct.show_price ? (
                    <div className="text-3xl font-bold text-gradient">
                      ₹{selectedProduct.price.toLocaleString('en-IN')}
                    </div>
                  ) : (
                    <div className="text-2xl font-bold text-[var(--accent-primary)]">
                      Contact for Price
                    </div>
                  )}

                  {/* Additional Images */}
                  {selectedProduct.images && selectedProduct.images.length > 1 && (
                    <div>
                      <h5 className="text-sm font-semibold text-[var(--text-primary)] mb-2">More Images</h5>
                      <div className="flex space-x-2 overflow-x-auto">
                        {selectedProduct.images.slice(1, 4).map((image, index) => (
                          <div key={index} className="w-16 h-16 bg-[var(--shadow-grey)] rounded-lg overflow-hidden flex-shrink-0">
                            <img
                              src={image}
                              alt={`${selectedProduct.name} ${index + 2}`}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex space-x-3 pt-4">
                    <Link href={getProductUrl(selectedProduct.slug)} className="flex-1">
                      <button className="btn-primary w-full flex items-center justify-center space-x-2">
                        <ExternalLink className="h-4 w-4" />
                        <span>View Full Details</span>
                      </button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
