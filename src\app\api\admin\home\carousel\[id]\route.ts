import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabase = createSupabaseAdminClient();

    const { data: carouselProduct, error } = await supabase
      .from('home_carousel_products')
      .select(`
        *,
        products (
          id,
          name,
          price,
          images,
          brand,
          model,
          description,
          show_price
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching carousel product:', error);
      return NextResponse.json({ error: 'Carousel product not found' }, { status: 404 });
    }

    return NextResponse.json(carouselProduct);
  } catch (error) {
    console.error('Error in carousel product GET:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabase = createSupabaseAdminClient();
    const body = await request.json();
    const { is_active, display_order, custom_image, custom_description, custom_background_image } = body;

    const updateData: any = {};
    if (typeof is_active === 'boolean') updateData.is_active = is_active;
    if (typeof display_order === 'number') updateData.display_order = display_order;
    if (custom_image !== undefined) updateData.custom_image = custom_image;
    if (custom_description !== undefined) updateData.custom_description = custom_description;
    if (custom_background_image !== undefined) updateData.custom_background_image = custom_background_image;

    const { data, error } = await supabase
      .from('home_carousel_products')
      .update(updateData)
      .eq('id', id)
      .select(`
        id,
        product_id,
        display_order,
        custom_image,
        custom_description,
        custom_background_image,
        is_active,
        products (
          id,
          name,
          price,
          images,
          brand,
          model,
          description,
          show_price
        )
      `)
      .single();

    if (error) {
      console.error('Error updating carousel product:', error);
      return NextResponse.json({ error: 'Failed to update carousel product' }, { status: 500 });
    }

    return NextResponse.json({ product: data });

  } catch (error) {
    console.error('Error in carousel product PATCH:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabase = createSupabaseAdminClient();

    const { error } = await supabase
      .from('home_carousel_products')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting carousel product:', error);
      return NextResponse.json({ error: 'Failed to delete carousel product' }, { status: 500 });
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error in carousel product DELETE:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
