import { NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';

// Mock database for development testing
const mockDatabase: {
  roles: Array<{ id: string; name: string; permissions: Array<{ resource: string; actions: string[] }> }>;
  admin_users: Array<{ id: string; email: string; password_hash: string; role_id: string; full_name: string; is_active: boolean }>;
} = {
  roles: [],
  admin_users: []
};

export async function POST() {
  try {
    // This is a development-only endpoint for setting up initial admin users
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    // Create default roles if they don't exist
    const roles = [
      {
        id: 'b4f1c8d2-9a7e-4f2b-8c3d-6e5f9a8b7c6d',
        name: 'Super Admin',
        permissions: [
          {"resource": "products", "actions": ["create", "read", "update", "delete"]},
          {"resource": "inquiries", "actions": ["create", "read", "update", "delete"]},
          {"resource": "users", "actions": ["create", "read", "update", "delete"]},
          {"resource": "roles", "actions": ["create", "read", "update", "delete"]},
          {"resource": "chat", "actions": ["create", "read", "update", "delete"]},
          {"resource": "inventory", "actions": ["create", "read", "update", "delete"]}
        ]
      },
      {
        id: 'c5g2d9e3-ab8f-5g3c-9d4e-7f6g0b9c8d7e',
        name: 'Admin',
        permissions: [
          {"resource": "products", "actions": ["create", "read", "update", "delete"]},
          {"resource": "inquiries", "actions": ["read", "update"]},
          {"resource": "users", "actions": ["read"]},
          {"resource": "chat", "actions": ["create", "read", "update"]},
          {"resource": "inventory", "actions": ["read", "update"]}
        ]
      },
      {
        id: 'd6h3e0f4-bc9g-6h4d-ae5f-8g7h1c0d9e8f',
        name: 'Support',
        permissions: [
          {"resource": "inquiries", "actions": ["read", "update"]},
          {"resource": "chat", "actions": ["create", "read", "update"]}
        ]
      }
    ];

    // Store roles in mock database
    mockDatabase.roles = roles;

    // Create default admin users
    const adminPassword = await bcrypt.hash('admin123', 10);
    const supportPassword = await bcrypt.hash('support123', 10);

    const adminUsers = [
      {
        id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
        email: '<EMAIL>',
        password_hash: adminPassword,
        role_id: 'b4f1c8d2-9a7e-4f2b-8c3d-6e5f9a8b7c6d',
        full_name: 'System Administrator',
        is_active: true
      },
      {
        id: 'b2c3d4e5-f6g7-8h9i-0j1k-l2m3n4o5p6q7',
        email: '<EMAIL>',
        password_hash: supportPassword,
        role_id: 'd6h3e0f4-bc9g-6h4d-ae5f-8g7h1c0d9e8f',
        full_name: 'Support Admin',
        is_active: true
      }
    ];

    // Store users in mock database
    mockDatabase.admin_users = adminUsers;

    return NextResponse.json({
      message: 'Admin users and roles created successfully',
      users: [
        { email: '<EMAIL>', password: 'admin123', role: 'Super Admin' },
        { email: '<EMAIL>', password: 'support123', role: 'Support' }
      ]
    });

  } catch (error) {
    console.error('Setup error:', error);
    return NextResponse.json(
      { error: 'Failed to setup admin users' },
      { status: 500 }
    );
  }
}

// Export the mock database for other endpoints to use
export { mockDatabase };
