import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const imageUrl = searchParams.get('url');

    if (!imageUrl) {
      return NextResponse.json(
        { error: 'Image URL is required' },
        { status: 400 }
      );
    }

    const supabase = createSupabaseAdminClient();

    // Extract the file path from the URL
    // URL format: https://project.supabase.co/storage/v1/object/public/bucket/path
    const urlParts = imageUrl.split('/storage/v1/object/public/');
    if (urlParts.length !== 2) {
      return NextResponse.json(
        { error: 'Invalid image URL format' },
        { status: 400 }
      );
    }

    const [bucket, ...pathParts] = urlParts[1].split('/');
    const filePath = pathParts.join('/');

    console.log('Deleting image:', { bucket, filePath });

    // Delete the file from Supabase storage
    const { error } = await supabase.storage
      .from(bucket)
      .remove([filePath]);

    if (error) {
      console.error('Error deleting image from storage:', error);
      return NextResponse.json(
        { error: 'Failed to delete image from storage', details: error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Image deleted successfully',
      deletedPath: filePath
    });

  } catch (error) {
    console.error('Delete image error:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    );
  }
}
