import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { mobile, name, companyName, email } = await request.json();

    if (!mobile) {
      return NextResponse.json(
        { error: 'Mobile number is required' },
        { status: 400 }
      );
    }

    // Basic mobile number validation
    const mobileRegex = /^[6-9]\d{9}$/;
    if (!mobileRegex.test(mobile.trim())) {
      return NextResponse.json(
        { error: 'Please enter a valid 10-digit mobile number' },
        { status: 400 }
      );
    }

    const supabase = createSupabaseAdminClient();

    // Check if user exists with this mobile/email
    let userId = null;
    if (email) {
      const { data: existingUser } = await supabase
        .from('users')
        .select('id')
        .eq('email', email)
        .single();
      
      if (existingUser) {
        userId = existingUser.id;
      }
    }

    // Create the hot inquiry
    const { data: inquiry, error: inquiryError } = await supabase
      .from('inquiries')
      .insert({
        user_id: userId,
        product_id: null, // No specific product for hot inquiries
        type: 'hot',
        subject: 'Private Products Access Request',
        message: `Customer requesting access to private products. Contact details: Mobile: ${mobile}${name ? `, Name: ${name}` : ''}${companyName ? `, Company: ${companyName}` : ''}${email ? `, Email: ${email}` : ''}`,
        quantity: 1,
        contact_details: {
          fullName: name || '',
          email: email || '',
          phone: mobile,
          companyName: companyName || ''
        },
        urgency_level: 'high',
        preferred_contact_method: 'phone',
        status: 'pending'
      })
      .select('*')
      .single();

    if (inquiryError) {
      console.error('Error creating hot inquiry:', inquiryError);
      return NextResponse.json(
        { error: 'Failed to submit request' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      inquiry,
      message: 'Your request has been submitted successfully. Our team will contact you soon.'
    });

  } catch (error) {
    console.error('Error in hot inquiry API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
