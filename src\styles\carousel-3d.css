/* 3D Carousel Styles */

.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.rotate-y-45 {
  transform: rotateY(45deg);
}

.rotate-y-neg-45 {
  transform: rotateY(-45deg);
}

/* Smooth transitions for 3D transforms */
.carousel-slide {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center center;
}

.carousel-slide-prev {
  transform: translateX(-70%) scale(0.75) rotateY(45deg);
  opacity: 0.6;
  z-index: 1;
}

.carousel-slide-current {
  transform: translateX(0) scale(1) rotateY(0deg);
  opacity: 1;
  z-index: 10;
}

.carousel-slide-next {
  transform: translateX(70%) scale(0.75) rotateY(-45deg);
  opacity: 0.6;
  z-index: 1;
}

/* Transition states */
.carousel-slide-transitioning-left {
  transform: translateX(-100%) scale(0.75) rotateY(0deg);
  opacity: 0.6;
}

.carousel-slide-transitioning-right {
  transform: translateX(100%) scale(0.75) rotateY(0deg);
  opacity: 0.6;
}

.carousel-slide-transitioning-center {
  transform: translateX(0) scale(1.05) rotateY(0deg);
  opacity: 1;
}

/* Enhanced shadow effects for depth */
.carousel-slide-shadow {
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05);
}

.carousel-slide-shadow-deep {
  box-shadow: 
    0 35px 60px -12px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Backdrop blur effects */
.backdrop-blur-carousel {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* Animation keyframes */
@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%) scale(0.75) rotateY(45deg);
    opacity: 0;
  }
  to {
    transform: translateX(-70%) scale(0.75) rotateY(45deg);
    opacity: 0.6;
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%) scale(0.75) rotateY(-45deg);
    opacity: 0;
  }
  to {
    transform: translateX(70%) scale(0.75) rotateY(-45deg);
    opacity: 0.6;
  }
}

@keyframes slideToCenter {
  from {
    transform: translateX(-70%) scale(0.75) rotateY(45deg);
    opacity: 0.6;
  }
  to {
    transform: translateX(0) scale(1) rotateY(0deg);
    opacity: 1;
  }
}

@keyframes slideFromCenter {
  from {
    transform: translateX(0) scale(1) rotateY(0deg);
    opacity: 1;
  }
  to {
    transform: translateX(70%) scale(0.75) rotateY(-45deg);
    opacity: 0.6;
  }
}

/* Hover effects */
.carousel-nav-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.carousel-nav-button:hover {
  transform: scale(1.1);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.carousel-nav-button:active {
  transform: scale(0.95);
}

/* Progress bar animation */
.carousel-progress {
  transition: width 0.1s ease-linear;
}

/* Indicator animations */
.carousel-indicator {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.carousel-indicator:hover {
  transform: scale(1.1);
}

.carousel-indicator.active {
  transform: scale(1.25);
  box-shadow: 0 0 20px rgba(var(--accent-primary-rgb), 0.5);
}

/* Content fade animations */
.carousel-content {
  animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Button hover effects */
.carousel-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.carousel-button:hover {
  transform: scale(1.05);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.carousel-button:active {
  transform: scale(0.98);
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .carousel-slide-prev {
    transform: translateX(-85%) scale(0.6) rotateY(30deg);
    opacity: 0.4;
  }
  
  .carousel-slide-next {
    transform: translateX(85%) scale(0.6) rotateY(-30deg);
    opacity: 0.4;
  }
  
  .carousel-content {
    padding: 1rem;
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .carousel-slide,
  .carousel-nav-button,
  .carousel-indicator,
  .carousel-button {
    transition: none;
  }
  
  .carousel-content {
    animation: none;
  }
}
