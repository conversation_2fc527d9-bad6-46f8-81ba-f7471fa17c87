import { Database } from './database'

// Database table types
export type User = Database['public']['Tables']['users']['Row']
export type UserInsert = Database['public']['Tables']['users']['Insert']
export type UserUpdate = Database['public']['Tables']['users']['Update']

export type AdminUser = Database['public']['Tables']['admin_users']['Row']
export type AdminUserInsert = Database['public']['Tables']['admin_users']['Insert']
export type AdminUserUpdate = Database['public']['Tables']['admin_users']['Update']

export type Role = Database['public']['Tables']['roles']['Row']
export type RoleInsert = Database['public']['Tables']['roles']['Insert']
export type RoleUpdate = Database['public']['Tables']['roles']['Update']

export type Category = Database['public']['Tables']['categories']['Row']
export type CategoryInsert = Database['public']['Tables']['categories']['Insert']
export type CategoryUpdate = Database['public']['Tables']['categories']['Update']

export type Product = Database['public']['Tables']['products']['Row']
export type ProductInsert = Database['public']['Tables']['products']['Insert']
export type ProductUpdate = Database['public']['Tables']['products']['Update']

export type Inquiry = Database['public']['Tables']['inquiries']['Row']
export type InquiryInsert = Database['public']['Tables']['inquiries']['Insert']
export type InquiryUpdate = Database['public']['Tables']['inquiries']['Update']

export type ChatMessage = Database['public']['Tables']['chat_messages']['Row']
export type ChatMessageInsert = Database['public']['Tables']['chat_messages']['Insert']
export type ChatMessageUpdate = Database['public']['Tables']['chat_messages']['Update']

// Extended types with relations
export interface ProductWithCategory extends Product {
  category: Category
}

export interface InquiryWithProduct extends Inquiry {
  product: Product
  user: User
}

export interface InquiryWithMessages extends InquiryWithProduct {
  messages: ChatMessage[]
}

// Form types
export interface ProductFormData {
  name: string
  brand: string
  model_name: string
  category_id: string
  screen_size?: string
  color?: string
  storage?: string
  ram?: string
  operating_system?: string
  graphics_card?: string
  special_features?: string[]
  additional_features?: {
    webcam?: boolean
    fingerprint_sensor?: boolean
    keyboard?: string
    pointer_device?: string
  }
  description?: string
  price: number
  stock_quantity: number
  low_stock_threshold: number
  images?: string[]
}

export interface InquiryFormData {
  product_id: string
  inquiry_type: 'regular' | 'bulk' | 'hot'
  quantity: number
  user_details: {
    full_name: string
    email: string
    phone: string
    whatsapp_number: string
    address: string
  }
}

export interface UserRegistrationData {
  email: string
  full_name: string
  phone?: string
  whatsapp_number?: string
  address?: string
}

// Filter types
export interface ProductFilters {
  category?: string
  brand?: string
  min_price?: number
  max_price?: number
  storage?: string
  ram?: string
  operating_system?: string
  search?: string
}

export interface InquiryFilters {
  status?: string
  inquiry_type?: string
  user_id?: string
  product_id?: string
  date_from?: string
  date_to?: string
}

// Permission types
export interface Permission {
  resource: 'products' | 'inquiries' | 'users' | 'roles' | 'chat' | 'dashboard'
  actions: ('create' | 'read' | 'update' | 'delete')[]
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

// State types
export interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
}

export interface AdminAuthState {
  admin: AdminUser | null
  role: Role | null
  isLoading: boolean
  isAuthenticated: boolean
}

export interface CartItem {
  product: Product
  quantity: number
}

// Email types
export interface EmailTemplate {
  type: 'inquiry_update' | 'chat_message' | 'low_stock_alert'
  subject: string
  html: string
  text: string
}

export interface NotificationData {
  recipient: string
  template_type: EmailTemplate['type']
  data: Record<string, any>
}
