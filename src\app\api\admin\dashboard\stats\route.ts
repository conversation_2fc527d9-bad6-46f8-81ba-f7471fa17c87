import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';
import { verifyAdminAuth } from '@/lib/auth/admin';

export async function GET(request: NextRequest) {
  try {
    // Use the new unified admin authentication
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createSupabaseAdminClient();

    // Get products stats - try different column names
    let products = null;
    let productsError = null;

    // Get basic product info without stock columns
    const { data: basicProducts, error: basicError } = await supabase
      .from('products')
      .select('id, is_active, created_at');

    products = basicProducts;
    productsError = basicError;

    // Get users stats
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, created_at');

    if (usersError) {
      console.error('Error fetching users:', usersError);
    }

    // Get inquiries stats
    const { data: inquiries, error: inquiriesError } = await supabase
      .from('inquiries')
      .select('id, status, created_at');

    if (inquiriesError) {
      console.error('Error fetching inquiries:', inquiriesError);
    }

    // Calculate stats
    const now = new Date();
    const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

    const stats = {
      products: {
        total: products?.length || 0,
        active: products?.filter(p => p.is_active).length || 0,
        lowStock: 0, // Not available without stock columns
        outOfStock: 0 // Not available without stock columns
      },
      users: {
        total: users?.length || 0,
        newThisMonth: users?.filter(u => new Date(u.created_at) >= thisMonth).length || 0,
        newLastMonth: users?.filter(u => {
          const date = new Date(u.created_at);
          return date >= lastMonth && date < thisMonth;
        }).length || 0
      },
      inquiries: {
        total: inquiries?.length || 0,
        pending: inquiries?.filter(i => i.status === 'pending').length || 0,
        inProgress: inquiries?.filter(i => i.status === 'in_progress').length || 0,
        completed: inquiries?.filter(i => i.status === 'completed').length || 0,
        newThisMonth: inquiries?.filter(i => new Date(i.created_at) >= thisMonth).length || 0
      }
    };

    // Calculate growth percentages
    const userGrowth = stats.users.newLastMonth > 0 
      ? ((stats.users.newThisMonth - stats.users.newLastMonth) / stats.users.newLastMonth) * 100
      : stats.users.newThisMonth > 0 ? 100 : 0;

    return NextResponse.json({
      stats: {
        ...stats,
        growth: {
          users: Math.round(userGrowth * 100) / 100
        }
      }
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
