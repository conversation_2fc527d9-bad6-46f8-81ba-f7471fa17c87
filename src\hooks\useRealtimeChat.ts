'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/lib/supabase/client';

export interface ChatMessage {
  id: string;
  inquiry_id: string;
  sender_type: 'user' | 'admin';
  sender_id: string;
  message_type: 'text' | 'image' | 'link';
  content?: string;  // New messages use content
  message?: string;  // Old messages use message
  created_at: string;
  sender_name?: string;
}

interface UseRealtimeChatProps {
  inquiryId: string;
  currentUserId?: string;
  isAdmin?: boolean;
}

export function useRealtimeChat({ inquiryId, currentUserId, isAdmin = false }: UseRealtimeChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasInitiallyLoaded, setHasInitiallyLoaded] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const channelRef = useRef<any>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pollingIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const lastMessageTimestampRef = useRef<string | null>(null);

  // Typing stop workaround - refresh messages for 60 seconds after typing stops
  const typingStopWorkaroundRef = useRef<NodeJS.Timeout | null>(null);
  const typingStopCounterRef = useRef<number>(0);

  // Fetch initial messages (only once)
  const fetchMessages = useCallback(async () => {
    if (hasInitiallyLoaded) {
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Use admin API endpoint if isAdmin is true, otherwise use regular chat API
      const baseEndpoint = isAdmin ? `/api/admin/chat/${inquiryId}` : `/api/chat/${inquiryId}`;

      // Use relative API endpoint - let the browser handle the correct port
      const apiEndpoint = baseEndpoint;




      const response = await fetch(`${apiEndpoint}?t=${Date.now()}&r=${Math.random()}`, {
        credentials: 'include',
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', response.status, response.statusText, errorText);
        throw new Error(`Failed to fetch messages: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const messages = data.messages || [];
      setMessages(messages);
      setHasInitiallyLoaded(true);

      // Set last message timestamp for polling
      if (messages.length > 0) {
        lastMessageTimestampRef.current = messages[messages.length - 1].created_at;
      }
    } catch (err) {
      console.error('Error fetching messages:', err);
      setError(err instanceof Error ? err.message : 'Failed to load messages');
    } finally {
      setIsLoading(false);
    }
  }, [inquiryId, hasInitiallyLoaded, isAdmin]);

  // Send a new message
  const sendMessage = useCallback(async (content: string, messageType: 'text' | 'image' | 'link' = 'text') => {
    try {
      // Use admin API endpoint if isAdmin is true, otherwise use regular chat API
      const baseEndpoint = isAdmin ? `/api/admin/chat/${inquiryId}` : `/api/chat/${inquiryId}`;

      // Use relative API endpoint - let the browser handle the correct port
      const apiEndpoint = baseEndpoint;


      const response = await fetch(`${apiEndpoint}?t=${Date.now()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        cache: 'no-cache',
        body: JSON.stringify({
          content,
          message_type: messageType,
          is_admin: isAdmin, // Include admin flag in request for backward compatibility
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error:', response.status, response.statusText, errorText);
        throw new Error(`Failed to send message: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      return data.message;
    } catch (err) {
      console.error('Error sending message:', err);
      throw err;
    }
  }, [inquiryId, isAdmin]);

  // Typing indicator functions
  const sendTypingIndicator = useCallback((typing: boolean) => {
    if (channelRef.current && currentUserId) {
      console.log('Sending typing indicator:', { typing, currentUserId, inquiryId, isAdmin });
      channelRef.current.send({
        type: 'broadcast',
        event: 'typing',
        payload: {
          user_id: currentUserId,
          inquiry_id: inquiryId,
          typing,
          user_type: isAdmin ? 'admin' : 'user'
        }
      });
    } else {
      console.log('Cannot send typing indicator - channel or user not available:', {
        hasChannel: !!channelRef.current,
        currentUserId
      });
    }
  }, [currentUserId, inquiryId, isAdmin]);

  const handleTyping = useCallback(() => {
    if (!isTyping) {
      setIsTyping(true);
      sendTypingIndicator(true);
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      sendTypingIndicator(false);
    }, 2000);
  }, [isTyping, sendTypingIndicator]);

  // Polling mechanism as fallback
  const pollForNewMessages = useCallback(async () => {
    try {
      // Use admin API endpoint if isAdmin is true, otherwise use regular chat API
      const baseEndpoint = isAdmin ? `/api/admin/chat/${inquiryId}` : `/api/chat/${inquiryId}`;

      // Use relative API endpoint - let the browser handle the correct port
      const apiEndpoint = baseEndpoint;

      const url = lastMessageTimestampRef.current
        ? `${apiEndpoint}?since=${encodeURIComponent(lastMessageTimestampRef.current)}`
        : apiEndpoint;

      const response = await fetch(`${url}&t=${Date.now()}`, {
        credentials: 'include',
        cache: 'no-cache',
      });

      if (!response.ok) return;

      const data = await response.json();
      const newMessages = data.messages || [];

      if (newMessages.length > 0) {
        console.log('📥 Polling found new messages:', newMessages.length);
        setMessages(prev => {
          const combined = [...prev, ...newMessages];
          // Remove duplicates and sort by timestamp
          const unique = combined.filter((msg, index, arr) =>
            arr.findIndex(m => m.id === msg.id) === index
          ).sort((a, b) => new Date(a.created_at).getTime() - new Date(b.created_at).getTime());

          // Update last message timestamp
          if (unique.length > 0) {
            lastMessageTimestampRef.current = unique[unique.length - 1].created_at;
          }

          return unique;
        });
      }
    } catch (error) {
      console.error('Error polling for messages:', error);
    }
  }, [inquiryId, isAdmin]);

  // Manual refresh function (defined early to avoid reference errors)
  const refreshMessages = useCallback(async () => {
    try {
      setError(null);

      // Use admin API endpoint if isAdmin is true, otherwise use regular chat API
      const baseEndpoint = isAdmin ? `/api/admin/chat/${inquiryId}` : `/api/chat/${inquiryId}`;

      // Use relative API endpoint - let the browser handle the correct port
      const apiEndpoint = baseEndpoint;



      const response = await fetch(`${apiEndpoint}?t=${Date.now()}&r=${Math.random()}`, {
        credentials: 'include',
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to refresh messages: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      const newMessages = data.messages || [];

      // Update messages if we got new ones
      setMessages(newMessages);

    } catch (error) {
      console.error('Error refreshing messages:', error);
      setError(error instanceof Error ? error.message : 'Failed to refresh messages');
    }
  }, [inquiryId, isAdmin]);

  // Typing stop workaround - refresh messages every 1 second for 60 seconds
  const startTypingStopWorkaround = useCallback(() => {
    console.log('🔄 Starting typing stop workaround - will refresh messages every 1s for 60s');

    // Clear any existing workaround
    if (typingStopWorkaroundRef.current) {
      clearInterval(typingStopWorkaroundRef.current);
    }

    // Reset counter
    typingStopCounterRef.current = 0;

    // Start interval to refresh messages every 1 second
    typingStopWorkaroundRef.current = setInterval(() => {
      typingStopCounterRef.current += 1;
      console.log(`🔄 Typing workaround refresh ${typingStopCounterRef.current}/60`);

      // Refresh messages
      refreshMessages();

      // Stop after 60 seconds
      if (typingStopCounterRef.current >= 10) {
        console.log('🔄 Typing stop workaround completed (10 seconds)');
        if (typingStopWorkaroundRef.current) {
          clearInterval(typingStopWorkaroundRef.current);
          typingStopWorkaroundRef.current = null;
        }
      }
    }, 1000); // Every 1 second
  }, [refreshMessages]);

  // Set up real-time subscription
  useEffect(() => {
    console.log('🔧 useEffect triggered:', { inquiryId, currentUserId, isAdmin });

    if (!inquiryId) {
      console.log('❌ No inquiryId, skipping setup');
      return;
    }

    if (!currentUserId) {
      console.log('❌ No currentUserId, skipping setup');
      return;
    }

    console.log('Setting up real-time subscription for inquiry:', inquiryId, 'currentUserId:', currentUserId, 'isAdmin:', isAdmin);

    // Clean up previous subscription
    if (channelRef.current) {
      console.log('Cleaning up previous subscription');
      channelRef.current.unsubscribe();
    }

    const channel = supabase
      .channel(`chat_${inquiryId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `inquiry_id=eq.${inquiryId}`,
        },
        (payload) => {
          console.log('🔥 New message received via real-time:', payload);
          console.log('🔍 Current user ID:', currentUserId, 'Is Admin:', isAdmin);
          const newMessage = payload.new as ChatMessage;

          if (!newMessage || !newMessage.id) {
            console.error('Invalid message received:', newMessage);
            return;
          }

          console.log('📝 Message details:', {
            messageId: newMessage.id,
            senderId: newMessage.sender_id,
            senderType: newMessage.sender_type,
            inquiryId: newMessage.inquiry_id,
            content: newMessage.message || newMessage.content
          });

          // Add all new messages, but check for duplicates
          setMessages((prev) => {
            // Check if message already exists to prevent duplicates
            if (prev.some(msg => msg.id === newMessage.id)) {
              console.log('Duplicate message detected, skipping:', newMessage.id);
              return prev;
            }
            console.log('✅ Adding new message to state:', newMessage.id, newMessage);

            // Sort messages by created_at to ensure proper order
            const updatedMessages = [...prev, newMessage].sort((a, b) =>
              new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
            );

            // Update last message timestamp
            lastMessageTimestampRef.current = newMessage.created_at;

            return updatedMessages;
          });
        }
      )
      .on('broadcast', { event: 'typing' }, (payload) => {
        console.log('Typing indicator received:', payload);
        const { user_id, typing, user_type } = payload.payload;

        // Show typing indicator based on user type and current view
        let shouldShowTyping = false;

        if (isAdmin) {
          // Admin view: show typing from customers (user_type === 'user')
          shouldShowTyping = user_type === 'user' && user_id !== currentUserId;
        } else {
          // User view: show typing from admins (user_type === 'admin')
          shouldShowTyping = user_type === 'admin' && user_id !== currentUserId;
        }

        if (shouldShowTyping) {
          console.log('Setting other user typing:', typing, 'from user:', user_id, 'type:', user_type);
          setOtherUserTyping(typing);

          // Auto-hide typing indicator after 3 seconds
          if (typing) {
            setTimeout(() => {
              setOtherUserTyping(false);
              // Start typing stop workaround when typing stops
              console.log('🔄 Typing stopped, starting message refresh workaround');
              startTypingStopWorkaround();
            }, 3000);
          } else {
            // Typing stopped immediately, start workaround
            console.log('🔄 Typing stopped immediately, starting message refresh workaround');
            startTypingStopWorkaround();
          }
        } else {
          console.log('Ignoring typing indicator - user_id:', user_id, 'user_type:', user_type, 'currentUserId:', currentUserId, 'isAdmin:', isAdmin);
        }
      })
      .subscribe((status) => {
        console.log('🔌 Realtime subscription status:', status, 'for inquiry:', inquiryId);
        setIsConnected(status === 'SUBSCRIBED');

        if (status === 'SUBSCRIBED') {
          console.log('✅ Real-time subscription active for chat_messages table');
          setError(null); // Clear any previous errors
          // Stop polling when real-time is working
          if (pollingIntervalRef.current) {
            clearInterval(pollingIntervalRef.current);
            pollingIntervalRef.current = null;
          }
        } else if (status === 'CHANNEL_ERROR') {
          console.error('❌ Real-time connection failed');
          setError('Real-time connection failed. Using polling as fallback.');
          // Start polling as fallback
          if (!pollingIntervalRef.current) {
            pollingIntervalRef.current = setInterval(pollForNewMessages, 3000);
          }
        } else if (status === 'TIMED_OUT') {
          console.error('⏰ Real-time connection timed out');
          setError('Connection timed out. Using polling as fallback.');
          // Start polling as fallback
          if (!pollingIntervalRef.current) {
            pollingIntervalRef.current = setInterval(pollForNewMessages, 3000);
          }
          // Try to reconnect after a delay
          setTimeout(() => {
            if (channelRef.current) {
              channelRef.current.subscribe();
            }
          }, 3000);
        } else if (status === 'CLOSED') {
          console.log('🔒 Real-time connection closed');
          setIsConnected(false);
          // Start polling as fallback
          if (!pollingIntervalRef.current) {
            pollingIntervalRef.current = setInterval(pollForNewMessages, 3000);
          }
        }
      });

    channelRef.current = channel;

    // Fetch initial messages
    console.log('📞 About to call fetchMessages');
    fetchMessages();

    return () => {
      if (channelRef.current) {
        console.log('Unsubscribing from real-time channel');
        channelRef.current.unsubscribe();
      }
      if (pollingIntervalRef.current) {
        clearInterval(pollingIntervalRef.current);
        pollingIntervalRef.current = null;
      }
      if (typingStopWorkaroundRef.current) {
        clearInterval(typingStopWorkaroundRef.current);
        typingStopWorkaroundRef.current = null;
      }
    };
  }, [inquiryId, currentUserId, fetchMessages, isAdmin, pollForNewMessages, startTypingStopWorkaround]);

  // Optimistically add message when sending
  const sendMessageOptimistic = useCallback(async (content: string, messageType: 'text' | 'image' | 'link' = 'text') => {
    if (!currentUserId) {
      throw new Error('User not authenticated');
    }

    // Create optimistic message
    const optimisticMessage: ChatMessage = {
      id: `temp-${Date.now()}`,
      inquiry_id: inquiryId,
      sender_type: isAdmin ? 'admin' : 'user',
      sender_id: currentUserId,
      message_type: messageType,
      message: content,
      created_at: new Date().toISOString(),
      sender_name: 'You',
    };

    // Add optimistic message immediately
    setMessages(prev => [...prev, optimisticMessage]);

    try {
      // Send message to server
      const savedMessage = await sendMessage(content, messageType);

      // Replace optimistic message with real message
      setMessages(prev =>
        prev.map(msg =>
          msg.id === optimisticMessage.id ? savedMessage : msg
        )
      );

      // Start typing stop workaround after sending message to ensure it appears on other side
      console.log('🔄 Message sent, starting refresh workaround to ensure delivery to other users');
      startTypingStopWorkaround();

      return savedMessage;
    } catch (error) {
      // Remove optimistic message on error
      setMessages(prev => prev.filter(msg => msg.id !== optimisticMessage.id));
      throw error;
    }
  }, [inquiryId, currentUserId, sendMessage, startTypingStopWorkaround]);



  return {
    messages,
    isLoading,
    isConnected,
    error,
    sendMessage: sendMessageOptimistic,
    refetch: fetchMessages,
    refreshMessages,
    isTyping,
    otherUserTyping,
    handleTyping,
  };
}
