import type { NextApiRequest, NextApiResponse } from 'next';

export default function handler(req: NextApiRequest, res: NextApiResponse) {
  // Simple SVG placeholder for product images
  const svg = `
    <svg width="400" height="400" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#956358;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#f9c1b2;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="#1a2b4a"/>
      <rect x="50" y="50" width="300" height="300" fill="url(#grad)" opacity="0.1"/>
      <text x="50%" y="45%" dominant-baseline="middle" text-anchor="middle" fill="#f9c1b2" font-family="system-ui" font-size="14">
        TISHA INTERNATIONAL
      </text>
      <text x="50%" y="55%" dominant-baseline="middle" text-anchor="middle" fill="#956358" font-family="system-ui" font-size="12">
        Product Image
      </text>
    </svg>
  `;

  res.setHeader('Content-Type', 'image/svg+xml');
  res.setHeader('Cache-Control', 'public, max-age=31536000');
  res.status(200).send(svg);
}
