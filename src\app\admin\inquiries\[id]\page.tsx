import { AdminInquiryDetail } from '@/components/admin/AdminInquiryDetail';

interface AdminInquiryDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function AdminInquiryDetailPage({ params }: AdminInquiryDetailPageProps) {
  const { id } = await params;
  return <AdminInquiryDetail inquiryId={id} />;
}

export const metadata = {
  title: 'Inquiry Details - Tisha International Admin',
  description: 'View customer inquiry details and manage communication',
};
