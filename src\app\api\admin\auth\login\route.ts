import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

const JWT_SECRET = process.env.JWT_SECRET || 'development_jwt_secret_key_change_in_production';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    console.log('Admin login attempt for email:', email);

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    const supabase = createSupabaseAdminClient();

    // Find admin user by email
    const { data: adminUser, error: userError } = await supabase
      .from('admin_users')
      .select('*')
      .eq('email', email)
      .eq('is_active', true)
      .single();

    if (userError) {
      console.error('Admin user query error:', userError);

      // Check if it's a table not found error
      if (userError.code === '42P01') {
        return NextResponse.json(
          { error: 'Admin system not set up. Please run admin setup first.' },
          { status: 500 }
        );
      }

      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    if (!adminUser) {
      console.error('Admin user not found for email:', email);
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    console.log('Admin user found:', { id: adminUser.id, email: adminUser.email, full_name: adminUser.full_name, role: adminUser.role });

    // Verify password
    const isValidPassword = await bcrypt.compare(password, adminUser.password_hash);
    console.log('Password validation result:', isValidPassword);

    if (!isValidPassword) {
      console.error('Invalid password for admin user:', email);
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Get role information by name (since the admin_users table has role as string)
    const { data: role, error: roleError } = await supabase
      .from('roles')
      .select('*')
      .eq('name', adminUser.role === 'admin' ? 'Super Admin' : adminUser.role)
      .single();

    if (roleError || !role) {
      console.error('Role query error:', roleError);
      console.log('Looking for role name:', adminUser.role === 'admin' ? 'Super Admin' : adminUser.role);
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 500 }
      );
    }

    // Create JWT token
    const token = jwt.sign(
      {
        adminId: adminUser.id,
        email: adminUser.email,
        role: role.name,
        roleId: role.id
      },
      JWT_SECRET,
      { expiresIn: '8h' }
    );

    // Set HTTP-only cookie
    const cookieStore = await cookies();
    cookieStore.set('admin-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 8 // 8 hours
    });

    // Return success response with user data
    return NextResponse.json({
      success: true,
      admin: {
        id: adminUser.id,
        email: adminUser.email,
        fullName: adminUser.full_name,
        role: {
          id: role.id,
          name: role.name,
          permissions: role.permissions,
          description: role.description
        }
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    );
  }
}