import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET() {
  try {
    // Get carousel settings
    const { data: settings } = await supabase
      .from('home_carousel_settings')
      .select('*')
      .single();

    // Get carousel products
    const { data: carouselData, error: carouselError } = await supabase
      .from('home_carousel_products')
      .select(`
        id,
        product_id,
        display_order,
        custom_image,
        custom_description,
        custom_background_image,
        is_active,
        products (
          id,
          name,
          price,
          images,
          brand,
          model,
          description,
          show_price
        )
      `)
      .order('display_order');

    if (carouselError) {
      console.error('Error fetching carousel products:', carouselError);
      return NextResponse.json({ error: 'Failed to fetch carousel products' }, { status: 500 });
    }

    return NextResponse.json({
      products: carouselData || [],
      settings: {
        auto_play_interval: settings?.auto_play_interval || 5000
      }
    });

  } catch (error) {
    console.error('Error in admin carousel API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { product_id, display_order, custom_image, custom_description, custom_background_image } = body;

    if (!product_id) {
      return NextResponse.json({ error: 'Product ID is required' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('home_carousel_products')
      .insert({
        product_id,
        display_order: display_order || 0,
        custom_image,
        custom_description,
        custom_background_image,
        is_active: true
      })
      .select(`
        id,
        product_id,
        display_order,
        custom_image,
        custom_description,
        custom_background_image,
        is_active,
        products (
          id,
          name,
          price,
          images,
          brand,
          model,
          description,
          show_price
        )
      `)
      .single();

    if (error) {
      console.error('Error creating carousel product:', error);
      return NextResponse.json({ error: 'Failed to create carousel product' }, { status: 500 });
    }

    return NextResponse.json({ product: data });

  } catch (error) {
    console.error('Error in admin carousel POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
