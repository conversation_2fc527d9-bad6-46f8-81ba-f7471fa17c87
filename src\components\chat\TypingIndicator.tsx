import React from 'react';

interface TypingIndicatorProps {
  isTyping: boolean;
  userName?: string;
  isAdmin?: boolean;
}

export function TypingIndicator({ isTyping, userName, isAdmin = false }: TypingIndicatorProps) {
  if (!isTyping) return null;

  const displayName = userName || (isAdmin ? 'Support Team' : 'Customer');

  return (
    <div className="flex items-center space-x-2 px-4 py-2 text-sm text-gray-400">
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-[#f9c1b2] rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
        <div className="w-2 h-2 bg-[#f9c1b2] rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
        <div className="w-2 h-2 bg-[#f9c1b2] rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
      </div>
      <span>{displayName} is typing...</span>
    </div>
  );
}
