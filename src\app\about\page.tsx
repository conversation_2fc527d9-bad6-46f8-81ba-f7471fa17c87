import { Metadata } from 'next';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export const metadata: Metadata = {
  title: 'About Us - Tisha International',
  description: 'Learn about Tisha International\'s journey in providing premium refurbished laptops and desktops with quality and reliability.',
};

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--background-primary)] via-[var(--background-secondary)] to-[var(--background-card)]">
      {/* Hero Section */}
      <section className="py-20 px-4 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-[var(--accent-primary)]/10 to-[var(--accent-secondary)]/10"></div>
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-primary rounded-full opacity-20 animate-float"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-gradient-primary rounded-full opacity-15 animate-float" style={{ animationDelay: '1s' }}></div>

        <div className="max-w-6xl mx-auto text-center relative z-10">
          <div className="animate-fade-in">
            <h1 className="heading-primary text-5xl md:text-7xl mb-6">
              About <span className="text-gradient">Tisha International</span>
            </h1>
            <p className="text-xl text-[var(--soft-white)] max-w-3xl mx-auto leading-relaxed">
              Your trusted partner for premium refurbished laptops and desktops.
              Quality, reliability, and exceptional value with every purchase.
            </p>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="card-glass p-8 mb-12 animate-fade-in">
            <h2 className="heading-primary text-4xl mb-8 text-center">
              Our <span className="text-gradient">Story</span>
            </h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <p className="text-[var(--soft-white)] leading-relaxed text-lg">
                  Founded with a vision to make premium computing accessible to everyone,
                  Tisha International has been at the forefront of the refurbished technology market.
                  We believe that high-quality technology shouldn&apos;t come at a premium price.
                </p>
                <p className="text-[var(--soft-white)] leading-relaxed">
                  Our journey began with a simple idea: to provide businesses and individuals
                  with reliable, tested, and certified refurbished laptops and desktops that
                  offer exceptional value without compromising on quality.
                </p>
              </div>
              <div className="space-y-4">
                <div className="flex items-center gap-3 group hover:translate-x-2 transition-transform duration-300">
                  <div className="badge-completed">2015</div>
                  <span className="text-[var(--soft-white)] group-hover:text-[var(--accent-secondary)] transition-colors duration-300">Company Founded</span>
                </div>
                <div className="flex items-center gap-3 group hover:translate-x-2 transition-transform duration-300">
                  <div className="badge-completed">10,000+</div>
                  <span className="text-[var(--soft-white)] group-hover:text-[var(--accent-secondary)] transition-colors duration-300">Happy Customers</span>
                </div>
                <div className="flex items-center gap-3 group hover:translate-x-2 transition-transform duration-300">
                  <div className="badge-completed">50,000+</div>
                  <span className="text-[var(--soft-white)] group-hover:text-[var(--accent-secondary)] transition-colors duration-300">Devices Refurbished</span>
                </div>
                <div className="flex items-center gap-3 group hover:translate-x-2 transition-transform duration-300">
                  <div className="badge-completed">99.2%</div>
                  <span className="text-[var(--soft-white)] group-hover:text-[var(--accent-secondary)] transition-colors duration-300">Customer Satisfaction</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Values Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="animate-fade-in" style={{ animationDelay: '0.2s' }}>
            <h2 className="heading-primary text-4xl text-center mb-12">
              Our Core <span className="text-gradient">Values</span>
            </h2>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="card-glass p-6 text-center group hover:shadow-glow transition-all duration-500 animate-fade-in" style={{ animationDelay: '0.3s' }}>
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-button">
                <span className="text-white font-bold text-xl">Q</span>
              </div>
              <h3 className="heading-secondary text-xl mb-3 group-hover:text-[var(--accent-secondary)] transition-colors duration-300">Quality First</h3>
              <p className="text-[var(--soft-white)] text-sm leading-relaxed">
                Every device undergoes rigorous testing and certification to ensure optimal performance.
              </p>
            </div>

            <div className="card-glass p-6 text-center group hover:shadow-glow transition-all duration-500 animate-fade-in" style={{ animationDelay: '0.4s' }}>
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-button">
                <span className="text-white font-bold text-xl">T</span>
              </div>
              <h3 className="heading-secondary text-xl mb-3 group-hover:text-[var(--accent-secondary)] transition-colors duration-300">Transparency</h3>
              <p className="text-[var(--soft-white)] text-sm leading-relaxed">
                Complete honesty about product condition, specifications, and pricing.
              </p>
            </div>

            <div className="card-glass p-6 text-center group hover:shadow-glow transition-all duration-500 animate-fade-in" style={{ animationDelay: '0.5s' }}>
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-button">
                <span className="text-white font-bold text-xl">S</span>
              </div>
              <h3 className="heading-secondary text-xl mb-3 group-hover:text-[var(--accent-secondary)] transition-colors duration-300">Sustainability</h3>
              <p className="text-[var(--soft-white)] text-sm leading-relaxed">
                Committed to reducing e-waste by extending the lifecycle of quality devices.
              </p>
            </div>

            <div className="card-glass p-6 text-center group hover:shadow-glow transition-all duration-500 animate-fade-in" style={{ animationDelay: '0.6s' }}>
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-button">
                <span className="text-white font-bold text-xl">C</span>
              </div>
              <h3 className="heading-secondary text-xl mb-3 group-hover:text-[var(--accent-secondary)] transition-colors duration-300">Customer Care</h3>
              <p className="text-[var(--soft-white)] text-sm leading-relaxed">
                Dedicated support team ensuring exceptional customer service experience.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <Card className="p-8">
            <h2 className="text-3xl font-bold text-white text-center mb-12">Why Choose Tisha International?</h2>
            <div className="grid md:grid-cols-2 gap-8">
              <div className="space-y-6">
                <div className="flex gap-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Certified Refurbishment Process</h3>
                    <p className="text-gray-300 text-sm">
                      Our 47-point inspection process ensures every device meets our high standards.
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Comprehensive Warranty</h3>
                    <p className="text-gray-300 text-sm">
                      All products come with warranty coverage for your peace of mind.
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Expert Technical Support</h3>
                    <p className="text-gray-300 text-sm">
                      Our certified technicians provide ongoing support and assistance.
                    </p>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="flex gap-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Bulk Order Solutions</h3>
                    <p className="text-gray-300 text-sm">
                      Special pricing and services for businesses and educational institutions.
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Fast Delivery Network</h3>
                    <p className="text-gray-300 text-sm">
                      Quick and secure delivery across the region with tracking support.
                    </p>
                  </div>
                </div>

                <div className="flex gap-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">Eco-Friendly Approach</h3>
                    <p className="text-gray-300 text-sm">
                      Contributing to environmental sustainability through device refurbishment.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </section>
    </div>
  );
}
