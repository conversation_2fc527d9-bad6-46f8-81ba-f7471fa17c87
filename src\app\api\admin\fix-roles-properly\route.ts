import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    // This is a development-only endpoint for properly fixing admin user roles
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    const supabase = createSupabaseAdminClient();

    // First, get all roles to see what IDs actually exist
    const { data: roles, error: rolesError } = await supabase
      .from('roles')
      .select('*');

    console.log('Available roles:', roles);

    if (rolesError || !roles || roles.length === 0) {
      return NextResponse.json({
        error: 'No roles found',
        rolesError,
        roles
      });
    }

    // Find Super Admin and Support roles
    const superAdminRole = roles.find(role => role.name === 'Super Admin');
    const supportRole = roles.find(role => role.name === 'Support');

    console.log('Super Admin role:', superAdminRole);
    console.log('Support role:', supportRole);

    if (!superAdminRole || !supportRole) {
      return NextResponse.json({
        error: 'Required roles not found',
        superAdminRole,
        supportRole,
        availableRoles: roles
      });
    }

    // Update admin user with correct role_id
    const { error: adminError } = await supabase
      .from('admin_users')
      .update({ role: 'super_admin' })
      .eq('email', '<EMAIL>');

    console.log('Admin update error:', adminError);

    // Update support user with correct role_id
    const { error: supportError } = await supabase
      .from('admin_users')
      .update({ role: 'support' })
      .eq('email', '<EMAIL>');

    console.log('Support update error:', supportError);

    // Verify the updates
    const { data: updatedAdminUsers, error: fetchError } = await supabase
      .from('admin_users')
      .select('email, role_id, full_name');

    console.log('Updated admin users:', updatedAdminUsers);

    return NextResponse.json({
      message: 'Admin user roles fixed properly',
      roles,
      superAdminRole,
      supportRole,
      adminError,
      supportError,
      updatedAdminUsers,
      fetchError
    });

  } catch (error) {
    console.error('Role fix error:', error);
    return NextResponse.json(
      { error: 'Role fix failed', details: error },
      { status: 500 }
    );
  }
}
