import type { Metadata } from "next";
import { Inter, Poppins } from "next/font/google";
import { Layout } from "@/components/layout/Layout";
import "./globals.css";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Tisha International - Premium Refurbished Laptops & Desktops",
  description: "Your trusted partner for premium refurbished laptops and desktops. Quality, reliability, and exceptional value with every purchase.",
  keywords: "refurbished laptops, refurbished desktops, computers, tech, Tisha International",
  authors: [{ name: "Tisha International" }],
  creator: "Tisha International",
  publisher: "Tisha International",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://tishainternational.com",
    siteName: "Tisha International",
    title: "Tisha International - Premium Refurbished Laptops & Desktops",
    description: "Your trusted partner for premium refurbished laptops and desktops. Quality, reliability, and exceptional value with every purchase.",
    images: [
      {
        url: "/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Tisha International",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Tisha International - Premium Refurbished Laptops & Desktops",
    description: "Your trusted partner for premium refurbished laptops and desktops. Quality, reliability, and exceptional value with every purchase.",
    images: ["/twitter-image.jpg"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="h-full">
      <body className={`${poppins.variable} ${inter.variable} antialiased h-full font-['Poppins']`}>
        <Layout>{children}</Layout>
      </body>
    </html>
  );
}
