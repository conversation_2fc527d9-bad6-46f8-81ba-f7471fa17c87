import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { data, error } = await supabase
      .from('home_featured_categories')
      .select(`
        id,
        category_id,
        custom_image,
        display_order,
        is_active,
        categories (
          id,
          name,
          slug
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching featured category:', error);
      return NextResponse.json({ error: 'Featured category not found' }, { status: 404 });
    }

    return NextResponse.json({ category: data });

  } catch (error) {
    console.error('Error in featured category GET:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { is_active, display_order, custom_image } = body;

    const updateData: any = {};
    if (typeof is_active === 'boolean') updateData.is_active = is_active;
    if (typeof display_order === 'number') updateData.display_order = display_order;
    if (custom_image !== undefined) updateData.custom_image = custom_image;

    const { data, error } = await supabase
      .from('home_featured_categories')
      .update(updateData)
      .eq('id', id)
      .select(`
        id,
        category_id,
        custom_image,
        display_order,
        is_active,
        categories (
          id,
          name,
          slug
        )
      `)
      .single();

    if (error) {
      console.error('Error updating featured category:', error);
      return NextResponse.json({ error: 'Failed to update featured category' }, { status: 500 });
    }

    return NextResponse.json({ category: data });

  } catch (error) {
    console.error('Error in featured category PATCH:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const { error } = await supabase
      .from('home_featured_categories')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting featured category:', error);
      return NextResponse.json({ error: 'Failed to delete featured category' }, { status: 500 });
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error in featured category DELETE:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
