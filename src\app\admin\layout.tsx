'use client';

import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import {
  LayoutDashboard,
  Package,
  MessageSquare,
  Users,
  Settings,
  LogOut,
  Menu,
  X,
  Shield,
  Tag,
  Star
} from 'lucide-react';

const navigation = [
  { name: 'Dashboard', href: '/admin/dashboard', icon: LayoutDashboard },
  { name: 'Products', href: '/admin/products', icon: Package },
  { name: 'Categories', href: '/admin/categories', icon: Tag },
  { name: 'Brands', href: '/admin/brands', icon: Tag },
  { name: 'Conditions', href: '/admin/conditions', icon: Star },
  { name: 'Inquiries', href: '/admin/inquiries', icon: MessageSquare },
  { name: 'Users', href: '/admin/users', icon: Users },
  { name: 'Home Settings', href: '/admin/home-settings', icon: Settings },
];

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const { admin, isAuthenticated, isLoading, logout, initialize } = useAdminAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  // Skip auth check for login and setup pages
  const isAuthPage = pathname?.includes('/login') || pathname?.includes('/setup') || pathname?.includes('/signin');

  // Initialize auth on mount
  useEffect(() => {
    initialize();
  }, [initialize]);

  useEffect(() => {
    if (!isLoading && !isAuthenticated && !isAuthPage) {
      router.replace('/auth/login?redirect=/admin/dashboard');
    }
  }, [isAuthenticated, isLoading, router, isAuthPage]);

  const handleLogout = async () => {
    await logout();
    router.push('/');
  };

  // Show auth pages without layout
  if (isAuthPage) {
    return <>{children}</>;
  }

  // Show loading state or redirect if not authenticated
  if (isLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#1a2b4a] flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 border-2 border-[#956358] border-t-transparent rounded-full animate-spin"></div>
          <span className="text-white">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--background-primary)] via-[var(--background-secondary)] to-[var(--background-card)] text-[var(--text-primary)]">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 z-50 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="fixed inset-y-0 left-0 flex w-64 flex-col bg-[var(--background-secondary)]">
          <div className="flex h-16 items-center justify-between px-4">
            <div className="flex items-center space-x-2">
              <Shield className="h-8 w-8 text-[var(--accent-primary)]" />
              <span className="text-[var(--text-primary)] font-bold">Admin Panel</span>
            </div>
            <button onClick={() => setSidebarOpen(false)} className="text-[var(--text-secondary)] hover:text-[var(--text-primary)]">
              <X className="h-6 w-6" />
            </button>
          </div>
          <nav className="flex-1 space-y-1 px-2 py-4">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => setSidebarOpen(false)}
                className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                  pathname === item.href
                    ? 'bg-[var(--accent-primary)] text-[var(--text-primary)]'
                    : 'text-[var(--text-secondary)] hover:bg-[var(--background-primary)] hover:text-[var(--text-primary)]'
                }`}
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </Link>
            ))}
          </nav>
          <div className="border-t border-[var(--shadow-grey)] p-4">
            <div className="flex items-center space-x-3 mb-3">
              <div className="h-8 w-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center">
                <span className="text-[var(--text-primary)] text-sm font-medium">
                  {admin?.fullName?.charAt(0) || 'A'}
                </span>
              </div>
              <div>
                <p className="text-[var(--text-primary)] text-sm font-medium">{admin?.fullName}</p>
                <p className="text-[var(--text-secondary)] text-xs">Administrator</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-2 py-2 text-sm text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:bg-[var(--background-primary)] rounded-md transition-colors"
            >
              <LogOut className="mr-3 h-4 w-4" />
              Sign out
            </button>
          </div>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex flex-col flex-grow bg-[var(--background-secondary)] pt-5 pb-4 overflow-y-auto">
          <div className="flex items-center flex-shrink-0 px-4 mb-8">
            <Shield className="h-8 w-8 text-[var(--accent-primary)]" />
            <span className="ml-2 text-[var(--text-primary)] font-bold text-lg">Admin Panel</span>
          </div>
          <nav className="flex-1 flex flex-col space-y-1 px-2">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                  pathname === item.href
                    ? 'bg-[var(--accent-primary)] text-[var(--text-primary)]'
                    : 'text-[var(--text-secondary)] hover:bg-[var(--background-primary)] hover:text-[var(--text-primary)]'
                }`}
              >
                <item.icon className="mr-3 h-5 w-5" />
                {item.name}
              </Link>
            ))}
          </nav>
          <div className="border-t border-[var(--shadow-grey)] p-4">
            <div className="flex items-center space-x-3 mb-3">
              <div className="h-10 w-10 bg-[var(--accent-primary)] rounded-full flex items-center justify-center">
                <span className="text-[var(--text-primary)] font-medium">
                  {admin?.fullName?.charAt(0) || 'A'}
                </span>
              </div>
              <div>
                <p className="text-[var(--text-primary)] text-sm font-medium">{admin?.fullName}</p>
                <p className="text-[var(--text-secondary)] text-xs">Administrator</p>
              </div>
            </div>
            <button
              onClick={handleLogout}
              className="flex items-center w-full px-2 py-2 text-sm text-[var(--text-secondary)] hover:text-[var(--text-primary)] hover:bg-[var(--background-primary)] rounded-md transition-colors"
            >
              <LogOut className="mr-3 h-4 w-4" />
              Sign out
            </button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64 flex flex-col flex-1">
        {/* Top bar */}
        <div className="sticky top-0 z-10 flex h-16 bg-[var(--background-secondary)] shadow-lg border-b border-[var(--shadow-grey)]">
          <button
            type="button"
            className="border-r border-[var(--shadow-grey)] px-4 text-[var(--text-secondary)] focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[var(--accent-primary)] lg:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>
          <div className="flex flex-1 justify-between px-4">
            <div className="flex flex-1">
              <div className="flex items-center">
                <h1 className="text-xl font-semibold text-[var(--text-primary)]">
                  {navigation.find(item => item.href === pathname)?.name || 'Admin Panel'}
                </h1>
              </div>
            </div>
            <div className="ml-4 flex items-center md:ml-6">
              <div className="flex items-center space-x-3">
                <div className="h-8 w-8 bg-[var(--accent-primary)] rounded-full flex items-center justify-center">
                  <span className="text-[var(--text-primary)] text-sm font-medium">
                    {admin?.fullName?.charAt(0) || 'A'}
                  </span>
                </div>
                <span className="text-[var(--text-primary)] text-sm font-medium hidden md:block">
                  {admin?.fullName}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 text-[var(--text-primary)]">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
