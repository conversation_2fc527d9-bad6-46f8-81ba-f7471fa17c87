import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { error } = await supabase
      .from('home_category_gallery')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Error deleting category gallery item:', error);
      return NextResponse.json({ error: 'Failed to delete gallery item' }, { status: 500 });
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error in category gallery DELETE:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { is_active, display_order } = body;

    const updateData: any = {};
    if (typeof is_active === 'boolean') updateData.is_active = is_active;
    if (typeof display_order === 'number') updateData.display_order = display_order;

    const { data, error } = await supabase
      .from('home_category_gallery')
      .update(updateData)
      .eq('id', params.id)
      .select(`
        id,
        category_id,
        product_id,
        display_order,
        is_active,
        products (
          id,
          name,
          price,
          images,
          brand,
          model,
          show_price
        )
      `)
      .single();

    if (error) {
      console.error('Error updating category gallery item:', error);
      return NextResponse.json({ error: 'Failed to update gallery item' }, { status: 500 });
    }

    return NextResponse.json({ gallery_item: data });

  } catch (error) {
    console.error('Error in category gallery PATCH:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
