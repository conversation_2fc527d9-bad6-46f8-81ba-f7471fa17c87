'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, Upload, Search } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  price: number;
  images: string[];
  brand: string;
  model: string;
  description?: string;
}

export default function AddCarouselProductPage() {
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [formData, setFormData] = useState({
    display_order: 0,
    custom_image: '',
    custom_description: ''
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [carouselCount, setCarouselCount] = useState(0);

  useEffect(() => {
    fetchProducts();
  }, []);

  useEffect(() => {
    if (searchTerm) {
      const filtered = products.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.model.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredProducts(filtered);
    } else {
      setFilteredProducts(products);
    }
  }, [searchTerm, products]);

  const fetchProducts = async () => {
    try {
      // Fetch all products
      const productsResponse = await fetch('/api/products?limit=100');

      // Fetch already added carousel products
      const carouselResponse = await fetch('/api/admin/home/<USER>');

      if (productsResponse.ok && carouselResponse.ok) {
        const productsData = await productsResponse.json();
        const carouselData = await carouselResponse.json();

        // Get IDs of products already in carousel
        const addedProductIds = new Set(
          carouselData.products?.map((item: any) => item.product_id) || []
        );

        // Filter out already added products
        const availableProducts = (productsData.products || []).filter(
          (product: Product) => !addedProductIds.has(product.id)
        );

        setProducts(availableProducts);
        setFilteredProducts(availableProducts);
        setCarouselCount(carouselData.products?.length || 0);

        // Set next display order
        const maxOrder = Math.max(
          ...carouselData.products?.map((item: any) => item.display_order) || [0]
        );
        setFormData(prev => ({ ...prev, display_order: maxOrder + 1 }));
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedProduct) {
      alert('Please select a product');
      return;
    }

    setIsSaving(true);
    try {
      const response = await fetch('/api/admin/home/<USER>', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          product_id: selectedProduct.id,
          display_order: formData.display_order,
          custom_image: formData.custom_image || null,
          custom_description: formData.custom_description || null
        }),
      });

      if (response.ok) {
        router.push('/admin/home-settings');
      } else {
        alert('Failed to add carousel product');
      }
    } catch (error) {
      console.error('Error adding carousel product:', error);
      alert('Error adding carousel product');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-[var(--accent-primary)] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4 border-b border-[var(--shadow-grey)] pb-4">
        <button
          onClick={() => router.back()}
          className="p-2 hover:bg-[var(--shadow-grey)] rounded-md transition-colors"
        >
          <ArrowLeft className="h-5 w-5 text-[var(--text-secondary)]" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-[var(--text-primary)]">Add Carousel Product</h1>
          <p className="text-[var(--text-secondary)] mt-1">
            Select a product to add to the home page carousel
          </p>
          <div className="flex items-center space-x-4 mt-2">
            <span className="text-sm text-[var(--text-secondary)]">
              Current carousel: {carouselCount} products
            </span>
            <span className="text-sm text-[var(--accent-primary)]">
              Next order: #{formData.display_order}
            </span>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Product Selection */}
        <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
          <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-4">Select Product</h2>
          
          {/* Search */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--text-secondary)]" />
            <input
              type="text"
              placeholder="Search products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
            />
          </div>

          {/* Product Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-96 overflow-y-auto">
            {filteredProducts.map((product) => (
              <div
                key={product.id}
                onClick={() => setSelectedProduct(product)}
                className={`p-4 rounded-lg border cursor-pointer transition-all ${
                  selectedProduct?.id === product.id
                    ? 'border-[var(--accent-primary)] bg-[var(--accent-primary)]/10'
                    : 'border-[var(--shadow-grey)] hover:border-[var(--accent-primary)]/50'
                }`}
              >
                <div className="aspect-square bg-[var(--shadow-grey)] rounded-lg mb-3 overflow-hidden">
                  {product.images && product.images.length > 0 ? (
                    <img
                      src={product.images[0]}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-[var(--text-secondary)]">
                      <Upload className="h-8 w-8" />
                    </div>
                  )}
                </div>
                <h3 className="font-semibold text-[var(--text-primary)] text-sm line-clamp-1 mb-1">
                  {product.name}
                </h3>
                <p className="text-xs text-[var(--text-secondary)] line-clamp-1">
                  {product.brand} • {product.model}
                </p>
              </div>
            ))}
          </div>

          {filteredProducts.length === 0 && (
            <div className="text-center py-8">
              {searchTerm ? (
                <p className="text-[var(--text-secondary)]">No products found matching "{searchTerm}"</p>
              ) : (
                <div>
                  <Upload className="h-12 w-12 text-[var(--text-secondary)] mx-auto mb-4" />
                  <p className="text-[var(--text-secondary)] mb-2">All available products are already in the carousel</p>
                  <p className="text-xs text-[var(--text-secondary)]">
                    Remove products from the carousel to add different ones
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Configuration */}
        {selectedProduct && (
          <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
            <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-4">Configuration</h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  Display Order
                </label>
                <input
                  type="number"
                  value={formData.display_order}
                  readOnly
                  className="w-full px-3 py-2 bg-[var(--shadow-grey)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-secondary)] cursor-not-allowed"
                />
                <p className="text-xs text-[var(--text-secondary)] mt-1">
                  Auto-assigned as next available order (products appear in this order)
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  Custom Image URL (Optional)
                </label>
                <input
                  type="url"
                  value={formData.custom_image}
                  onChange={(e) => setFormData(prev => ({ ...prev, custom_image: e.target.value }))}
                  className="w-full px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
                  placeholder="https://example.com/image.jpg"
                />
                <p className="text-xs text-[var(--text-secondary)] mt-1">
                  Leave empty to use product's first image
                </p>
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                  Custom Description (Optional)
                </label>
                <textarea
                  value={formData.custom_description}
                  onChange={(e) => setFormData(prev => ({ ...prev, custom_description: e.target.value }))}
                  className="w-full px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
                  rows={3}
                  placeholder="Custom description for the carousel..."
                />
                <p className="text-xs text-[var(--text-secondary)] mt-1">
                  Leave empty to use product's description
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-6 py-2 border border-[var(--shadow-grey)] text-[var(--text-secondary)] rounded-md hover:bg-[var(--shadow-grey)] transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={!selectedProduct || isSaving}
            className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4" />
            <span>{isSaving ? 'Adding...' : 'Add to Carousel'}</span>
          </button>
        </div>
      </form>
    </div>
  );
}
