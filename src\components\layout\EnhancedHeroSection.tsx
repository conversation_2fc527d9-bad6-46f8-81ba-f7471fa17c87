'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { SpotlightCard } from '@/components/ui/spotlight';
import { ArrowRight, Shield, Award, Truck, HeadphonesIcon, Star, CheckCircle } from 'lucide-react';
import Link from 'next/link';

export default function EnhancedHeroSection() {
  const [currentFeature, setCurrentFeature] = useState(0);

  const features = [
    {
      icon: Shield,
      title: "Quality Assured",
      description: "Rigorous testing & certification process"
    },
    {
      icon: Award,
      title: "Premium Brands",
      description: "Top-tier refurbished technology"
    },
    {
      icon: Truck,
      title: "Fast Delivery",
      description: "Quick & secure shipping nationwide"
    },
    {
      icon: HeadphonesIcon,
      title: "Expert Support",
      description: "24/7 technical assistance"
    }
  ];

  const stats = [
    { label: "Happy Customers", value: "15,000+", description: "Satisfied customers nationwide" },
    { label: "Products Sold", value: "50,000+", description: "Quality devices delivered" },
    { label: "Years Experience", value: "15+", description: "Industry expertise" },
    { label: "Customer Rating", value: "4.9★", description: "Average customer rating" }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 3000);
    return () => clearInterval(interval);
  }, [features.length]);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Enhanced Background with animated gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-deep-night-blue via-muted-steel-blue to-shadow-grey">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(149,99,88,0.15),transparent_50%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(249,193,178,0.15),transparent_50%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(149,99,88,0.05),transparent_70%)]" />
      </div>

      {/* Professional Grid Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, rgba(224,224,224,0.3) 1px, transparent 0)`,
          backgroundSize: '50px 50px'
        }} />
      </div>

      {/* Enhanced Moving Particle Effects - Full Hero Section */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Large floating elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-primary rounded-full opacity-10 animate-float blur-sm" />
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-accent-secondary rounded-full opacity-10 animate-float blur-sm" style={{ animationDelay: '1s' }} />
        <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-terra-cotta-rose rounded-full opacity-10 animate-float blur-sm" style={{ animationDelay: '2s' }} />
        <div className="absolute top-1/3 right-1/4 w-20 h-20 bg-pale-peach rounded-full opacity-10 animate-float blur-sm" style={{ animationDelay: '3s' }} />

        {/* Small moving particles - Top section */}
        <div className="absolute top-1/4 left-1/3 w-3 h-3 bg-terra-cotta-rose rounded-full opacity-70 animate-particle-1 shadow-lg" />
        <div className="absolute top-1/6 right-1/3 w-2 h-2 bg-pale-peach rounded-full opacity-80 animate-particle-2 shadow-lg" />
        <div className="absolute top-1/5 left-1/5 w-2.5 h-2.5 bg-accent-secondary rounded-full opacity-60 animate-particle-3 shadow-lg" />
        <div className="absolute top-1/8 right-1/5 w-3 h-3 bg-terra-cotta-rose rounded-full opacity-75 animate-particle-4 shadow-lg" />
        <div className="absolute top-1/3 left-2/3 w-2 h-2 bg-pale-peach rounded-full opacity-70 animate-particle-5 shadow-lg" />
        <div className="absolute top-1/4 right-2/3 w-2.5 h-2.5 bg-accent-secondary rounded-full opacity-80 animate-particle-6 shadow-lg" />

        {/* Small moving particles - Middle section */}
        <div className="absolute top-1/2 left-1/6 w-2.5 h-2.5 bg-terra-cotta-rose rounded-full opacity-75 animate-particle-1 shadow-lg" style={{ animationDelay: '1s' }} />
        <div className="absolute top-2/5 right-1/6 w-2 h-2 bg-pale-peach rounded-full opacity-70 animate-particle-2 shadow-lg" style={{ animationDelay: '2s' }} />
        <div className="absolute top-3/5 left-1/2 w-3 h-3 bg-accent-secondary rounded-full opacity-65 animate-particle-3 shadow-lg" style={{ animationDelay: '1.5s' }} />
        <div className="absolute top-1/2 right-1/2 w-2.5 h-2.5 bg-terra-cotta-rose rounded-full opacity-80 animate-particle-4 shadow-lg" style={{ animationDelay: '0.5s' }} />
        <div className="absolute top-3/5 left-3/4 w-2 h-2 bg-pale-peach rounded-full opacity-75 animate-particle-5 shadow-lg" style={{ animationDelay: '2.5s' }} />
        <div className="absolute top-2/5 right-3/4 w-3 h-3 bg-accent-secondary rounded-full opacity-70 animate-particle-6 shadow-lg" style={{ animationDelay: '1.8s' }} />

        {/* Small moving particles - Bottom section */}
        <div className="absolute bottom-1/4 left-1/3 w-2 h-2 bg-terra-cotta-rose rounded-full opacity-70 animate-particle-1 shadow-lg" style={{ animationDelay: '3s' }} />
        <div className="absolute bottom-1/6 right-1/3 w-2.5 h-2.5 bg-pale-peach rounded-full opacity-80 animate-particle-2 shadow-lg" style={{ animationDelay: '2.2s' }} />
        <div className="absolute bottom-1/5 left-1/5 w-3 h-3 bg-accent-secondary rounded-full opacity-65 animate-particle-3 shadow-lg" style={{ animationDelay: '1.2s' }} />
        <div className="absolute bottom-1/8 right-1/5 w-2 h-2 bg-terra-cotta-rose rounded-full opacity-75 animate-particle-4 shadow-lg" style={{ animationDelay: '2.8s' }} />
        <div className="absolute bottom-1/3 left-2/3 w-2.5 h-2.5 bg-pale-peach rounded-full opacity-70 animate-particle-5 shadow-lg" style={{ animationDelay: '0.8s' }} />
        <div className="absolute bottom-1/4 right-2/3 w-3 h-3 bg-accent-secondary rounded-full opacity-80 animate-particle-6 shadow-lg" style={{ animationDelay: '3.5s' }} />

        {/* Medium particles - Distributed */}
        <div className="absolute top-2/3 left-1/6 w-4 h-4 bg-gradient-primary rounded-full opacity-60 animate-particle-slow-1 shadow-lg" />
        <div className="absolute top-1/5 right-1/6 w-3.5 h-3.5 bg-terra-cotta-rose rounded-full opacity-65 animate-particle-slow-2 shadow-lg" />
        <div className="absolute bottom-1/5 left-3/4 w-3 h-3 bg-pale-peach rounded-full opacity-70 animate-particle-slow-3 shadow-lg" />
        <div className="absolute top-4/5 left-1/2 w-3.5 h-3.5 bg-accent-secondary rounded-full opacity-60 animate-particle-slow-1 shadow-lg" style={{ animationDelay: '4s' }} />
        <div className="absolute top-1/3 right-1/8 w-4 h-4 bg-terra-cotta-rose rounded-full opacity-65 animate-particle-slow-2 shadow-lg" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-2/5 left-1/8 w-3 h-3 bg-pale-peach rounded-full opacity-70 animate-particle-slow-3 shadow-lg" style={{ animationDelay: '3s' }} />

        {/* Extra small particles for richness */}
        <div className="absolute top-1/7 left-2/5 w-1.5 h-1.5 bg-terra-cotta-rose rounded-full opacity-80 animate-particle-1 shadow-md" style={{ animationDelay: '4.5s' }} />
        <div className="absolute top-3/7 right-2/5 w-1.5 h-1.5 bg-pale-peach rounded-full opacity-75 animate-particle-2 shadow-md" style={{ animationDelay: '3.2s' }} />
        <div className="absolute top-5/7 left-3/5 w-1.5 h-1.5 bg-accent-secondary rounded-full opacity-70 animate-particle-3 shadow-md" style={{ animationDelay: '1.7s' }} />
        <div className="absolute bottom-1/7 right-3/5 w-1.5 h-1.5 bg-terra-cotta-rose rounded-full opacity-85 animate-particle-4 shadow-md" style={{ animationDelay: '2.3s' }} />

        {/* Additional particles for more coverage */}
        <div className="absolute top-1/10 left-1/10 w-2 h-2 bg-terra-cotta-rose rounded-full opacity-60 animate-particle-5 shadow-lg" style={{ animationDelay: '5s' }} />
        <div className="absolute top-9/10 right-1/10 w-2 h-2 bg-pale-peach rounded-full opacity-65 animate-particle-6 shadow-lg" style={{ animationDelay: '6s' }} />
        <div className="absolute top-1/2 left-1/10 w-2.5 h-2.5 bg-accent-secondary rounded-full opacity-70 animate-particle-1 shadow-lg" style={{ animationDelay: '7s' }} />
        <div className="absolute top-1/2 right-1/10 w-2.5 h-2.5 bg-terra-cotta-rose rounded-full opacity-75 animate-particle-2 shadow-lg" style={{ animationDelay: '8s' }} />
        <div className="absolute top-1/4 left-9/10 w-2 h-2 bg-pale-peach rounded-full opacity-65 animate-particle-3 shadow-lg" style={{ animationDelay: '9s' }} />
        <div className="absolute top-3/4 right-9/10 w-2 h-2 bg-accent-secondary rounded-full opacity-70 animate-particle-4 shadow-lg" style={{ animationDelay: '10s' }} />

        {/* Corner particles */}
        <div className="absolute top-1/12 left-1/12 w-1.5 h-1.5 bg-terra-cotta-rose rounded-full opacity-80 animate-particle-5 shadow-md" style={{ animationDelay: '11s' }} />
        <div className="absolute top-1/12 right-1/12 w-1.5 h-1.5 bg-pale-peach rounded-full opacity-75 animate-particle-6 shadow-md" style={{ animationDelay: '12s' }} />
        <div className="absolute bottom-1/12 left-1/12 w-1.5 h-1.5 bg-accent-secondary rounded-full opacity-70 animate-particle-1 shadow-md" style={{ animationDelay: '13s' }} />
        <div className="absolute bottom-1/12 right-1/12 w-1.5 h-1.5 bg-terra-cotta-rose rounded-full opacity-85 animate-particle-2 shadow-md" style={{ animationDelay: '14s' }} />

        {/* Center area particles */}
        <div className="absolute top-2/5 left-2/5 w-2 h-2 bg-pale-peach rounded-full opacity-60 animate-particle-3 shadow-lg" style={{ animationDelay: '15s' }} />
        <div className="absolute top-3/5 right-2/5 w-2 h-2 bg-accent-secondary rounded-full opacity-65 animate-particle-4 shadow-lg" style={{ animationDelay: '16s' }} />
        <div className="absolute top-2/5 right-3/5 w-2.5 h-2.5 bg-terra-cotta-rose rounded-full opacity-70 animate-particle-5 shadow-lg" style={{ animationDelay: '17s' }} />
        <div className="absolute top-3/5 left-3/5 w-2.5 h-2.5 bg-pale-peach rounded-full opacity-75 animate-particle-6 shadow-lg" style={{ animationDelay: '18s' }} />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        {/* Centered Main Content */}
        <div className="text-center space-y-12">
          {/* Badge */}
          <Badge className="bg-gradient-primary text-white border-0 px-6 py-3 text-base font-medium animate-fade-in">
            <Star className="w-5 h-5 mr-2" />
            India's #1 Refurbished Tech Store
          </Badge>

          {/* Main Heading - Made Bigger */}
          <div className="space-y-6 animate-slide-up">
            <h1 className="text-5xl md:text-7xl lg:text-8xl xl:text-9xl font-bold leading-tight">
              <span className="text-soft-white">Premium</span>
              <br />
              <span className="text-gradient">Refurbished</span>
              <br />
              <span className="text-soft-white">Computers</span>
            </h1>
            <p className="text-lg md:text-xl text-muted-grey max-w-3xl mx-auto leading-relaxed">
              Discover certified pre-owned computers, laptops, and servers with
              <span className="text-accent-secondary font-semibold"> up to 70% savings</span> and
              <span className="text-accent-secondary font-semibold"> full warranty coverage</span>.
            </p>
          </div>

          {/* CTA Buttons - Made Bigger */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center animate-scale-in">
            <Link href="/products">
              <Button size="lg" className="bg-gradient-primary hover:bg-gradient-primary/90 hover:scale-110 hover:-translate-y-2 hover:shadow-2xl hover:shadow-terra-cotta-rose/30 border-0 px-12 py-6 text-2xl font-bold min-w-[200px] h-16 transition-all duration-300 group">
                Shop Now
                <ArrowRight className="w-7 h-7 ml-4 group-hover:translate-x-1 transition-transform duration-300" />
              </Button>
            </Link>
            <Link href="/about">
              <Button
                size="lg"
                variant="outline"
                className="border-2 border-terra-cotta-rose text-terra-cotta-rose hover:bg-terra-cotta-rose hover:text-white hover:scale-110 hover:-translate-y-2 hover:shadow-2xl hover:shadow-terra-cotta-rose/30 px-12 py-6 text-2xl font-bold min-w-[200px] h-16 transition-all duration-300"
              >
                Learn More
              </Button>
            </Link>
          </div>

          {/* Trust Indicators */}
          <div className="flex flex-wrap gap-6 justify-center items-center pt-8 animate-fade-in">
            <div className="flex items-center gap-3">
              <CheckCircle className="w-6 h-6 text-success" />
              <span className="text-base text-muted-grey font-medium">Certified Quality</span>
            </div>
            <Separator orientation="vertical" className="h-6 bg-shadow-grey" />
            <div className="flex items-center gap-3">
              <CheckCircle className="w-6 h-6 text-success" />
              <span className="text-base text-muted-grey font-medium">1 Year Warranty</span>
            </div>
            <Separator orientation="vertical" className="h-6 bg-shadow-grey" />
            <div className="flex items-center gap-3">
              <CheckCircle className="w-6 h-6 text-success" />
              <span className="text-base text-muted-grey font-medium">Free Shipping</span>
            </div>
          </div>
        </div>

        {/* Professional Stats Cards Section */}
        <div className="mt-20 animate-slide-up">
          <div className="text-center mb-10">
            <h2 className="text-xl md:text-2xl font-bold text-soft-white mb-3">
              Trusted by Thousands of <span className="text-gradient">Happy Customers</span>
            </h2>
            <p className="text-base text-muted-grey max-w-2xl mx-auto">
              Join our growing community of satisfied customers who trust us for quality refurbished technology
            </p>
          </div>

          {/* Compact Stats Grid */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 mb-10">
            {stats.map((stat, index) => (
              <SpotlightCard
                key={index}
                className="group overflow-hidden animate-card-float hover:shadow-2xl hover:shadow-terra-cotta-rose/20 transition-all duration-700"
                spotlightColor="rgba(149, 99, 88, 0.2)"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className="relative p-4 text-center">
                  {/* Icon background */}
                  <div className="w-8 h-8 bg-gradient-primary/10 rounded-full flex items-center justify-center mx-auto mb-3 group-hover:bg-gradient-primary/20 transition-all duration-500">
                    <div className="w-4 h-4 bg-gradient-primary rounded-full group-hover:scale-125 transition-transform duration-500" />
                  </div>

                  {/* Main stat value */}
                  <div className="text-xl md:text-2xl font-bold text-gradient mb-1 group-hover:scale-110 transition-transform duration-500">
                    {stat.value}
                  </div>

                  {/* Label */}
                  <div className="text-xs font-semibold text-soft-white mb-1 group-hover:text-accent-secondary transition-colors duration-300">
                    {stat.label}
                  </div>

                  {/* Description */}
                  <div className="text-xs text-muted-grey leading-relaxed mb-2">
                    {stat.description}
                  </div>

                  {/* Animated progress bar */}
                  <div className="w-full h-0.5 bg-shadow-grey/30 rounded-full overflow-hidden">
                    <div className="h-full bg-gradient-primary rounded-full transform -translate-x-full group-hover:translate-x-0 transition-transform duration-1000 ease-out" />
                  </div>
                </div>
              </SpotlightCard>
            ))}
          </div>

          {/* Compact Features Showcase */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              const isActive = index === currentFeature;
              return (
                <SpotlightCard
                  key={index}
                  className={`cursor-pointer group overflow-hidden transition-all duration-700 hover:shadow-xl hover:shadow-terra-cotta-rose/10 animate-card-float hover:animate-card-bounce ${
                    isActive
                      ? 'border-terra-cotta-rose/60 shadow-lg shadow-terra-cotta-rose/20'
                      : 'hover:border-accent-secondary/40'
                  }`}
                  spotlightColor={isActive ? "rgba(149, 99, 88, 0.3)" : "rgba(149, 99, 88, 0.15)"}
                  style={{ animationDelay: `${index * 0.15}s` }}
                  onClick={() => setCurrentFeature(index)}
                >
                  <div className="relative p-4 text-center">
                    {/* Animated corner accent */}
                    <div className={`absolute top-0 right-0 w-12 h-12 transition-all duration-500 ${
                      isActive ? 'opacity-100' : 'opacity-0 group-hover:opacity-60'
                    }`}>
                      <div className="absolute top-0 right-0 w-0 h-0 border-l-12 border-b-12 border-l-transparent border-b-terra-cotta-rose/20" />
                    </div>

                    {/* Icon container with elegant design and outline */}
                    <div className={`relative w-10 h-10 rounded-xl flex items-center justify-center mx-auto mb-3 transition-all duration-500 border-2 ${
                      isActive
                        ? 'bg-gradient-primary shadow-lg shadow-terra-cotta-rose/30 scale-110 border-terra-cotta-rose/60'
                        : 'bg-shadow-grey/20 group-hover:bg-gradient-primary group-hover:shadow-md group-hover:shadow-terra-cotta-rose/20 group-hover:scale-105 border-shadow-grey/40 group-hover:border-terra-cotta-rose/50'
                    }`}>
                      {/* Icon glow effect */}
                      <div className={`absolute inset-0 rounded-xl transition-opacity duration-500 ${
                        isActive ? 'bg-gradient-primary/20 blur-md' : 'opacity-0 group-hover:opacity-100 bg-gradient-primary/10 blur-md'
                      }`} />

                      {/* Outer glow ring */}
                      <div className={`absolute -inset-1 rounded-xl transition-all duration-500 ${
                        isActive ? 'bg-terra-cotta-rose/20 blur-sm' : 'opacity-0 group-hover:opacity-100 bg-terra-cotta-rose/10 blur-sm'
                      }`} />

                      <IconComponent className={`relative w-5 h-5 transition-all duration-300 ${
                        isActive ? 'text-white drop-shadow-sm' : 'text-muted-grey group-hover:text-white group-hover:drop-shadow-sm'
                      }`} />
                    </div>

                    {/* Title with elegant typography */}
                    <h3 className={`text-xs font-semibold mb-2 transition-all duration-300 ${
                      isActive ? 'text-terra-cotta-rose' : 'text-soft-white group-hover:text-accent-secondary'
                    }`}>
                      {feature.title}
                    </h3>

                    {/* Description with refined styling */}
                    <p className="text-xs text-muted-grey leading-relaxed group-hover:text-muted-grey/80 transition-colors duration-300">
                      {feature.description}
                    </p>

                    {/* Subtle bottom accent line */}
                    <div className={`mt-2 h-0.5 bg-gradient-primary rounded-full transition-all duration-500 ${
                      isActive ? 'w-full opacity-100' : 'w-0 group-hover:w-full opacity-60'
                    }`} />
                  </div>
                </SpotlightCard>
              );
            })}
          </div>

          {/* Compact Customer Testimonial */}
          <div className="mt-10 max-w-2xl mx-auto">
            <SpotlightCard
              className="group overflow-hidden hover:shadow-xl hover:shadow-terra-cotta-rose/10 transition-all duration-700"
              spotlightColor="rgba(149, 99, 88, 0.1)"
            >
              {/* Quote decoration */}
              <div className="absolute top-3 left-4 text-4xl text-terra-cotta-rose/20 font-serif leading-none">"</div>

              <div className="relative p-5 text-center">
                {/* Star rating with elegant animation */}
                <div className="flex justify-center mb-3 gap-1">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className="w-4 h-4 text-warning fill-current transform group-hover:scale-110 transition-transform duration-300"
                      style={{ transitionDelay: `${i * 100}ms` }}
                    />
                  ))}
                </div>

                {/* Testimonial text with refined typography */}
                <blockquote className="text-base md:text-lg text-soft-white font-medium mb-4 leading-relaxed italic">
                  "Exceptional quality and service! Got a refurbished laptop that works like new.
                  The warranty and support team are outstanding. Highly recommended!"
                </blockquote>

                {/* Customer info with elegant layout */}
                <div className="flex items-center justify-center gap-3">
                  <div className="relative">
                    <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center shadow-lg">
                      <span className="text-white font-bold text-xs">RK</span>
                    </div>
                    <div className="absolute -inset-1 bg-gradient-primary rounded-full opacity-20 blur-sm group-hover:opacity-40 transition-opacity duration-300" />
                  </div>
                  <div className="text-left">
                    <div className="text-soft-white font-semibold text-sm">Rajesh Kumar</div>
                    <div className="text-muted-grey text-xs">IT Professional, Mumbai</div>
                  </div>
                </div>

                {/* Subtle bottom decoration */}
                <div className="mt-3 w-12 h-0.5 bg-gradient-primary rounded-full mx-auto opacity-60 group-hover:w-20 transition-all duration-500" />
              </div>
            </SpotlightCard>
          </div>
        </div>
      </div>
    </section>
  );
}
