'use client';

import { useState, useEffect, useRef } from 'react';
import { Plus, X, Edit2, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface SpecialFeaturesManagerProps {
  title: string;
  description?: string;
  initialData?: string[];
  onChange: (data: string[]) => void;
  placeholder?: string;
}

export function SpecialFeaturesManager({
  title,
  description,
  initialData = [],
  onChange,
  placeholder = 'Enter a special feature'
}: SpecialFeaturesManagerProps) {
  const [features, setFeatures] = useState<string[]>(() =>
    initialData.filter(feature => feature.trim())
  );
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editValue, setEditValue] = useState('');
  const [newFeature, setNewFeature] = useState('');
  const [isAdding, setIsAdding] = useState(false);

  // Update parent component when features change
  useEffect(() => {
    const filteredFeatures = features.filter(feature => feature.trim());
    onChange(filteredFeatures);
  }, [features]);

  const addFeature = () => {
    if (newFeature.trim()) {
      setFeatures(prev => [...prev, newFeature.trim()]);
      setNewFeature('');
      setIsAdding(false);
    }
  };

  const updateFeature = (index: number, value: string) => {
    if (value.trim()) {
      setFeatures(prev =>
        prev.map((feature, i) => (i === index ? value.trim() : feature))
      );
    }
    setEditingIndex(null);
    setEditValue('');
  };

  const deleteFeature = (index: number) => {
    setFeatures(prev => prev.filter((_, i) => i !== index));
  };

  const startEditing = (index: number) => {
    setEditingIndex(index);
    setEditValue(features[index]);
    setIsAdding(false);
  };

  const cancelEditing = () => {
    setEditingIndex(null);
    setEditValue('');
    setIsAdding(false);
    setNewFeature('');
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-semibold text-gray-800 mb-2">
          {title}
        </label>
        {description && (
          <p className="text-sm text-gray-600 mb-4">{description}</p>
        )}
      </div>

      {/* Existing Features */}
      {features.length > 0 && (
        <div className="space-y-3">
          {features.map((feature, index) => (
            <FeatureRow
              key={index}
              feature={feature}
              index={index}
              isEditing={editingIndex === index}
              editValue={editValue}
              onEditValueChange={setEditValue}
              onEdit={() => startEditing(index)}
              onSave={(value) => updateFeature(index, value)}
              onCancel={cancelEditing}
              onDelete={() => deleteFeature(index)}
            />
          ))}
        </div>
      )}

      {/* Add New Feature */}
      {isAdding ? (
        <div className="border-2 border-dashed border-[#956358] rounded-lg p-4 bg-orange-50">
          <input
            type="text"
            placeholder={placeholder}
            value={newFeature}
            onChange={(e) => setNewFeature(e.target.value)}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-[#956358] focus:border-[#956358]"
            autoFocus
            onKeyPress={(e) => e.key === 'Enter' && addFeature()}
          />
          <div className="flex justify-end space-x-2 mt-3">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={cancelEditing}
            >
              Cancel
            </Button>
            <Button
              type="button"
              size="sm"
              onClick={addFeature}
              disabled={!newFeature.trim()}
              className="bg-[#956358] hover:bg-[#7d5249]"
            >
              <Check className="h-4 w-4 mr-1" />
              Add
            </Button>
          </div>
        </div>
      ) : (
        <Button
          type="button"
          variant="outline"
          onClick={() => setIsAdding(true)}
          className="w-full border-dashed border-2 border-gray-300 hover:border-[#956358] hover:bg-orange-50 text-gray-600 hover:text-[#956358]"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Feature
        </Button>
      )}

      {features.length === 0 && !isAdding && (
        <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
          <AlertCircle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm">No {title.toLowerCase()} added yet</p>
          <p className="text-xs text-gray-400 mt-1">Click "Add Feature" to get started</p>
        </div>
      )}

      {/* Preview */}
      {features.length > 0 && (
        <div className="mt-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Preview:</h4>
          <div className="flex flex-wrap gap-2">
            {features.map((feature, index) => (
              <Badge key={index} variant="default" className="text-sm bg-[#956358] text-white border-[#956358]">
                {feature}
              </Badge>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

interface FeatureRowProps {
  feature: string;
  index: number;
  isEditing: boolean;
  editValue: string;
  onEditValueChange: (value: string) => void;
  onEdit: () => void;
  onSave: (value: string) => void;
  onCancel: () => void;
  onDelete: () => void;
}

function FeatureRow({
  feature,
  index,
  isEditing,
  editValue,
  onEditValueChange,
  onEdit,
  onSave,
  onCancel,
  onDelete
}: FeatureRowProps) {
  const handleSave = () => {
    if (editValue.trim()) {
      onSave(editValue);
    }
  };

  if (isEditing) {
    return (
      <div className="border-2 border-[#956358] rounded-lg p-4 bg-orange-50">
        <input
          type="text"
          value={editValue}
          onChange={(e) => onEditValueChange(e.target.value)}
          className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-[#956358] focus:border-[#956358]"
          autoFocus
          onKeyPress={(e) => e.key === 'Enter' && handleSave()}
        />
        <div className="flex justify-end space-x-2 mt-3">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            type="button"
            size="sm"
            onClick={handleSave}
            disabled={!editValue.trim()}
            className="bg-[#956358] hover:bg-[#7d5249]"
          >
            <Check className="h-4 w-4 mr-1" />
            Save
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
      <div className="flex-1">
        <span className="text-sm text-gray-900">{feature}</span>
      </div>
      <div className="flex space-x-2 ml-4">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onEdit}
          className="text-gray-600 hover:text-[#956358]"
        >
          <Edit2 className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onDelete}
          className="text-gray-600 hover:text-red-600"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
