import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET() {
  try {
    // Get featured categories for admin
    const { data: featuredCategories, error: categoriesError } = await supabase
      .from('home_featured_categories')
      .select(`
        id,
        category_id,
        custom_image,
        display_order,
        is_active,
        categories (
          id,
          name,
          slug
        )
      `)
      .order('display_order');

    if (categoriesError) {
      console.error('Error fetching featured categories:', categoriesError);
      return NextResponse.json({ error: 'Failed to fetch categories' }, { status: 500 });
    }

    return NextResponse.json({
      categories: featuredCategories || []
    });

  } catch (error) {
    console.error('Error in admin categories API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { category_id, custom_image, display_order } = body;

    if (!category_id) {
      return NextResponse.json({ error: 'Category ID is required' }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('home_featured_categories')
      .insert({
        category_id,
        custom_image,
        display_order: display_order || 0,
        is_active: true
      })
      .select(`
        id,
        category_id,
        custom_image,
        display_order,
        is_active,
        categories (
          id,
          name,
          slug
        )
      `)
      .single();

    if (error) {
      console.error('Error creating featured category:', error);
      return NextResponse.json({ error: 'Failed to create featured category' }, { status: 500 });
    }

    return NextResponse.json({ category: data });

  } catch (error) {
    console.error('Error in admin categories POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
