import { Metada<PERSON> } from 'next';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

export const metadata: Metadata = {
  title: 'Contact Us - Tisha International',
  description: 'Get in touch with Tisha International for inquiries, support, or partnership opportunities. We\'re here to help with all your technology needs.',
};

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--background-primary)] via-[var(--background-secondary)] to-[var(--background-card)]">
      {/* Hero Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Contact <span className="bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light bg-clip-text text-transparent">
              Us
            </span>
          </h1>          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            We&apos;re here to help with all your technology needs. Get in touch with our team for personalized assistance.
          </p>
        </div>
      </section>

      {/* Contact Form and Info Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <Card className="p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Send us a Message</h2>
              <form className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-300 mb-2">
                      First Name *
                    </label>
                    <Input
                      id="firstName"
                      name="firstName"
                      type="text"
                      required
                      placeholder="Enter your first name"
                    />
                  </div>
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-300 mb-2">
                      Last Name *
                    </label>
                    <Input
                      id="lastName"
                      name="lastName"
                      type="text"
                      required
                      placeholder="Enter your last name"
                    />
                  </div>
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                    Email Address *
                  </label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    required
                    placeholder="Enter your email address"
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-300 mb-2">
                    Phone Number
                  </label>
                  <Input
                    id="phone"
                    name="phone"
                    type="tel"
                    placeholder="Enter your phone number"
                  />
                </div>

                <div>
                  <label htmlFor="whatsapp" className="block text-sm font-medium text-gray-300 mb-2">
                    WhatsApp Number
                  </label>
                  <Input
                    id="whatsapp"
                    name="whatsapp"
                    type="tel"
                    placeholder="Enter your WhatsApp number"
                  />
                </div>

                <div>
                  <label htmlFor="inquiryType" className="block text-sm font-medium text-gray-300 mb-2">
                    Inquiry Type *
                  </label>
                  <select
                    id="inquiryType"
                    name="inquiryType"
                    required
                    className="w-full px-4 py-3 bg-background-card border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gradient-to-r focus:ring-primary-gradient-dark focus:border-transparent"
                  >
                    <option value="">Select inquiry type</option>
                    <option value="general">General Inquiry</option>
                    <option value="product">Product Information</option>
                    <option value="bulk">Bulk Order</option>
                    <option value="support">Technical Support</option>
                    <option value="dealer">Dealer Partnership</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows={6}
                    placeholder="Tell us about your requirements or questions..."
                    className="w-full px-4 py-3 bg-background-card border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gradient-to-r focus:ring-primary-gradient-dark focus:border-transparent resize-vertical"
                  ></textarea>
                </div>

                <Button variant="primary" size="lg" className="w-full bg-gradient-to-r from-[#f9c1b2] to-[#956358] hover:from-[#956358] hover:to-[#f9c1b2]">
                  Send Message
                </Button>
              </form>
            </Card>

            {/* Contact Information */}
            <div className="space-y-8">
              {/* Contact Details */}
              <Card className="p-8">
                <h2 className="text-2xl font-bold text-white mb-6">Get in Touch</h2>
                <div className="space-y-6">
                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-lg">📧</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-1">Email</h3>
                      <p className="text-gray-300"><EMAIL></p>
                      <p className="text-gray-300"><EMAIL></p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-lg">📞</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-1">Phone</h3>
                      <p className="text-gray-300">+****************</p>
                      <p className="text-gray-300">+****************</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-lg">💬</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-1">WhatsApp</h3>
                      <p className="text-gray-300">+****************</p>
                      <p className="text-gray-400 text-sm">Available 24/7 for support</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-4">
                    <div className="w-12 h-12 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center flex-shrink-0">
                      <span className="text-white text-lg">📍</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-1">Address</h3>
                      <p className="text-gray-300">
                        123 Technology Drive<br />
                        Business District<br />
                        City, State 12345
                      </p>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Business Hours */}
              <Card className="p-8">
                <h2 className="text-2xl font-bold text-white mb-6">Business Hours</h2>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-300">Monday - Friday</span>
                    <span className="text-white">9:00 AM - 6:00 PM</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Saturday</span>
                    <span className="text-white">10:00 AM - 4:00 PM</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-300">Sunday</span>
                    <span className="text-white">Closed</span>
                  </div>
                  <div className="mt-4 pt-4 border-t border-gray-600">
                    <p className="text-gray-400 text-sm">
                      * Emergency technical support available 24/7 for enterprise customers
                    </p>
                  </div>
                </div>
              </Card>

              {/* Quick Links */}
              <Card className="p-8">
                <h2 className="text-2xl font-bold text-white mb-6">Quick Links</h2>
                <div className="grid grid-cols-2 gap-4">
                  <Button variant="outline" className="justify-start">
                    Product Catalog
                  </Button>
                  <Button variant="outline" className="justify-start">
                    Bulk Orders
                  </Button>
                  <Button variant="outline" className="justify-start">
                    Technical Support
                  </Button>
                  <Button variant="outline" className="justify-start">
                    Dealer Program
                  </Button>
                </div>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto">
          <Card className="p-8">
            <h2 className="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div className="space-y-6">
              <div className="border-b border-gray-600 pb-6">
                <h3 className="text-lg font-semibold text-white mb-2">How quickly do you respond to inquiries?</h3>
                <p className="text-gray-300">
                  We typically respond to all inquiries within 2-4 hours during business hours. 
                  Urgent requests are handled immediately via our WhatsApp support line.
                </p>
              </div>

              <div className="border-b border-gray-600 pb-6">
                <h3 className="text-lg font-semibold text-white mb-2">Do you offer on-site technical support?</h3>
                <p className="text-gray-300">
                  Yes, we provide on-site technical support for bulk orders and enterprise customers. 
                  Contact us to discuss your specific requirements and availability in your area.
                </p>
              </div>

              <div className="border-b border-gray-600 pb-6">
                <h3 className="text-lg font-semibold text-white mb-2">What information should I include in my inquiry?</h3>
                <p className="text-gray-300">
                  Please include your specific requirements (device type, quantity, specifications), 
                  timeline, budget range, and contact preferences. This helps us provide more accurate recommendations.
                </p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-white mb-2">Can I visit your facility?</h3>                <p className="text-gray-300">
                  Yes, we welcome visits to our facility by appointment. Please contact us in advance 
                  to schedule a tour and product demonstration. We&apos;re happy to show you our refurbishment process.
                </p>
              </div>
            </div>
          </Card>
        </div>
      </section>
    </div>
  );
}
