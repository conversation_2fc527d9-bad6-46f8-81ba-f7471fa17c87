'use client';

import { useState, useEffect } from 'react';
import { formatPrice } from '@/lib/utils/index';
import { CategoryCard } from '@/components/ui/expandable-card';

interface Product {
  id: string;
  name: string;
  price: number;
  images: string[];
  brand: string;
  model: string;
  show_price?: boolean;
}

interface CategoryData {
  id: string;
  name: string;
  slug: string;
  image: string;
  products: Product[];
}

interface GallerySettings {
  products_per_category: number;
  grid_columns: number;
  show_price: boolean;
  hover_animation: boolean;
  auto_hide_delay: number;
}

interface EnhancedCategoryShowcaseProps {
  categories: CategoryData[];
}



export default function EnhancedCategoryShowcase({ categories }: EnhancedCategoryShowcaseProps) {
  const [gallerySettings, setGallerySettings] = useState<GallerySettings>({
    products_per_category: 8,
    grid_columns: 2,
    show_price: true,
    hover_animation: true,
    auto_hide_delay: 3000
  });

  useEffect(() => {
    fetchGallerySettings();
  }, []);

  const fetchGallerySettings = async () => {
    try {
      const response = await fetch('/api/admin/settings/category-gallery');
      if (response.ok) {
        const settings = await response.json();
        setGallerySettings(settings);
      }
    } catch (error) {
      console.error('Error fetching gallery settings:', error);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 px-8 py-8">
      {categories.map((category) => (
        <div key={category.id} className="relative">
          <CategoryCard
            category={category}
            gallerySettings={gallerySettings}
            formatPrice={formatPrice}
          />
        </div>
      ))}
    </div>
  );
}
