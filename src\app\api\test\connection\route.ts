import { NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    const supabase = await getSupabaseServerClient();

    // Test basic connection by fetching roles
    const { data: roles, error: rolesError } = await supabase
      .from('roles')
      .select('*');

    if (rolesError) {
      throw rolesError;
    }

    // Test admin users query structure
    const { data: adminUsers, error: adminError } = await supabase
      .from('admin_users')
      .select(`
        id,
        email,
        full_name,
        is_active,
        role_id,
        roles (
          id,
          name,
          permissions
        )
      `)
      .limit(1);

    if (adminError) {
      throw adminError;
    }

    return NextResponse.json({
      success: true,
      connection: 'OK',
      roles: roles,
      adminUsers: adminUsers,
      environment: {
        supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set',
        supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set'
      }
    });

  } catch (error) {
    console.error('Database test error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    );
  }
}
