import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function POST() {
  try {
    const supabase = createSupabaseAdminClient();

    // Get a category first
    const { data: categories } = await supabase
      .from('categories')
      .select('id')
      .limit(1);

    if (!categories || categories.length === 0) {
      return NextResponse.json(
        { error: 'No categories found. Please create categories first.' },
        { status: 400 }
      );
    }

    // Test product data
    const testProduct = {
      name: 'Test Laptop Pro',
      brand: 'TechBrand',
      description: 'A high-performance laptop for testing purposes',
      price: 1299.99,
      category_id: categories[0].id,
      specifications: {
        processor: 'Intel i7-12700H',
        ram: '16GB DDR4',
        storage: '512GB SSD',
        display: '15.6" FHD',
        graphics: 'NVIDIA RTX 3060'
      },
      features: [
        'High-performance processor',
        'Fast SSD storage',
        'Dedicated graphics card',
        'Full HD display',
        'Backlit keyboard'
      ],
      is_active: true
    };

    // Generate slug
    const slug = testProduct.name.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    // Create product
    const { data: product, error } = await supabase
      .from('products')
      .insert({
        ...testProduct,
        slug,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select(`
        *,
        categories (
          id,
          name,
          slug
        )
      `)
      .single();

    if (error) {
      console.error('Error creating test product:', error);
      return NextResponse.json(
        { error: 'Failed to create test product', details: error },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Test product created successfully',
      product
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
