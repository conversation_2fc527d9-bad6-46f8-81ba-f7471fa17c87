import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { requireAdminAuth } from '@/lib/auth/admin';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// GET - Fetch all inquiries with chat statistics for admin
export async function GET() {
  try {
    // Verify admin authentication and permissions
    const authResult = await requireAdminAuth('chat', 'read');
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Fetch inquiries with chat message counts
    const { data: inquiries, error } = await supabase
      .from('inquiries')
      .select(`
        *,
        product:products(
          id,
          name,
          brand,
          model,
          price,
          images
        ),
        message_count:chat_messages(count),
        last_message:chat_messages(created_at)
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching inquiries:', error);
      return NextResponse.json({ error: 'Failed to fetch inquiries' }, { status: 500 });
    }

    // Process the data to include chat statistics
    const processedInquiries = await Promise.all(
      inquiries.map(async (inquiry) => {
        // Get message count for this inquiry
        const { count: messageCount } = await supabase
          .from('chat_messages')
          .select('*', { count: 'exact', head: true })
          .eq('inquiry_id', inquiry.id);

        // Get last message timestamp
        const { data: lastMessage } = await supabase
          .from('chat_messages')
          .select('created_at')
          .eq('inquiry_id', inquiry.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .single();

        // For simplicity, we'll consider unread messages as messages from users (not admins)
        // In a real implementation, you'd have a proper read/unread tracking system
        const { count: unreadCount } = await supabase
          .from('chat_messages')
          .select('*', { count: 'exact', head: true })
          .eq('inquiry_id', inquiry.id)
          .eq('sender_type', 'user');

        return {
          ...inquiry,
          message_count: messageCount || 0,
          last_message_at: lastMessage?.created_at || null,
          unread_messages: unreadCount || 0,
        };
      })
    );

    return NextResponse.json({ inquiries: processedInquiries });

  } catch (error) {
    console.error('Error in admin chats API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
