import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { requireAdminAuth } from '@/lib/auth/admin';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// GET - Fetch specific inquiry details for admin
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin authentication and permissions
    const authResult = await requireAdminAuth('inquiries', 'read');
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { id: inquiryId } = await params;

    // Fetch inquiry with product details
    const { data: inquiry, error } = await supabase
      .from('inquiries')
      .select(`
        *,
        product:products(
          id,
          name,
          brand,
          model,
          price,
          images,
          description,
          specifications
        )
      `)
      .eq('id', inquiryId)
      .single();

    if (error || !inquiry) {
      console.error('Error fetching inquiry:', error);
      return NextResponse.json({ error: 'Inquiry not found' }, { status: 404 });
    }

    return NextResponse.json({ inquiry });

  } catch (error) {
    console.error('Error in admin inquiry API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
