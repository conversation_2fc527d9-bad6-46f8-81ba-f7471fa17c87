'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Trash2, Download, AlertTriangle, CheckCircle, XCircle, ArrowLeft } from 'lucide-react';

interface RemovalResult {
  success: boolean;
  message: string;
  backup: any;
  databaseUpdated: boolean;
  storageDeleted: boolean;
  errors: string[];
}

export default function RemoveImagesPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<RemovalResult | null>(null);
  const [options, setOptions] = useState({
    removeFromDatabase: true,
    deleteFromStorage: false,
    backupData: true
  });

  const handleRemoveImages = async () => {
    if (!confirm('Are you sure you want to remove all product images? This action cannot be undone unless you have a backup.')) {
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/admin/remove-product-images', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(options),
      });

      const data = await response.json();
      setResult(data);

      if (data.success) {
        alert('Images removed successfully!');
      } else {
        alert('Image removal completed with some errors. Check the results below.');
      }
    } catch (error) {
      console.error('Error removing images:', error);
      setResult({
        success: false,
        message: 'Failed to remove images due to network error',
        backup: null,
        databaseUpdated: false,
        storageDeleted: false,
        errors: ['Network error occurred']
      });
    } finally {
      setIsLoading(false);
    }
  };

  const downloadBackup = () => {
    if (!result?.backup) return;

    const dataStr = JSON.stringify(result.backup, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `product-images-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.back()}
            className="p-2 hover:bg-gray-100 rounded-md transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-600" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Remove Product Images</h1>
            <p className="text-gray-600">Remove image links from all products in the database</p>
          </div>
        </div>
      </div>

      {/* Warning Card */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex items-start">
          <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3 flex-shrink-0" />
          <div>
            <h3 className="text-sm font-medium text-yellow-800">Important Warning</h3>
            <p className="text-sm text-yellow-700 mt-1">
              This action will remove all image links from your product database. Make sure you understand the implications:
            </p>
            <ul className="text-sm text-yellow-700 mt-2 list-disc list-inside space-y-1">
              <li>Products will no longer display images on your website</li>
              <li>This action affects all products in your database</li>
              <li>Always create a backup before proceeding</li>
              <li>Deleting from storage will permanently remove image files</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Options Card */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Removal Options</h2>
        
        <div className="space-y-4">
          <div className="flex items-center">
            <input
              id="removeFromDatabase"
              type="checkbox"
              checked={options.removeFromDatabase}
              onChange={(e) => setOptions(prev => ({ ...prev, removeFromDatabase: e.target.checked }))}
              className="h-4 w-4 text-[#956358] focus:ring-[#956358] border-gray-300 rounded"
            />
            <label htmlFor="removeFromDatabase" className="ml-2 text-sm text-gray-700">
              Remove image links from database (recommended)
            </label>
          </div>

          <div className="flex items-center">
            <input
              id="deleteFromStorage"
              type="checkbox"
              checked={options.deleteFromStorage}
              onChange={(e) => setOptions(prev => ({ ...prev, deleteFromStorage: e.target.checked }))}
              className="h-4 w-4 text-[#956358] focus:ring-[#956358] border-gray-300 rounded"
            />
            <label htmlFor="deleteFromStorage" className="ml-2 text-sm text-gray-700">
              Delete actual image files from storage (permanent)
            </label>
          </div>

          <div className="flex items-center">
            <input
              id="backupData"
              type="checkbox"
              checked={options.backupData}
              onChange={(e) => setOptions(prev => ({ ...prev, backupData: e.target.checked }))}
              className="h-4 w-4 text-[#956358] focus:ring-[#956358] border-gray-300 rounded"
            />
            <label htmlFor="backupData" className="ml-2 text-sm text-gray-700">
              Create backup of image data (highly recommended)
            </label>
          </div>
        </div>

        <div className="mt-6">
          <button
            onClick={handleRemoveImages}
            disabled={isLoading || !options.removeFromDatabase}
            className="inline-flex items-center px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                Processing...
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4 mr-2" />
                Remove Images
              </>
            )}
          </button>
        </div>
      </div>

      {/* Results Card */}
      {result && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-medium text-gray-900">Removal Results</h2>
            {result.backup && (
              <button
                onClick={downloadBackup}
                className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <Download className="h-4 w-4 mr-2" />
                Download Backup
              </button>
            )}
          </div>

          {/* Status */}
          <div className={`flex items-center p-3 rounded-md mb-4 ${
            result.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'
          }`}>
            {result.success ? (
              <CheckCircle className="h-5 w-5 mr-2" />
            ) : (
              <XCircle className="h-5 w-5 mr-2" />
            )}
            <span className="font-medium">{result.message}</span>
          </div>

          {/* Details */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className={`p-3 rounded-md ${result.databaseUpdated ? 'bg-green-50' : 'bg-gray-50'}`}>
              <div className="text-sm font-medium text-gray-900">Database Updated</div>
              <div className={`text-sm ${result.databaseUpdated ? 'text-green-600' : 'text-gray-600'}`}>
                {result.databaseUpdated ? 'Yes' : 'No'}
              </div>
            </div>
            <div className={`p-3 rounded-md ${result.storageDeleted ? 'bg-green-50' : 'bg-gray-50'}`}>
              <div className="text-sm font-medium text-gray-900">Storage Deleted</div>
              <div className={`text-sm ${result.storageDeleted ? 'text-green-600' : 'text-gray-600'}`}>
                {result.storageDeleted ? 'Yes' : 'No'}
              </div>
            </div>
            <div className={`p-3 rounded-md ${result.backup ? 'bg-green-50' : 'bg-gray-50'}`}>
              <div className="text-sm font-medium text-gray-900">Backup Created</div>
              <div className={`text-sm ${result.backup ? 'text-green-600' : 'text-gray-600'}`}>
                {result.backup ? 'Yes' : 'No'}
              </div>
            </div>
          </div>

          {/* Backup Info */}
          {result.backup && (
            <div className="bg-blue-50 p-3 rounded-md mb-4">
              <div className="text-sm font-medium text-blue-900">Backup Information</div>
              <div className="text-sm text-blue-700 mt-1">
                Total Products: {result.backup.totalProducts} | 
                Products with Images: {result.backup.productsWithImages} | 
                Created: {new Date(result.backup.timestamp).toLocaleString()}
              </div>
            </div>
          )}

          {/* Errors */}
          {result.errors && result.errors.length > 0 && (
            <div className="bg-red-50 p-3 rounded-md">
              <div className="text-sm font-medium text-red-900 mb-2">Errors:</div>
              <ul className="text-sm text-red-700 space-y-1">
                {result.errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
