import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function POST() {
  try {
    const supabase = createSupabaseAdminClient();

    // Sample categories for testing
    const categories = [
      {
        name: 'Lapt<PERSON>',
        slug: 'laptops',
        description: 'Portable computers and laptops',
        is_active: true
      },
      {
        name: 'Desktops',
        slug: 'desktops', 
        description: 'Desktop computers and workstations',
        is_active: true
      },
      {
        name: 'Accessories',
        slug: 'accessories',
        description: 'Computer accessories and peripherals',
        is_active: true
      },
      {
        name: 'Monitors',
        slug: 'monitors',
        description: 'Computer monitors and displays',
        is_active: true
      },
      {
        name: 'Printers',
        slug: 'printers',
        description: 'Printers and printing equipment',
        is_active: true
      }
    ];

    // Check if categories already exist
    const { data: existingCategories } = await supabase
      .from('categories')
      .select('name');

    if (existingCategories && existingCategories.length > 0) {
      return NextResponse.json({
        message: 'Categories already exist',
        categories: existingCategories
      });
    }

    // Insert categories
    const { data: insertedCategories, error } = await supabase
      .from('categories')
      .insert(categories)
      .select();

    if (error) {
      console.error('Error inserting categories:', error);
      return NextResponse.json(
        { error: 'Failed to create categories' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Categories created successfully',
      categories: insertedCategories
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
