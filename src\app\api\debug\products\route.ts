import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseAdminClient();
    
    // Get all products with their basic info and slugs
    const { data: products, error } = await supabase
      .from('products')
      .select('id, name, slug, status, is_active')
      .order('created_at', { ascending: false })
      .limit(20);

    if (error) {
      console.error('Error fetching products for debug:', error);
      return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 });
    }

    return NextResponse.json({
      products: products || [],
      count: products?.length || 0
    });

  } catch (error) {
    console.error('Unexpected error in debug API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
