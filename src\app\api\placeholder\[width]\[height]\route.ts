import { NextRequest, NextResponse } from 'next/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ width: string; height: string }> }
) {
  const { width, height } = await params;
  const w = parseInt(width) || 400;
  const h = parseInt(height) || 400;

  // Create a simple SVG placeholder
  const svg = `
    <svg width="${w}" height="${h}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#273549;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#1a2332;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)"/>
      <rect x="10%" y="10%" width="80%" height="80%" fill="none" stroke="#956358" stroke-width="2" stroke-dasharray="5,5" opacity="0.5"/>
      <text x="50%" y="45%" text-anchor="middle" fill="#956358" font-family="Arial, sans-serif" font-size="${Math.min(w, h) / 15}px" opacity="0.7">
        Product Image
      </text>
      <text x="50%" y="60%" text-anchor="middle" fill="#7a8396" font-family="Arial, sans-serif" font-size="${Math.min(w, h) / 20}px" opacity="0.6">
        ${w} × ${h}
      </text>
    </svg>
  `;

  return new NextResponse(svg, {
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'public, max-age=31536000',
    },
  });
}
