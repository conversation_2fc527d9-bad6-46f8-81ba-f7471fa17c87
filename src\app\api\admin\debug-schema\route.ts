import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    const supabase = createSupabaseAdminClient();

    // Try to get all products to see what columns exist
    const { data: allProducts, error: allError } = await supabase
      .from('products')
      .select('*')
      .limit(5);

    if (allError) {
      console.error('Error fetching all products:', allError);
    }

    // Try to get categories
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('*')
      .limit(5);

    if (categoriesError) {
      console.error('Error fetching categories:', categoriesError);
    }

    // Try to get users
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(5);

    if (usersError) {
      console.error('Error fetching users:', usersError);
    }

    return NextResponse.json({
      products: allProducts || null,
      productsError: allError,
      categories: categories || null,
      categoriesError,
      users: users || null,
      usersError,
      message: 'Database structure debug info'
    });

  } catch (error) {
    console.error('Debug schema error:', error);
    return NextResponse.json(
      { error: 'Failed to debug schema' },
      { status: 500 }
    );
  }
}
