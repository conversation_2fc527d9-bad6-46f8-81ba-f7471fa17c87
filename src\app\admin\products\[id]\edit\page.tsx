'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useParams } from 'next/navigation';
import Image from 'next/image';
import { Save, ArrowLeft, Package, Upload, X } from 'lucide-react';
import { SpecificationManager } from '@/components/admin/SpecificationManager';
import { SpecialFeaturesManager } from '@/components/admin/SpecialFeaturesManager';
import { AdditionalFeaturesManager } from '@/components/admin/AdditionalFeaturesManager';

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface Brand {
  id: string;
  name: string;
  slug: string;
}

interface Condition {
  id: string;
  name: string;
  slug: string;
}

interface Product {
  id: string;
  name: string;
  brand_id: string;
  condition_id: string;
  model: string;
  product_id: string;
  description: string;
  price: number;
  show_price: boolean;
  category_id: string;
  stock_quantity: number;
  low_stock_threshold: number;
  status: string;
  specifications: any;
  special_features: string[];
  additional_features: any;
  images: string[];
  brands?: Brand;
  conditions?: Condition;
  categories?: Category;
}

export default function EditProductPage() {
  const router = useRouter();
  const params = useParams();
  const productId = params?.id as string;

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingProduct, setIsLoadingProduct] = useState(true);
  const [categories, setCategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [conditions, setConditions] = useState<Condition[]>([]);
  const [product, setProduct] = useState<Product | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    brand_id: '',
    condition_id: '',
    model: '',
    product_id: '',
    description: '',
    price: '',
    show_price: true,
    category_id: '',
    stock_quantity: '',
    low_stock_threshold: '',
    status: 'active'
  });

  // Manager component states
  const [specifications, setSpecifications] = useState<Record<string, string>>({});
  const [specialFeatures, setSpecialFeatures] = useState<string[]>([]);
  const [additionalFeatures, setAdditionalFeatures] = useState<Record<string, any>>({});

  // Image management states
  const [currentImages, setCurrentImages] = useState<string[]>([]); // Existing images from database
  const [imagesToDelete, setImagesToDelete] = useState<string[]>([]); // Images marked for deletion
  const [newImages, setNewImages] = useState<File[]>([]); // New images to upload
  const [newImageUrls, setNewImageUrls] = useState<string[]>([]); // Preview URLs for new images
  const [isUploadingImages, setIsUploadingImages] = useState(false);

  // Generate 6-digit unique product ID
  const generateProductId = () => {
    return Math.floor(100000 + Math.random() * 900000).toString();
  };

  // Memoize onChange functions to prevent infinite re-renders
  const handleSpecificationsChange = useCallback((data: Record<string, string>) => {
    setSpecifications(data);
  }, []);

  const handleSpecialFeaturesChange = useCallback((data: string[]) => {
    setSpecialFeatures(data);
  }, []);

  const handleAdditionalFeaturesChange = useCallback((data: Record<string, any>) => {
    setAdditionalFeatures(data);
  }, []);

  useEffect(() => {
    if (productId) {
      fetchCategories();
      fetchBrands();
      fetchConditions();
      fetchProduct();
    }
  }, [productId]);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await fetch('/api/admin/brands?status=active&limit=100');
      if (response.ok) {
        const data = await response.json();
        setBrands(data.brands || []);
      }
    } catch (error) {
      console.error('Error fetching brands:', error);
    }
  };

  const fetchConditions = async () => {
    try {
      const response = await fetch('/api/admin/conditions?status=active&limit=100');
      if (response.ok) {
        const data = await response.json();
        setConditions(data.conditions || []);
      }
    } catch (error) {
      console.error('Error fetching conditions:', error);
    }
  };

  const fetchProduct = async () => {
    try {
      const response = await fetch(`/api/admin/products/${productId}`);
      if (response.ok) {
        const data = await response.json();
        const product = data.product;
        setProduct(product);

        // Populate form with existing data
        setFormData({
          name: product.name || '',
          brand_id: product.brand_id || '',
          condition_id: product.condition_id || '',
          model: product.model || '',
          product_id: product.product_id || '',
          description: product.description || '',
          price: product.price?.toString() || '',
          show_price: product.show_price !== undefined ? product.show_price : true,
          category_id: product.category_id || '',
          stock_quantity: product.stock_quantity?.toString() || '0',
          low_stock_threshold: product.low_stock_threshold?.toString() || '5',
          status: product.status || 'active'
        });

        // Populate manager component data
        setSpecifications(product.specifications || {});
        setSpecialFeatures(product.special_features || []);
        setAdditionalFeatures(product.additional_features || {});
        setCurrentImages(product.images || []);
      } else {
        alert('Product not found');
        router.push('/admin/products');
      }
    } catch (error) {
      console.error('Error fetching product:', error);
      alert('Failed to load product');
      router.push('/admin/products');
    } finally {
      setIsLoadingProduct(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length === 0) return;

    // Store files locally for upload during form submission
    setNewImages(prev => [...prev, ...files]);

    // Create preview URLs
    files.forEach(file => {
      const url = URL.createObjectURL(file);
      setNewImageUrls(prev => [...prev, url]);
    });

    // Clear the input
    e.target.value = '';
  };

  const removeCurrentImage = (index: number) => {
    const imageToRemove = currentImages[index];

    // Add to deletion list (will be deleted from Supabase when form is submitted)
    setImagesToDelete(prev => [...prev, imageToRemove]);

    // Remove from current images display
    setCurrentImages(prev => prev.filter((_, i) => i !== index));
  };

  const removeNewImage = (index: number) => {
    setNewImages(prev => prev.filter((_, i) => i !== index));
    setNewImageUrls(prev => {
      // Revoke the URL to free memory
      URL.revokeObjectURL(prev[index]);
      return prev.filter((_, i) => i !== index);
    });
  };

  const uploadImagesToSupabase = async (files: File[]): Promise<string[]> => {
    const uploadedUrls: string[] = [];

    for (const file of files) {
      const formData = new FormData();
      formData.append('file', file);

      try {
        const response = await fetch('/api/admin/upload-image', {
          method: 'POST',
          body: formData,
        });

        if (response.ok) {
          const data = await response.json();
          uploadedUrls.push(data.url);
        } else {
          console.error('Failed to upload image:', file.name);
        }
      } catch (error) {
        console.error('Error uploading image:', error);
      }
    }

    return uploadedUrls;
  };

  const deleteImagesFromSupabase = async (imageUrls: string[]): Promise<void> => {
    for (const url of imageUrls) {
      try {
        // Send URL as query parameter, not in body
        const response = await fetch(`/api/admin/delete-image?url=${encodeURIComponent(url)}`, {
          method: 'DELETE',
        });

        if (!response.ok) {
          const errorData = await response.json();
          console.error('Failed to delete image:', url, errorData);
        } else {
          console.log('Successfully deleted image:', url);
        }
      } catch (error) {
        console.error('Error deleting image:', error);
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Upload new images first if any
      let uploadedImageUrls: string[] = [];
      if (newImages.length > 0) {
        setIsUploadingImages(true);
        uploadedImageUrls = await uploadImagesToSupabase(newImages);
        setIsUploadingImages(false);
      }

      // Delete images marked for deletion from Supabase
      if (imagesToDelete.length > 0) {
        await deleteImagesFromSupabase(imagesToDelete);
      }

      // Combine existing images with newly uploaded ones
      const allImages = [...currentImages, ...uploadedImageUrls];

      const productData = {
        ...formData,
        price: parseFloat(formData.price),
        stock_quantity: parseInt(formData.stock_quantity) || 0,
        low_stock_threshold: parseInt(formData.low_stock_threshold) || 5,
        specifications,
        special_features: specialFeatures,
        additional_features: additionalFeatures,
        images: allImages
      };

      const response = await fetch(`/api/admin/products/${productId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      if (response.ok) {
        alert('Product updated successfully!');
        router.push('/admin/products');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error('Error updating product:', error);
      alert('Failed to update product. Please check the form data.');
    } finally {
      setIsLoading(false);
      setIsUploadingImages(false);
    }
  };

  if (isLoadingProduct) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#956358]"></div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-semibold text-gray-900">Product not found</h2>
        <button
          onClick={() => router.push('/admin/products')}
          className="mt-4 text-[#956358] hover:text-[#956358]/80"
        >
          Back to Products
        </button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  router.push('/admin/products');
                }}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#956358]"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                  <Package className="h-8 w-8 mr-3 text-[#956358]" />
                  Edit Product
                </h1>
                <p className="mt-1 text-lg text-gray-600">Update product information and settings</p>
              </div>
            </div>
          </div>

      <form onSubmit={handleSubmit} className="space-y-8">
        <div className="bg-white shadow-lg rounded-lg border border-gray-200">
          <div className="px-6 py-8 sm:p-8">
            <div className="border-b border-gray-200 pb-6 mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-2">Basic Information</h3>
              <p className="text-sm text-gray-600">Update the core details for this product</p>
            </div>

            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Product Name <span className="text-red-600">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  required
                  value={formData.name}
                  onChange={handleInputChange}
                  className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                  placeholder="Enter product name"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Brand <span className="text-red-600">*</span>
                </label>
                <select
                  name="brand_id"
                  required
                  value={formData.brand_id}
                  onChange={handleInputChange}
                  className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 bg-white focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                >
                  <option value="" className="text-gray-500">Select a brand</option>
                  {brands.map((brand) => (
                    <option key={brand.id} value={brand.id} className="text-gray-900">
                      {brand.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Model <span className="text-red-600">*</span>
                </label>
                <input
                  type="text"
                  name="model"
                  required
                  value={formData.model}
                  onChange={handleInputChange}
                  className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                  placeholder="Enter product model"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Product ID <span className="text-red-600">*</span>
                </label>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    name="product_id"
                    required
                    value={formData.product_id}
                    onChange={handleInputChange}
                    className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                    placeholder="6-digit product ID"
                    maxLength={6}
                    pattern="[0-9]{6}"
                  />
                  <button
                    type="button"
                    onClick={() => setFormData(prev => ({ ...prev, product_id: generateProductId() }))}
                    className="px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-[#956358] transition-colors"
                    title="Generate new ID"
                  >
                    🔄
                  </button>
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  Unique 6-digit identifier for this product
                </p>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Category <span className="text-red-600">*</span>
                </label>
                <select
                  name="category_id"
                  required
                  value={formData.category_id}
                  onChange={handleInputChange}
                  className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 bg-white focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                >
                  <option value="" className="text-gray-500">Select a category</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id} className="text-gray-900">
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Condition <span className="text-red-600">*</span>
                </label>
                <select
                  name="condition_id"
                  required
                  value={formData.condition_id}
                  onChange={handleInputChange}
                  className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 bg-white focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                >
                  <option value="" className="text-gray-500">Select a condition</option>
                  {conditions.map((condition) => (
                    <option key={condition.id} value={condition.id} className="text-gray-900">
                      {condition.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Price <span className="text-red-600">*</span>
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <span className="text-gray-600 text-lg font-medium">₹</span>
                  </div>
                  <input
                    type="number"
                    name="price"
                    required
                    step="0.01"
                    min="0"
                    value={formData.price}
                    onChange={handleInputChange}
                    className="block w-full pl-8 pr-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                    placeholder="0.00"
                  />
                </div>
                <div className="mt-3 flex items-center">
                  <input
                    type="checkbox"
                    name="show_price"
                    id="show_price"
                    checked={formData.show_price}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-[#956358] focus:ring-[#956358] border-gray-300 rounded"
                  />
                  <label htmlFor="show_price" className="ml-2 block text-sm text-gray-700">
                    Show price to customers
                  </label>
                </div>
                <p className="mt-1 text-sm text-gray-500">
                  Uncheck to hide price and show "Contact for Price" instead
                </p>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Stock Quantity <span className="text-red-500">*</span>
                </label>
                <input
                  type="number"
                  name="stock_quantity"
                  required
                  min="0"
                  value={formData.stock_quantity}
                  onChange={handleInputChange}
                  className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                  placeholder="0"
                />
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Low Stock Threshold
                </label>
                <input
                  type="number"
                  name="low_stock_threshold"
                  min="0"
                  value={formData.low_stock_threshold}
                  onChange={handleInputChange}
                  className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                  placeholder="5"
                />
                <p className="mt-1 text-sm text-gray-500">
                  Alert when stock falls below this number
                </p>
              </div>

              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">
                  Product Status <span className="text-red-500">*</span>
                </label>
                <select
                  name="status"
                  required
                  value={formData.status}
                  onChange={handleInputChange}
                  className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                >
                  <option value="active">Active</option>
                  <option value="deactivated">Deactivated</option>
                  <option value="archived">Archived</option>
                  <option value="private">Private</option>
                </select>
                <p className="mt-1 text-sm text-gray-500">
                  Active: Visible to customers | Deactivated: Hidden but manageable | Archived: Read-only | Private: Internal use only
                </p>
              </div>
            </div>

            <div className="mt-8 space-y-8">
              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">Description</label>
                <textarea
                  name="description"
                  rows={5}
                  value={formData.description}
                  onChange={handleInputChange}
                  className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors resize-none"
                  placeholder="Enter a detailed product description..."
                />
              </div>

              <SpecialFeaturesManager
                title="Special Features"
                description="Add key features that make this product stand out"
                initialData={specialFeatures}
                onChange={handleSpecialFeaturesChange}
                placeholder="Business-grade reliability"
              />

              <SpecificationManager
                title="Product Specifications"
                description="Add technical specifications and product details"
                initialData={specifications}
                onChange={handleSpecificationsChange}
                placeholder={{ key: 'Processor', value: 'Intel i7' }}
              />

              <AdditionalFeaturesManager
                title="Additional Features"
                description="Add extra features with flexible value types"
                initialData={additionalFeatures}
                onChange={handleAdditionalFeaturesChange}
                placeholder={{ key: 'Webcam', value: 'HD 720p' }}
              />

              <div>
                <label className="block text-sm font-semibold text-gray-800 mb-2">Product Images</label>

                {/* Current Images */}
                {currentImages.length > 0 && (
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">Current Images</h4>
                    <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
                      {currentImages.map((url, index) => (
                        <div key={index} className="relative group">
                          <Image
                            src={url}
                            alt={`Current ${index + 1}`}
                            className="h-32 w-32 object-cover rounded-lg border-2 border-gray-300 group-hover:border-[#956358] transition-colors"
                            width={128}
                            height={128}
                          />
                          <button
                            type="button"
                            onClick={() => removeCurrentImage(index)}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-2 hover:bg-red-600 shadow-lg transition-colors"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Upload New Images */}
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 bg-gray-50 hover:bg-gray-100 transition-colors">
                  <div className="text-center">
                    <Upload className="mx-auto h-16 w-16 text-gray-400" />
                    <div className="mt-6">
                      <label htmlFor="image-upload" className="cursor-pointer">
                        <span className="mt-2 block text-lg font-semibold text-gray-900">
                          Upload additional images
                        </span>
                        <span className="mt-2 block text-sm text-gray-600">
                          PNG, JPG, GIF up to 10MB each • Images will be uploaded when you save
                        </span>
                      </label>
                      <input
                        id="image-upload"
                        type="file"
                        multiple
                        accept="image/*"
                        onChange={handleImageUpload}
                        className="hidden"
                      />
                    </div>
                  </div>
                </div>

                {/* New Image Previews */}
                {newImageUrls.length > 0 && (
                  <div className="mt-6">
                    <h4 className="text-sm font-medium text-gray-700 mb-3">New Images (will be uploaded when you save)</h4>
                    <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
                      {newImageUrls.map((url, index) => (
                        <div key={index} className="relative group">
                          <Image
                            src={url}
                            alt={`New ${index + 1}`}
                            className="h-32 w-32 object-cover rounded-lg border-2 border-blue-300 group-hover:border-blue-500 transition-colors"
                            width={128}
                            height={128}
                          />
                          <button
                            type="button"
                            onClick={() => removeNewImage(index)}
                            className="absolute -top-2 -right-2 bg-blue-500 text-white rounded-full p-2 hover:bg-blue-600 shadow-lg transition-colors"
                          >
                            <X className="h-4 w-4" />
                          </button>
                          <div className="absolute bottom-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                            New
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white shadow-lg rounded-lg border border-gray-200 p-6">
          <div className="flex justify-between items-center">
            <div>
              <h4 className="text-lg font-semibold text-gray-900">Ready to update product?</h4>
              <p className="text-sm text-gray-600 mt-1">Review your changes before updating the product</p>
            </div>
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  router.push('/admin/products');
                }}
                className="px-6 py-3 border-2 border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isLoading || isUploadingImages}
                className="px-8 py-3 bg-[#956358] text-white font-semibold rounded-lg hover:bg-[#7d5249] disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#956358] flex items-center transition-colors shadow-md"
              >
                {isUploadingImages ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                    Uploading Images...
                  </>
                ) : isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                    Updating Product...
                  </>
                ) : (
                  <>
                    <Save className="h-5 w-5 mr-3" />
                    Update Product
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </form>
        </div>
      </div>
    </div>
  );
}