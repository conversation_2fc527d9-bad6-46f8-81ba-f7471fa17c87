import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

interface AdminUser {
  id: string;
  email: string;
  fullName: string;
  isAdmin: boolean;
}

interface AdminAuthState {
  admin: AdminUser | null;
  isLoading: boolean;
  isAuthenticated: boolean;

  // Actions
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  verifyAuth: () => Promise<void>;
  initialize: () => Promise<void>;
  checkPermission: (resource: string, action: string) => boolean;
}

export const useAdminAuth = create<AdminAuthState>()(
  persist(
    (set, get) => ({
      admin: null,
      isLoading: true, // Start with loading true
      isAuthenticated: false,

      login: async (email: string, password: string) => {
        set({ isLoading: true });

        try {
          // Use admin login API
          const response = await fetch('/api/admin/auth/login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({ email, password }),
          });

          const data = await response.json();

          if (response.ok && data.success) {
            set({
              admin: data.admin,
              isAuthenticated: true,
              isLoading: false
            });
            return { success: true };
          } else {
            set({ isLoading: false });
            return { success: false, error: data.error || 'Login failed' };
          }
        } catch (error) {
          set({ isLoading: false });
          return { success: false, error: 'Network error' };
        }
      },

      logout: async () => {
        set({ isLoading: true });

        try {
          // Use admin logout API
          await fetch('/api/admin/auth/logout', {
            method: 'POST',
            credentials: 'include',
          });
        } catch (error) {
          console.error('Logout error:', error);
        } finally {
          set({
            admin: null,
            isAuthenticated: false,
            isLoading: false
          });
        }
      },

      verifyAuth: async () => {
        set({ isLoading: true });

        try {
          const response = await fetch('/api/admin/auth/verify', {
            credentials: 'include', // Ensure cookies are sent
            cache: 'no-cache' // Prevent caching issues
          });

          if (response.ok) {
            const data = await response.json();
            set({
              admin: data.admin,
              isAuthenticated: true,
              isLoading: false
            });
          } else {
            // Clear stored auth state if verification fails
            set({
              admin: null,
              isAuthenticated: false,
              isLoading: false
            });
          }
        } catch (error) {
          console.error('Auth verification error:', error);
          // Clear stored auth state on error
          set({
            admin: null,
            isAuthenticated: false,
            isLoading: false
          });
        }
      },

      initialize: async () => {
        // Always verify auth on initialization to ensure session is still valid
        // This handles cases where server restarts or tokens expire
        await get().verifyAuth();
      },

      checkPermission: (_resource: string, _action: string) => {
        const { admin } = get();
        // For simplified admin system, all admin users have full permissions
        return admin?.isAdmin || false;
      },
    }),    {
      name: 'admin-auth-storage',
      storage: createJSONStorage(() => localStorage),
      partialize: (state) => ({ 
        admin: state.admin, 
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
);
