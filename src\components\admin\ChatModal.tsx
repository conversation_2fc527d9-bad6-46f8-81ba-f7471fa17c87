'use client';

import { useEffect } from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ChatWindow } from '@/components/chat/ChatWindow';
import { useAdminAuth } from '@/hooks/useAdminAuth';

interface ChatModalProps {
  isOpen: boolean;
  onClose: () => void;
  inquiryId: string;
  customerName: string;
}

export function ChatModal({ isOpen, onClose, inquiryId, customerName }: ChatModalProps) {
  const { admin } = useAdminAuth();

  // Handle escape key press
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Modal */}
      <div className="flex min-h-full items-center justify-center p-4">
        <div className="relative bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Chat with Customer</h2>
              <p className="text-sm text-gray-600 mt-1">
                Chatting with {customerName} about their inquiry
              </p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
              className="p-2"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Chat Content */}
          <div className="h-[600px] overflow-hidden">
            <ChatWindow
              inquiryId={inquiryId}
              currentUserId={admin?.id || "admin-user"}
              title=""
              subtitle=""
              isAdmin={true}
              customerName={customerName}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
