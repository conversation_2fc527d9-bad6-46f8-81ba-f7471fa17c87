#!/usr/bin/env node

/**
 * Remove Product Images Script
 * 
 * This script removes all image links from the products table in Supabase.
 * It provides options to backup data and optionally delete storage files.
 * 
 * Usage:
 *   node scripts/remove-images.js [options]
 * 
 * Options:
 *   --backup-only    Only create backup, don't remove images
 *   --no-backup      Skip creating backup (not recommended)
 *   --delete-storage Delete actual image files from storage
 *   --dry-run        Show what would be done without making changes
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Configuration
const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
  console.error('❌ Error: Missing Supabase configuration');
  console.error('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY environment variables');
  process.exit(1);
}

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  backupOnly: args.includes('--backup-only'),
  noBackup: args.includes('--no-backup'),
  deleteStorage: args.includes('--delete-storage'),
  dryRun: args.includes('--dry-run')
};

async function main() {
  console.log('🚀 Product Images Removal Script');
  console.log('================================');
  
  if (options.dryRun) {
    console.log('🔍 DRY RUN MODE - No changes will be made');
  }
  
  try {
    // Step 1: Fetch all products with images
    console.log('\n📊 Fetching products...');
    const { data: products, error: fetchError } = await supabase
      .from('products')
      .select('id, name, images');

    if (fetchError) {
      throw new Error(`Failed to fetch products: ${fetchError.message}`);
    }

    const productsWithImages = products.filter(p => 
      p.images && Array.isArray(p.images) && p.images.length > 0
    );

    console.log(`📈 Found ${products.length} total products`);
    console.log(`🖼️  Found ${productsWithImages.length} products with images`);
    
    const totalImages = productsWithImages.reduce((sum, p) => sum + p.images.length, 0);
    console.log(`🔗 Total image links: ${totalImages}`);

    if (productsWithImages.length === 0) {
      console.log('✅ No products with images found. Nothing to do.');
      return;
    }

    // Step 2: Create backup
    if (!options.noBackup) {
      console.log('\n💾 Creating backup...');
      
      const backupData = {
        timestamp: new Date().toISOString(),
        totalProducts: products.length,
        productsWithImages: productsWithImages.length,
        totalImageLinks: totalImages,
        products: productsWithImages.map(p => ({
          id: p.id,
          name: p.name,
          images: p.images
        }))
      };

      const backupFileName = `product-images-backup-${new Date().toISOString().split('T')[0]}.json`;
      const backupPath = path.join(process.cwd(), 'backups', backupFileName);
      
      // Create backups directory if it doesn't exist
      const backupsDir = path.dirname(backupPath);
      if (!fs.existsSync(backupsDir)) {
        fs.mkdirSync(backupsDir, { recursive: true });
      }

      if (!options.dryRun) {
        fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2));
        console.log(`✅ Backup created: ${backupPath}`);
      } else {
        console.log(`🔍 Would create backup: ${backupPath}`);
      }
    }

    if (options.backupOnly) {
      console.log('\n✅ Backup-only mode completed.');
      return;
    }

    // Step 3: Remove images from database
    console.log('\n🗑️  Removing image links from database...');
    
    if (!options.dryRun) {
      const { error: updateError } = await supabase
        .from('products')
        .update({ 
          images: [],
          updated_at: new Date().toISOString()
        })
        .neq('id', '00000000-0000-0000-0000-000000000000'); // This matches all products

      if (updateError) {
        throw new Error(`Failed to update products: ${updateError.message}`);
      }
      
      console.log('✅ Database updated successfully');
    } else {
      console.log(`🔍 Would remove images from ${productsWithImages.length} products`);
    }

    // Step 4: Delete from storage (optional)
    if (options.deleteStorage) {
      console.log('\n🔥 Deleting images from storage...');
      
      const allImageUrls = [];
      productsWithImages.forEach(product => {
        allImageUrls.push(...product.images);
      });

      // Extract file paths from URLs
      const imagePaths = allImageUrls.map(imageUrl => {
        const urlParts = imageUrl.split('/storage/v1/object/public/product-images/');
        return urlParts.length > 1 ? urlParts[1] : null;
      }).filter(Boolean);

      console.log(`🔗 Found ${imagePaths.length} image files to delete`);

      if (imagePaths.length > 0 && !options.dryRun) {
        const { error: storageError } = await supabase.storage
          .from('product-images')
          .remove(imagePaths);

        if (storageError) {
          console.warn(`⚠️  Storage deletion warning: ${storageError.message}`);
        } else {
          console.log('✅ Storage files deleted successfully');
        }
      } else if (options.dryRun) {
        console.log(`🔍 Would delete ${imagePaths.length} files from storage`);
      }
    }

    // Step 5: Verify results
    if (!options.dryRun) {
      console.log('\n🔍 Verifying results...');
      const { data: verifyProducts, error: verifyError } = await supabase
        .from('products')
        .select('id, images');

      if (verifyError) {
        console.warn(`⚠️  Verification warning: ${verifyError.message}`);
      } else {
        const remainingImages = verifyProducts.filter(p => 
          p.images && Array.isArray(p.images) && p.images.length > 0
        ).length;
        
        console.log(`✅ Verification complete: ${remainingImages} products still have images`);
      }
    }

    console.log('\n🎉 Script completed successfully!');
    
    if (!options.noBackup && !options.dryRun) {
      console.log('💡 Tip: Keep your backup file safe in case you need to restore images');
    }

  } catch (error) {
    console.error('\n❌ Error:', error.message);
    process.exit(1);
  }
}

// Show help if requested
if (args.includes('--help') || args.includes('-h')) {
  console.log(`
Product Images Removal Script

Usage:
  node scripts/remove-images.js [options]

Options:
  --backup-only     Only create backup, don't remove images
  --no-backup       Skip creating backup (not recommended)
  --delete-storage  Delete actual image files from storage
  --dry-run         Show what would be done without making changes
  --help, -h        Show this help message

Examples:
  node scripts/remove-images.js                    # Remove images with backup
  node scripts/remove-images.js --dry-run          # See what would happen
  node scripts/remove-images.js --backup-only      # Only create backup
  node scripts/remove-images.js --delete-storage   # Remove images and delete files
`);
  process.exit(0);
}

main();
