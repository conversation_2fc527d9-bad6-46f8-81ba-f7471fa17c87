'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Save, ArrowLeft, Package, Upload, X } from 'lucide-react';
import { SpecificationManager } from '@/components/admin/SpecificationManager';
import { SpecialFeaturesManager } from '@/components/admin/SpecialFeaturesManager';
import { AdditionalFeaturesManager } from '@/components/admin/AdditionalFeaturesManager';

interface Category {
  id: string;
  name: string;
  slug: string;
}

interface Brand {
  id: string;
  name: string;
  slug: string;
}

interface Condition {
  id: string;
  name: string;
  slug: string;
}

export default function NewProductPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [categories, setCategories] = useState<Category[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [conditions, setConditions] = useState<Condition[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    brand_id: '',
    condition_id: '',
    model: '',
    product_id: '',
    description: '',
    price: '',
    show_price: true,
    category_id: '',
    stock_quantity: '',
    low_stock_threshold: '',
    status: 'active'
  });
  const [specifications, setSpecifications] = useState<Record<string, string>>({});
  const [specialFeatures, setSpecialFeatures] = useState<string[]>([]);
  const [additionalFeatures, setAdditionalFeatures] = useState<Record<string, any>>({});
  const [images, setImages] = useState<File[]>([]);
  const [imageUrls, setImageUrls] = useState<string[]>([]);
  const [isUploadingImages, setIsUploadingImages] = useState(false);

  // Generate 6-digit unique product ID
  const generateProductId = () => {
    return Math.floor(100000 + Math.random() * 900000).toString();
  };

  // Generate product ID on component mount
  useEffect(() => {
    if (!formData.product_id) {
      setFormData(prev => ({
        ...prev,
        product_id: generateProductId()
      }));
    }
  }, [formData.product_id]);

  // Memoize onChange functions to prevent infinite re-renders
  const handleSpecificationsChange = useCallback((data: Record<string, string>) => {
    setSpecifications(data);
  }, []);

  const handleSpecialFeaturesChange = useCallback((data: string[]) => {
    setSpecialFeatures(data);
  }, []);

  const handleAdditionalFeaturesChange = useCallback((data: Record<string, any>) => {
    setAdditionalFeatures(data);
  }, []);

  useEffect(() => {
    fetchCategories();
    fetchBrands();
    fetchConditions();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const fetchBrands = async () => {
    try {
      const response = await fetch('/api/admin/brands?status=active&limit=100');
      if (response.ok) {
        const data = await response.json();
        setBrands(data.brands || []);
      }
    } catch (error) {
      console.error('Error fetching brands:', error);
    }
  };

  const fetchConditions = async () => {
    try {
      const response = await fetch('/api/admin/conditions?status=active&limit=100');
      if (response.ok) {
        const data = await response.json();
        setConditions(data.conditions || []);
      }
    } catch (error) {
      console.error('Error fetching conditions:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    setImages(prev => [...prev, ...files]);

    // Create preview URLs
    files.forEach(file => {
      const url = URL.createObjectURL(file);
      setImageUrls(prev => [...prev, url]);
    });
  };

  const removeImage = (index: number) => {
    setImages(prev => prev.filter((_, i) => i !== index));
    setImageUrls(prev => {
      // Revoke the URL to free memory
      URL.revokeObjectURL(prev[index]);
      return prev.filter((_, i) => i !== index);
    });
  };

  const uploadImagesToSupabase = async (files: File[]): Promise<string[]> => {
    const uploadedUrls: string[] = [];

    for (const file of files) {
      const formData = new FormData();
      formData.append('file', file);

      try {
        const response = await fetch('/api/admin/upload-image', {
          method: 'POST',
          body: formData,
        });

        if (response.ok) {
          const data = await response.json();
          uploadedUrls.push(data.url);
        } else {
          console.error('Failed to upload image:', file.name);
        }
      } catch (error) {
        console.error('Error uploading image:', error);
      }
    }

    return uploadedUrls;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Upload images first if any
      let uploadedImageUrls: string[] = [];
      if (images.length > 0) {
        setIsUploadingImages(true);
        uploadedImageUrls = await uploadImagesToSupabase(images);
        setIsUploadingImages(false);
      }

      const productData = {
        ...formData,
        price: parseFloat(formData.price),
        stock_quantity: parseInt(formData.stock_quantity) || 0,
        low_stock_threshold: parseInt(formData.low_stock_threshold) || 5,
        specifications,
        special_features: specialFeatures,
        additional_features: additionalFeatures,
        images: uploadedImageUrls
      };

      const response = await fetch('/api/admin/products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(productData),
      });

      if (response.ok) {
        alert('Product created successfully!');
        router.push('/admin/products');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error('Error creating product:', error);
      alert('Failed to create product. Please check the form data.');
    } finally {
      setIsLoading(false);
      setIsUploadingImages(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => router.back()}
                className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#956358]"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                  <Package className="h-8 w-8 mr-3 text-[#956358]" />
                  Add New Product
                </h1>
                <p className="mt-1 text-lg text-gray-600">Create a new product listing for your inventory</p>
              </div>
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-8">
            <div className="bg-white shadow-lg rounded-lg border border-gray-200">
              <div className="px-6 py-8 sm:p-8">
                <div className="border-b border-gray-200 pb-6 mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">Basic Information</h3>
                  <p className="text-sm text-gray-600">Enter the core details for this product</p>
                </div>
            
                <div className="grid grid-cols-1 gap-8 sm:grid-cols-2">
                  <div>
                    <label className="block text-sm font-semibold text-gray-800 mb-2">
                      Product Name <span className="text-red-600">*</span>
                    </label>
                    <input
                      type="text"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleInputChange}
                      className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                      placeholder="Enter product name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-800 mb-2">
                      Brand <span className="text-red-600">*</span>
                    </label>
                    <select
                      name="brand_id"
                      required
                      value={formData.brand_id}
                      onChange={handleInputChange}
                      className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 bg-white focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                    >
                      <option value="" className="text-gray-500">Select a brand</option>
                      {brands.map((brand) => (
                        <option key={brand.id} value={brand.id} className="text-gray-900">
                          {brand.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-800 mb-2">
                      Model <span className="text-red-600">*</span>
                    </label>
                    <input
                      type="text"
                      name="model"
                      required
                      value={formData.model}
                      onChange={handleInputChange}
                      className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                      placeholder="Enter product model"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-800 mb-2">
                      Product ID <span className="text-red-600">*</span>
                    </label>
                    <div className="flex space-x-2">
                      <input
                        type="text"
                        name="product_id"
                        required
                        value={formData.product_id}
                        onChange={handleInputChange}
                        className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                        placeholder="6-digit product ID"
                        maxLength={6}
                        pattern="[0-9]{6}"
                      />
                      <button
                        type="button"
                        onClick={() => setFormData(prev => ({ ...prev, product_id: generateProductId() }))}
                        className="px-4 py-2 bg-gray-100 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-[#956358] transition-colors"
                        title="Generate new ID"
                      >
                        🔄
                      </button>
                    </div>
                    <p className="mt-1 text-sm text-gray-500">
                      Unique 6-digit identifier for this product
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-800 mb-2">
                      Category <span className="text-red-600">*</span>
                    </label>
                    <select
                      name="category_id"
                      required
                      value={formData.category_id}
                      onChange={handleInputChange}
                      className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 bg-white focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                    >
                      <option value="" className="text-gray-500">Select a category</option>
                      {categories.map((category) => (
                        <option key={category.id} value={category.id} className="text-gray-900">
                          {category.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-800 mb-2">
                      Condition <span className="text-red-600">*</span>
                    </label>
                    <select
                      name="condition_id"
                      required
                      value={formData.condition_id}
                      onChange={handleInputChange}
                      className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 bg-white focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                    >
                      <option value="" className="text-gray-500">Select a condition</option>
                      {conditions.map((condition) => (
                        <option key={condition.id} value={condition.id} className="text-gray-900">
                          {condition.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-800 mb-2">
                      Price <span className="text-red-600">*</span>
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                        <span className="text-gray-600 text-lg font-medium">₹</span>
                      </div>
                      <input
                        type="number"
                        name="price"
                        required
                        step="0.01"
                        min="0"
                        value={formData.price}
                        onChange={handleInputChange}
                        className="block w-full pl-8 pr-4 py-3 border-2 border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                        placeholder="0.00"
                      />
                    </div>
                    <div className="mt-3 flex items-center">
                      <input
                        type="checkbox"
                        name="show_price"
                        id="show_price"
                        checked={formData.show_price}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-[#956358] focus:ring-[#956358] border-gray-300 rounded"
                      />
                      <label htmlFor="show_price" className="ml-2 block text-sm text-gray-700">
                        Show price to customers
                      </label>
                    </div>
                    <p className="mt-1 text-sm text-gray-500">
                      Uncheck to hide price and show "Contact for Price" instead
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-800 mb-2">
                      Stock Quantity <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="number"
                      name="stock_quantity"
                      required
                      min="0"
                      value={formData.stock_quantity}
                      onChange={handleInputChange}
                      className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                      placeholder="0"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-800 mb-2">
                      Low Stock Threshold
                    </label>
                    <input
                      type="number"
                      name="low_stock_threshold"
                      min="0"
                      value={formData.low_stock_threshold}
                      onChange={handleInputChange}
                      className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                      placeholder="5"
                    />
                    <p className="mt-1 text-sm text-gray-500">
                      Alert when stock falls below this number
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-800 mb-2">
                      Product Status <span className="text-red-500">*</span>
                    </label>
                    <select
                      name="status"
                      required
                      value={formData.status}
                      onChange={handleInputChange}
                      className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors"
                    >
                      <option value="active">Active</option>
                      <option value="deactivated">Deactivated</option>
                      <option value="archived">Archived</option>
                      <option value="private">Private</option>
                    </select>
                    <p className="mt-1 text-sm text-gray-500">
                      Active: Visible to customers | Deactivated: Hidden but manageable | Archived: Read-only | Private: Internal use only
                    </p>
                  </div>


                </div>

                <div className="mt-8 space-y-8">
                  <div>
                    <label className="block text-sm font-semibold text-gray-800 mb-2">Description</label>
                    <textarea
                      name="description"
                      rows={5}
                      value={formData.description}
                      onChange={handleInputChange}
                      className="block w-full border-2 border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-[#956358] focus:border-[#956358] transition-colors resize-none"
                      placeholder="Enter a detailed product description..."
                    />
                  </div>

                  <SpecialFeaturesManager
                    title="Special Features"
                    description="Add key features that make this product stand out"
                    initialData={[]}
                    onChange={handleSpecialFeaturesChange}
                    placeholder="Business-grade reliability"
                  />

                  <SpecificationManager
                    title="Product Specifications"
                    description="Add technical specifications and product details"
                    initialData={{}}
                    onChange={handleSpecificationsChange}
                    placeholder={{ key: 'Processor', value: 'Intel i7' }}
                  />

                  <AdditionalFeaturesManager
                    title="Additional Features"
                    description="Add extra features with flexible value types"
                    initialData={{}}
                    onChange={handleAdditionalFeaturesChange}
                    placeholder={{ key: 'Webcam', value: 'HD 720p' }}
                  />



                  <div>
                    <label className="block text-sm font-semibold text-gray-800 mb-2">Product Images</label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 bg-gray-50 hover:bg-gray-100 transition-colors">
                      <div className="text-center">
                        <Upload className="mx-auto h-16 w-16 text-gray-400" />
                        <div className="mt-6">
                          <label htmlFor="image-upload" className="cursor-pointer">
                            <span className="mt-2 block text-lg font-semibold text-gray-900">
                              Upload product images
                            </span>
                            <span className="mt-2 block text-sm text-gray-600">
                              PNG, JPG, GIF up to 10MB each • Drag and drop or click to browse
                            </span>
                          </label>
                          <input
                            id="image-upload"
                            type="file"
                            multiple
                            accept="image/*"
                            onChange={handleImageUpload}
                            className="hidden"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Image Previews */}
                    {imageUrls.length > 0 && (
                      <div className="mt-6 grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-4">
                        {imageUrls.map((url, index) => (
                          <div key={index} className="relative group">
                            <Image
                              src={url}
                              alt={`Preview ${index + 1}`}
                              className="h-32 w-32 object-cover rounded-lg border-2 border-gray-300 group-hover:border-[#956358] transition-colors"
                              width={128}
                              height={128}
                            />
                            <button
                              type="button"
                              onClick={() => removeImage(index)}
                              className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-2 hover:bg-red-600 shadow-lg transition-colors"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white shadow-lg rounded-lg border border-gray-200 p-6">
              <div className="flex justify-between items-center">
                <div>
                  <h4 className="text-lg font-semibold text-gray-900">Ready to create product?</h4>
                  <p className="text-sm text-gray-600 mt-1">Review your information before creating the product</p>
                </div>
                <div className="flex space-x-4">
                  <button
                    type="button"
                    onClick={() => router.back()}
                    className="px-6 py-3 border-2 border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={isLoading || isUploadingImages}
                    className="px-8 py-3 bg-[#956358] text-white font-semibold rounded-lg hover:bg-[#7d5249] disabled:opacity-50 disabled:cursor-not-allowed focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#956358] flex items-center transition-colors shadow-md"
                  >
                    {isUploadingImages ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                        Uploading Images...
                      </>
                    ) : isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3"></div>
                        Creating Product...
                      </>
                    ) : (
                      <>
                        <Save className="h-5 w-5 mr-3" />
                        Create Product
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
