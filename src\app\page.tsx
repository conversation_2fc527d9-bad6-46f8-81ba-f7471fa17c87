'use client';

import { useState, useEffect } from 'react';
import EnhancedHeroSection from '@/components/layout/EnhancedHeroSection';
import EnhancedProductCarousel from '@/components/home/<USER>';
import EnhancedCategoryShowcase from '@/components/home/<USER>';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Card, CardContent } from '@/components/ui/card';



interface Product {
  id: string;
  name: string;
  price: number;
  images: string[];
  brand: string;
  model: string;
  show_price?: boolean;
  slug: string;
}

interface CategoryData {
  id: string;
  name: string;
  slug: string;
  image: string;
  products: Product[];
}

export default function HomePage() {
  const [categories, setCategories] = useState<CategoryData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchHomePageData();
  }, []);

  const fetchHomePageData = async () => {
    try {
      // Fetch categories with products
      const categoriesResponse = await fetch('/api/home/<USER>');
      if (categoriesResponse.ok) {
        const categoriesData = await categoriesResponse.json();
        setCategories(categoriesData.categories || []);
      }
    } catch (error) {
      console.error('Error fetching home page data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--background-primary)] via-[var(--background-secondary)] to-[var(--background-card)]">
      <EnhancedHeroSection />

      {/* Product Carousel Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-r from-[var(--accent-primary)]/5 to-[var(--accent-secondary)]/5 rounded-3xl mx-4"></div>

        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16 animate-fade-in">
            <Badge className="bg-gradient-primary text-white border-0 mb-6 px-4 py-2">
              ⭐ Handpicked Selection
            </Badge>
            <h2 className="heading-primary text-4xl md:text-6xl mb-6">
              Featured <span className="text-gradient">Products</span>
            </h2>
            <p className="text-[var(--soft-white)] text-xl max-w-3xl mx-auto leading-relaxed">
              Discover our carefully curated collection of premium refurbished devices,
              each tested and certified for optimal performance
            </p>
          </div>

          <EnhancedProductCarousel />
        </div>
      </section>

      <Separator className="bg-shadow-grey/30 max-w-6xl mx-auto" />

      {/* Category Showcase Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto relative z-10">
          <div className="text-center mb-16 animate-fade-in">
            <Badge className="bg-gradient-primary text-white border-0 mb-6 px-4 py-2">
              🏷️ Browse by Category
            </Badge>
            <h2 className="heading-primary text-4xl md:text-6xl mb-6">
              Product <span className="text-gradient">Categories</span>
            </h2>
            <p className="text-[var(--soft-white)] text-xl max-w-3xl mx-auto leading-relaxed">
              Explore our comprehensive range of refurbished technology solutions,
              from powerful workstations to portable devices
            </p>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {Array.from({ length: 6 }).map((_, index) => (
                <Card key={index} className="bg-gradient-card border-shadow-grey/20">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <Skeleton className="w-12 h-12 rounded-xl" />
                      <div className="space-y-2">
                        <Skeleton className="h-5 w-24" />
                        <Skeleton className="h-4 w-16" />
                      </div>
                    </div>
                    <Skeleton className="h-32 w-full rounded-lg mb-4" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-4 w-1/2" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : categories.length > 0 ? (
            <EnhancedCategoryShowcase categories={categories} />
          ) : (
            <div className="text-center animate-fade-in">
              <Card className="max-w-md mx-auto bg-gradient-card border-shadow-grey/20">
                <CardContent className="p-12">
                  <div className="w-20 h-20 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-6">
                    <span className="text-white text-3xl">🔍</span>
                  </div>
                  <h3 className="text-xl font-semibold text-soft-white mb-3">No Categories Available</h3>
                  <p className="text-[var(--muted-grey)] text-lg mb-2">We're working on adding amazing categories.</p>
                  <p className="text-[var(--soft-white)] text-sm">Check back soon for incredible deals!</p>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </section>
    </div>
  )
}
