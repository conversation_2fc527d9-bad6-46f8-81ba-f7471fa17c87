import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params;
    const supabase = createSupabaseAdminClient();
    const searchParams = request.nextUrl.searchParams;
    const includePrivate = searchParams.get('includePrivate') === 'true';

    // Check if the parameter is a UUID (ID) or a slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);

    // Fetch product by ID or slug with relationships
    let query = supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          slug
        ),
        brands (
          id,
          name,
          slug
        )
      `)
      .eq('is_active', true);

    // Use appropriate field based on parameter type
    if (isUUID) {
      query = query.eq('id', id);
    } else {
      query = query.eq('slug', id);
    }

    const { data: product, error } = await query.single();

    if (error || !product) {
      console.error('Error fetching product:', error);
      console.error('Search parameter:', id, 'isUUID:', isUUID);
      console.error('Include private:', includePrivate);

      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }



    // Additional check for private products in application logic
    if (product.status === 'private' && !includePrivate) {
      return NextResponse.json(
        { error: 'Product access restricted. Please request access to view private products.' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      product
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
