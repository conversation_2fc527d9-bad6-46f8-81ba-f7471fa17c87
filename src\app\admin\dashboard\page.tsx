'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAdminAuth } from '@/hooks/useAdminAuth';
import {
  Package,
  MessageSquare,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';

interface DashboardStats {
  products: {
    total: number;
    active: number;
    lowStock: number;
    outOfStock: number;
  };
  users: {
    total: number;
    newThisMonth: number;
    newLastMonth: number;
  };
  inquiries: {
    total: number;
    pending: number;
    inProgress: number;
    completed: number;
    newThisMonth: number;
  };
  growth: {
    users: number;
  };
}

export default function AdminDashboard() {
  const router = useRouter();
  const { admin } = useAdminAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const response = await fetch('/api/admin/dashboard/stats');
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
      } else {
        console.error('Failed to fetch dashboard stats');
      }
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-[var(--accent-primary)] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  const statCards = [
    {
      name: 'Total Products',
      value: stats?.products.total || 0,
      icon: Package,
      color: 'bg-blue-500',
      change: `${stats?.products.active || 0} active`,
      changeType: 'info',
    },
    {
      name: 'Total Inquiries',
      value: stats?.inquiries.total || 0,
      icon: MessageSquare,
      color: 'bg-green-500',
      change: `${stats?.inquiries.newThisMonth || 0} this month`,
      changeType: 'info',
    },
    {
      name: 'Total Users',
      value: stats?.users.total || 0,
      icon: Users,
      color: 'bg-purple-500',
      change: `${(stats?.growth?.users || 0) > 0 ? '+' : ''}${stats?.growth?.users || 0}%`,
      changeType: (stats?.growth?.users || 0) >= 0 ? 'increase' : 'decrease',
    },
    {
      name: 'Pending Inquiries',
      value: stats?.inquiries.pending || 0,
      icon: Clock,
      color: 'bg-yellow-500',
      change: `${stats?.inquiries.inProgress || 0} in progress`,
      changeType: 'info',
    },
  ];

  const inquiryStatusCards = [
    {
      name: 'Pending',
      value: stats?.inquiries.pending || 0,
      icon: Clock,
      color: 'bg-yellow-500',
    },
    {
      name: 'In Progress',
      value: stats?.inquiries.inProgress || 0,
      icon: TrendingUp,
      color: 'bg-blue-500',
    },
    {
      name: 'Completed',
      value: stats?.inquiries.completed || 0,
      icon: CheckCircle,
      color: 'bg-green-500',
    },
    {
      name: 'Low Stock',
      value: stats?.products.lowStock || 0,
      icon: AlertCircle,
      color: 'bg-red-500',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-[var(--shadow-grey)] pb-4">
        <h1 className="text-3xl font-bold text-[var(--text-primary)]">Dashboard</h1>
        <p className="text-[var(--text-secondary)] mt-1">
          Welcome back, {admin?.fullName}. Here&apos;s what&apos;s happening with your store.
        </p>
      </div>

      {/* Main Stats */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => (
          <div
            key={stat.name}
            className="relative bg-[var(--background-card)] pt-5 px-4 pb-12 sm:pt-6 sm:px-6 shadow-lg rounded-lg overflow-hidden border border-[var(--shadow-grey)]"
          >
            <dt>
              <div className={`absolute ${stat.color} rounded-md p-3`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
              <p className="ml-16 text-sm font-medium text-[var(--text-secondary)] truncate">
                {stat.name}
              </p>
            </dt>
            <dd className="ml-16 pb-6 flex items-baseline sm:pb-7">
              <p className="text-2xl font-semibold text-[var(--text-primary)]">{stat.value}</p>
              <p
                className={`ml-2 flex items-baseline text-sm font-semibold ${
                  stat.changeType === 'increase'
                    ? 'text-[var(--success)]'
                    : stat.changeType === 'decrease'
                    ? 'text-[var(--error)]'
                    : 'text-[var(--text-secondary)]'
                }`}
              >
                {stat.change}
              </p>
            </dd>
          </div>
        ))}
      </div>

      {/* Inquiry Status Breakdown */}
      <div className="bg-[var(--background-card)] shadow-lg rounded-lg border border-[var(--shadow-grey)]">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-[var(--text-primary)] mb-4">
            Inquiry Status Overview
          </h3>
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            {inquiryStatusCards.map((status) => (
              <div key={status.name} className="flex items-center">
                <div className={`${status.color} rounded-md p-2 mr-3`}>
                  <status.icon className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-[var(--text-secondary)]">{status.name}</p>
                  <p className="text-2xl font-semibold text-[var(--text-primary)]">{status.value}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-[var(--background-card)] shadow-lg rounded-lg border border-[var(--shadow-grey)]">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-[var(--text-primary)] mb-4">
            Recent Activity
          </h3>
          <div className="space-y-3">
            <div className="flex items-center text-sm text-[var(--text-secondary)]">
              <div className="bg-green-100 rounded-full p-1 mr-3">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <span>New inquiry received for Dell Laptop XPS 13</span>
              <span className="ml-auto text-[var(--text-secondary)]">2 minutes ago</span>
            </div>
            <div className="flex items-center text-sm text-[var(--text-secondary)]">
              <div className="bg-blue-100 rounded-full p-1 mr-3">
                <TrendingUp className="h-4 w-4 text-blue-600" />
              </div>
              <span>Inquiry status updated to &quot;In Progress&quot;</span>
              <span className="ml-auto text-[var(--text-secondary)]">5 minutes ago</span>
            </div>
            <div className="flex items-center text-sm text-[var(--text-secondary)]">
              <div className="bg-purple-100 rounded-full p-1 mr-3">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
              <span>New user registered: John Doe</span>
              <span className="ml-auto text-[var(--text-secondary)]">10 minutes ago</span>
            </div>
            <div className="flex items-center text-sm text-[var(--text-secondary)]">
              <div className="bg-yellow-100 rounded-full p-1 mr-3">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
              </div>
              <span>Chat message received from customer</span>
              <span className="ml-auto text-[var(--text-secondary)]">15 minutes ago</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-[var(--background-card)] shadow-lg rounded-lg border border-[var(--shadow-grey)]">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-[var(--text-primary)] mb-4">
            Quick Actions
          </h3>
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            <button
              onClick={() => router.push('/admin/products/new')}
              className="btn-primary"
            >
              <Package className="h-4 w-4 mr-2" />
              Add New Product
            </button>
            <button
              onClick={() => router.push('/admin/inquiries')}
              className="inline-flex items-center px-4 py-2 border border-[var(--shadow-grey)] text-sm font-medium rounded-md text-[var(--text-primary)] bg-[var(--background-secondary)] hover:bg-[var(--background-primary)] transition-colors"
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              View All Inquiries
            </button>
            <button
              onClick={() => router.push('/admin/users')}
              className="inline-flex items-center px-4 py-2 border border-[var(--shadow-grey)] text-sm font-medium rounded-md text-[var(--text-primary)] bg-[var(--background-secondary)] hover:bg-[var(--background-primary)] transition-colors"
            >
              <Users className="h-4 w-4 mr-2" />
              Manage Users
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
