'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  ShoppingCart, 
  Package, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  MessageSquare,
  Building2,
  Calculator,
  Clock,
  Star,
  CheckCircle,
  AlertCircle,
  Loader2,
  ArrowLeft
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useInquiries } from '@/hooks/useInquiries';

interface Product {
  id: string;
  name: string;
  brand: string;
  model?: string;
  price: number;
  images: string[];
  stock_quantity?: number;
  is_active?: boolean;
}

export default function InquiryPage() {
  const { user, loading: authLoading } = useAuth();
  const { createInquiry, loading: submitting } = useInquiries();
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [product, setProduct] = useState<Product | null>(null);
  const [inquiryType, setInquiryType] = useState<'regular' | 'bulk'>('regular');
  const [quantity, setQuantity] = useState(1);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    address: '',
    message: '',
    companyName: '',
    businessType: '',
    expectedVolume: ''
  });

  // Get product ID from URL params
  const productId = searchParams.get('product');
  const type = searchParams.get('type') as 'regular' | 'bulk' | null;

  useEffect(() => {
    if (type && (type === 'regular' || type === 'bulk')) {
      setInquiryType(type);
    }
  }, [type]);

  useEffect(() => {
    if (productId) {
      fetchProduct(productId);
    } else {
      setIsLoading(false);
    }
  }, [productId]);

  // Auto-fill form data for authenticated users
  useEffect(() => {
    if (user && !authLoading) {
      const fetchUserProfile = async () => {
        try {
          const response = await fetch('/api/user/profile');
          if (response.ok) {
            const data = await response.json();
            const profile = data.profile;
            setFormData(prev => ({
              ...prev,
              fullName: profile.full_name || '',
              email: profile.email || '',
              phone: profile.phone || '',
              address: profile.address || '',
              companyName: profile.company_name || '',
              businessType: profile.business_type || ''
            }));
          }
        } catch (error) {
          console.error('Error fetching user profile:', error);
        }
      };
      fetchUserProfile();
    }
  }, [user, authLoading]);

  const fetchProduct = async (id: string) => {
    try {
      const response = await fetch(`/api/products/${id}`);
      if (response.ok) {
        const data = await response.json();
        setProduct(data.product);
      }
    } catch (error) {
      console.error('Error fetching product:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setSubmitError(null);
  };

  const validateForm = () => {
    const errors: string[] = [];
    
    if (!formData.fullName.trim()) errors.push('Full name is required');
    if (!formData.email.trim()) errors.push('Email is required');
    if (!formData.phone.trim()) errors.push('Phone number is required');
    if (!formData.address.trim()) errors.push('Address is required');
    
    if (inquiryType === 'bulk') {
      if (!formData.companyName.trim()) errors.push('Company name is required for bulk orders');
      if (!formData.businessType.trim()) errors.push('Business type is required for bulk orders');
      if (!formData.expectedVolume.trim()) errors.push('Expected volume is required for bulk orders');
    }
    
    if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.push('Please enter a valid email address');
    }
    
    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError(null);

    const errors = validateForm();
    if (errors.length > 0) {
      setSubmitError(errors.join(', '));
      return;
    }

    try {
      await createInquiry({
        productId: product?.id || '',
        inquiryType,
        quantity,
        userDetails: {
          fullName: formData.fullName,
          email: formData.email,
          phone: formData.phone,
          address: formData.address
        }
      });

      setIsSubmitted(true);

      // Show alert for non-logged in users
      if (!user) {
        setTimeout(() => {
          alert('You can track your orders and queries after making an account. Create an account to manage your inquiries easily!');
        }, 2000);
      }

      // Redirect to orders page after 5 seconds
      setTimeout(() => {
        if (user) {
          router.push('/user/orders');
        } else {
          router.push('/auth/signup?redirect=/user/orders');
        }
      }, 5000);

    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : 'Failed to submit inquiry');
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[var(--background-primary)] via-[var(--background-secondary)] to-[var(--background-card)] flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-[#956358] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // Success state
  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[var(--background-primary)] via-[var(--background-secondary)] to-[var(--background-card)]">
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-2xl mx-auto">
            <Card className="p-8 text-center">
              <div className="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="h-10 w-10 text-green-400" />
              </div>
              <h1 className="text-3xl font-bold text-white mb-4">Inquiry Submitted Successfully!</h1>
              <p className="text-gray-300 mb-6">
                We will contact you soon regarding your {inquiryType} order inquiry.
              </p>
              {!user && (
                <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mb-6">
                  <p className="text-sm text-blue-300">
                    💡 Create an account to track your orders and queries easily!
                  </p>
                </div>
              )}
              <div className="space-y-4">
                <Button
                  onClick={() => router.push(user ? '/user/orders' : '/auth/signup')}
                  className="w-full"
                >
                  {user ? 'View My Orders' : 'Create Account to Track Orders'}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => router.push('/products')}
                  className="w-full"
                >
                  Continue Shopping
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--background-primary)] via-[var(--background-secondary)] to-[var(--background-card)]">
      {/* Header */}
      <div className="bg-[var(--deep-night-blue)]/50 border-b border-[var(--shadow-grey)]/50">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-white">
                {inquiryType === 'bulk' ? 'Bulk Order' : 'Product'} Inquiry
              </h1>
              <p className="text-gray-300">
                {inquiryType === 'bulk' 
                  ? 'Request a quote for bulk purchases' 
                  : 'Get in touch for product information'
                }
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Product Info (if available) */}
          {product && (
            <div className="lg:col-span-1">
              <Card className="p-6 sticky top-24">
                <h3 className="text-lg font-semibold text-white mb-4">Product Details</h3>
                <div className="space-y-4">
                  <div className="aspect-square relative rounded-lg overflow-hidden bg-gray-800">
                    <img
                      src={product.images?.[0] || '/api/placeholder-image'}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div>
                    <h4 className="font-semibold text-white">{product.name}</h4>
                    <p className="text-gray-400">{product.brand} {product.model_name}</p>
                    {product.show_price ? (
                      <p className="text-2xl font-bold text-[#956358] mt-2">
                        ₹{product.price.toLocaleString('en-IN')}
                      </p>
                    ) : (
                      <p className="text-lg font-bold text-[#956358] mt-2">
                        Contact for Price
                      </p>
                    )}
                  </div>
                </div>
              </Card>
            </div>
          )}

          {/* Inquiry Form */}
          <div className={product ? 'lg:col-span-2' : 'lg:col-span-3'}>
            <Card className="p-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Inquiry Type Selection */}
                <div>
                  <label className="block text-lg font-semibold text-white mb-4">
                    Select Inquiry Type
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <button
                      type="button"
                      onClick={() => setInquiryType('regular')}
                      className={`p-6 text-left rounded-lg border transition-all ${
                        inquiryType === 'regular'
                          ? 'bg-gradient-to-r from-[#956358] to-[#b87568] text-white border-transparent shadow-lg'
                          : 'bg-[#1a2b4a] text-gray-300 border-gray-600 hover:border-gray-500'
                      }`}
                    >
                      <ShoppingCart className="h-8 w-8 mb-3" />
                      <h3 className="text-lg font-semibold mb-2">Regular Order</h3>
                      <p className="text-sm opacity-90">
                        Standard product inquiry for individual purchases
                      </p>
                    </button>
                    <button
                      type="button"
                      onClick={() => setInquiryType('bulk')}
                      className={`p-6 text-left rounded-lg border transition-all ${
                        inquiryType === 'bulk'
                          ? 'bg-gradient-to-r from-[#956358] to-[#b87568] text-white border-transparent shadow-lg'
                          : 'bg-[#1a2b4a] text-gray-300 border-gray-600 hover:border-gray-500'
                      }`}
                    >
                      <Package className="h-8 w-8 mb-3" />
                      <h3 className="text-lg font-semibold mb-2">Bulk Order</h3>
                      <p className="text-sm opacity-90">
                        Large quantity orders with special pricing
                      </p>
                    </button>
                  </div>
                </div>

                {/* Personal Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Input
                    label="Full Name *"
                    type="text"
                    required
                    value={formData.fullName}
                    onChange={(e) => handleInputChange('fullName', e.target.value)}
                    placeholder="Enter your full name"
                    disabled={submitting}
                  />

                  <Input
                    label="Email Address *"
                    type="email"
                    required
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Enter your email"
                    disabled={submitting}
                  />

                  <Input
                    label="Phone Number *"
                    type="tel"
                    required
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="Enter your phone number"
                    disabled={submitting}
                  />


                </div>

                {/* Bulk Order Specific Fields */}
                {inquiryType === 'bulk' && (
                  <div className="bg-[#1a2b4a]/50 p-6 rounded-lg border border-gray-600">
                    <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                      <Building2 className="h-5 w-5 mr-2" />
                      Business Information
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <Input
                        label="Company Name *"
                        type="text"
                        required
                        value={formData.companyName}
                        onChange={(e) => handleInputChange('companyName', e.target.value)}
                        placeholder="Enter your company name"
                        disabled={submitting}
                      />

                      <Input
                        label="Business Type *"
                        type="text"
                        required
                        value={formData.businessType}
                        onChange={(e) => handleInputChange('businessType', e.target.value)}
                        placeholder="e.g., Retailer, Distributor, Corporate"
                        disabled={submitting}
                      />

                      <Input
                        label="Expected Volume *"
                        type="text"
                        required
                        value={formData.expectedVolume}
                        onChange={(e) => handleInputChange('expectedVolume', e.target.value)}
                        placeholder="e.g., 50-100 units per month"
                        disabled={submitting}
                      />
                    </div>
                  </div>
                )}

                {/* Address and Message */}
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Delivery Address *
                  </label>
                  <textarea
                    required
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    placeholder="Enter your complete address"
                    rows={3}
                    disabled={submitting}
                    className="w-full px-4 py-3 bg-[#1a2b4a] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent resize-vertical disabled:opacity-50"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Additional Message
                  </label>
                  <textarea
                    value={formData.message}
                    onChange={(e) => handleInputChange('message', e.target.value)}
                    placeholder={inquiryType === 'bulk' 
                      ? "Tell us about your requirements, expected delivery timeline, budget range, etc."
                      : "Any special requirements or questions..."
                    }
                    rows={4}
                    disabled={submitting}
                    className="w-full px-4 py-3 bg-[#1a2b4a] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent resize-vertical disabled:opacity-50"
                  />
                </div>

                {/* Error Display */}
                {submitError && (
                  <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-5 w-5 text-red-400" />
                      <p className="text-sm text-red-300">{submitError}</p>
                    </div>
                  </div>
                )}

                {/* Submit Button */}
                <Button
                  type="submit"
                  className="w-full bg-gradient-to-r from-[#956358] to-[#b87568] hover:from-[#8a5a4f] hover:to-[#a56c5c] text-white py-4 text-lg"
                  disabled={submitting || authLoading}
                >
                  {submitting ? (
                    <>
                      <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                      Submitting Inquiry...
                    </>
                  ) : (
                    <>
                      <MessageSquare className="h-5 w-5 mr-2" />
                      Submit {inquiryType === 'bulk' ? 'Bulk Order' : 'Product'} Inquiry
                    </>
                  )}
                </Button>
              </form>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
