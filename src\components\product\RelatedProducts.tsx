import Link from 'next/link';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

interface RelatedProduct {
  id: string;
  name: string;
  brand: string;
  model: string;
  price: number;
  originalPrice: number;
  condition: string;
  images: string[];
  category: string;
  inStock: boolean;
  ram: string;
  storage: string;
  slug: string;
}

interface RelatedProductsProps {
  products: RelatedProduct[];
  currentProductId: string;
}

export function RelatedProducts({ products, currentProductId }: RelatedProductsProps) {
  const relatedProducts = products.filter(product => product.id !== currentProductId).slice(0, 4);

  if (relatedProducts.length === 0) {
    return null;
  }

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold text-white">Related Products</h2>
      
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
        {relatedProducts.map((product) => (
          <Card key={product.id} className="p-4 hover:shadow-lg transition-shadow">
            {/* Product Image */}
            <div className="aspect-video bg-gray-700 rounded-lg mb-4 flex items-center justify-center">
              <span className="text-gray-400 text-xs">Product Image</span>
            </div>

            {/* Product Info */}
            <div className="space-y-3">
              <div>
                <h3 className="text-sm font-semibold text-white line-clamp-2">
                  {product.name}
                </h3>
                <p className="text-gray-400 text-xs">{product.brand} • {product.model}</p>
              </div>

              {/* Key Specs */}
              <div className="space-y-1 text-xs">
                <p className="text-gray-300">
                  <span className="text-gray-400">RAM:</span> {product.ram}
                </p>
                <p className="text-gray-300">
                  <span className="text-gray-400">Storage:</span> {product.storage}
                </p>
              </div>

              {/* Price */}
              <div className="flex items-center gap-2">
                <span className="text-lg font-bold text-white">
                  ₹{product.price.toLocaleString('en-IN')}
                </span>
                <span className="text-xs text-gray-400 line-through">
                  ₹{product.originalPrice.toLocaleString('en-IN')}
                </span>
              </div>

              {/* Condition Badge */}
              <Badge 
                variant={product.condition === 'Excellent' ? 'gradient' : 'outline'}
                className="text-xs"
              >
                {product.condition}
              </Badge>

              {/* Stock Status */}
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${product.inStock ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className="text-xs text-gray-300">
                  {product.inStock ? 'In Stock' : 'Out of Stock'}
                </span>
              </div>

              {/* Action Button */}
              <Link href={`/products/${product.slug}`}>
                <Button variant="outline" size="sm" className="w-full text-xs">
                  View Details
                </Button>
              </Link>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}
