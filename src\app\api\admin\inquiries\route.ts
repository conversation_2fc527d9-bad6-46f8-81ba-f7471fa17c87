import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';
import { verifyAdminAuth } from '@/lib/auth/admin';

export async function GET(request: NextRequest) {
  try {
    // Use the new unified admin authentication
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createSupabaseAdminClient();
    const searchParams = request.nextUrl.searchParams;
    
    // Parse query parameters
    const search = searchParams.get('search');
    const status = searchParams.get('status') || 'all';
    const type = searchParams.get('type') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build query for inquiries
    let query = supabase
      .from('inquiries')
      .select(`
        *,
        products (
          id,
          name,
          brand,
          model,
          price,
          images
        ),
        users (
          id,
          full_name,
          email,
          phone
        )
      `);

    // Apply filters
    if (search) {
      query = query.or(`contact_details->>'fullName'.ilike.%${search}%,contact_details->>'email'.ilike.%${search}%`);
    }

    if (status !== 'all') {
      query = query.eq('status', status as 'pending' | 'in_progress' | 'completed' | 'cancelled');
    }

    if (type !== 'all') {
      query = query.eq('type', type as 'regular' | 'bulk' | 'hot');
    }

    // Apply sorting with stable secondary sort
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Add secondary sort by ID to ensure stable pagination
    if (sortBy !== 'id') {
      query = query.order('id', { ascending: true });
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data: inquiries, error } = await query;

    if (error) {
      console.error('Error fetching inquiries:', error);
      return NextResponse.json(
        { error: 'Failed to fetch inquiries' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('inquiries')
      .select('*', { count: 'exact', head: true });

    if (search) {
      countQuery = countQuery.or(`contact_details->>'fullName'.ilike.%${search}%,contact_details->>'email'.ilike.%${search}%`);
    }

    if (status !== 'all') {
      countQuery = countQuery.eq('status', status as 'pending' | 'in_progress' | 'completed' | 'cancelled');
    }

    if (type !== 'all') {
      countQuery = countQuery.eq('type', type as 'regular' | 'bulk');
    }

    const { count: totalCount } = await countQuery;

    // Get inquiry statistics
    const { data: allInquiries } = await supabase
      .from('inquiries')
      .select('status, created_at');

    const stats = {
      total: totalCount || 0,
      pending: allInquiries?.filter(i => i.status === 'pending').length || 0,
      in_progress: allInquiries?.filter(i => i.status === 'in_progress').length || 0,
      completed: allInquiries?.filter(i => i.status === 'completed').length || 0,
      cancelled: allInquiries?.filter(i => i.status === 'cancelled').length || 0
    };

    return NextResponse.json({
      inquiries,
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit)
      },
      stats
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    // Use the new unified admin authentication
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { inquiryId, status } = await request.json();

    if (!inquiryId || !status) {
      return NextResponse.json(
        { error: 'Inquiry ID and status are required' },
        { status: 400 }
      );
    }

    const supabase = createSupabaseAdminClient();

    const { data: inquiry, error } = await supabase
      .from('inquiries')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', inquiryId)
      .select()
      .single();

    if (error) {
      console.error('Error updating inquiry:', error);
      return NextResponse.json(
        { error: 'Failed to update inquiry' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Inquiry updated successfully',
      inquiry
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
