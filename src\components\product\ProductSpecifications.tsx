import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface ProductSpecificationsProps {
  product: {
    brand: string;
    model: string;
    specifications?: Record<string, string>;
    specialFeatures?: string[];
    additionalFeatures?: Record<string, any>;
  };
}

export function ProductSpecifications({ product }: ProductSpecificationsProps) {
  // Filter out specifications with empty values
  const validSpecifications = product.specifications
    ? Object.entries(product.specifications).filter(([key, value]) =>
        value && value.toString().trim() !== '' && value !== 'N/A'
      )
    : [];

  // Filter out additional features with empty values
  const validAdditionalFeatures = product.additionalFeatures
    ? Object.entries(product.additionalFeatures).filter(([key, value]) => {
        if (typeof value === 'boolean') return true; // Always show boolean values
        return value && value.toString().trim() !== '' && value !== 'N/A';
      })
    : [];

  return (
    <Card className="p-6">
      <h3 className="text-xl font-bold text-white mb-6">Specifications</h3>

      <div className="space-y-6">
        {/* Basic Information */}
        <div>
          <h4 className="text-lg font-semibold text-white mb-3">Basic Information</h4>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="flex justify-between py-2 border-b border-gray-600">
              <span className="text-gray-400">Brand</span>
              <span className="text-white font-medium">{product.brand}</span>
            </div>
            <div className="flex justify-between py-2 border-b border-gray-600">
              <span className="text-gray-400">Model</span>
              <span className="text-white font-medium">{product.model}</span>
            </div>
          </div>
        </div>

        {/* Technical Specifications */}
        {validSpecifications.length > 0 && (
          <div>
            <h4 className="text-lg font-semibold text-white mb-3">Technical Specifications</h4>
            <div className="grid md:grid-cols-2 gap-4">
              {validSpecifications.map(([key, value]) => (
                <div key={key} className="flex justify-between py-2 border-b border-gray-600">
                  <span className="text-gray-400">{key}</span>
                  <span className="text-white font-medium">{value}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Special Features */}
        {product.specialFeatures && product.specialFeatures.length > 0 && (
          <div>
            <h4 className="text-lg font-semibold text-white mb-3">Special Features</h4>
            <div className="flex flex-wrap gap-2">
              {product.specialFeatures.map((feature, index) => (
                <Badge key={index} variant="gradient" className="text-sm">
                  {feature}
                </Badge>
              ))}
            </div>
          </div>
        )}

        {/* Additional Features */}
        {validAdditionalFeatures.length > 0 && (
          <div>
            <h4 className="text-lg font-semibold text-white mb-3">Additional Features</h4>
            <div className="grid md:grid-cols-2 gap-4">
              {validAdditionalFeatures.map(([key, value]) => (
                <div key={key} className="flex justify-between py-2 border-b border-gray-600">
                  <span className="text-gray-400">{key.charAt(0).toUpperCase() + key.slice(1)}</span>
                  <span className="text-white font-medium">
                    {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : value}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
}
