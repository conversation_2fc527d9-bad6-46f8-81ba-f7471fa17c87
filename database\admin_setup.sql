-- Initial roles and admin users setup
-- Run this script in your Supabase SQL editor

-- Create default roles
INSERT INTO roles (id, name, permissions) VALUES 
(
  'b4f1c8d2-9a7e-4f2b-8c3d-6e5f9a8b7c6d',
  'Super Admin',
  '[
    {"resource": "products", "actions": ["create", "read", "update", "delete"]},
    {"resource": "inquiries", "actions": ["create", "read", "update", "delete"]},
    {"resource": "users", "actions": ["create", "read", "update", "delete"]},
    {"resource": "roles", "actions": ["create", "read", "update", "delete"]},
    {"resource": "chat", "actions": ["create", "read", "update", "delete"]},
    {"resource": "inventory", "actions": ["create", "read", "update", "delete"]}
  ]'::jsonb
),
(
  'c5g2d9e3-ab8f-5g3c-9d4e-7f6g0b9c8d7e',
  'Admin',
  '[
    {"resource": "products", "actions": ["create", "read", "update", "delete"]},
    {"resource": "inquiries", "actions": ["read", "update"]},
    {"resource": "users", "actions": ["read"]},
    {"resource": "chat", "actions": ["create", "read", "update"]},
    {"resource": "inventory", "actions": ["read", "update"]}
  ]'::jsonb
),
(
  'd6h3e0f4-bc9g-6h4d-ae5f-8g7h1c0d9e8f',
  'Support',
  '[
    {"resource": "inquiries", "actions": ["read", "update"]},
    {"resource": "chat", "actions": ["create", "read"]},
    {"resource": "products", "actions": ["read"]}
  ]'::jsonb
)
ON CONFLICT (id) DO NOTHING;

-- Create a default super admin user
-- Password: admin123 (hashed with bcrypt)
-- Please change this password after first login!
INSERT INTO admin_users (id, email, password_hash, role_id, full_name, is_active) VALUES
(
  'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
  '<EMAIL>',
  '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', -- password: admin123
  'b4f1c8d2-9a7e-4f2b-8c3d-6e5f9a8b7c6d', -- Super Admin role
  'System Administrator',
  true
)
ON CONFLICT (email) DO NOTHING;

-- Create a support admin user
-- Password: support123 (hashed with bcrypt)
INSERT INTO admin_users (id, email, password_hash, role_id, full_name, is_active) VALUES
(
  'b2c3d4e5-f6g7-8h9i-0j1k-l2m3n4o5p6q7',
  '<EMAIL>',
  '$2a$10$5OpSaVZT2KCq1UBbw9UpVOKFiUJJCUW.NbKL.K8hF5fYJFnKzaOUa', -- password: support123
  'd6h3e0f4-bc9g-6h4d-ae5f-8g7h1c0d9e8f', -- Support role
  'Support Admin',
  true
)
ON CONFLICT (email) DO NOTHING;
