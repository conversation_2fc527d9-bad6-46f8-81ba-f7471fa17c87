'use client';

import { useState, useEffect } from 'react';

interface TestResult {
  status: number | string;
  data: {
    success?: boolean;
    message?: string;
    token?: string;
    error?: string;
    [key: string]: unknown;
  };
}

interface EnvStatus {
  success?: boolean;
  environment?: {
    supabaseUrl?: string;
    supabaseKey?: string;
  };
  roles?: Array<{ id: string; name: string; [key: string]: unknown }>;
  adminUsers?: Array<{ id: string; email: string; [key: string]: unknown }>;
  error?: string;
}

export default function AdminTestPage() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('admin123');
  const [result, setResult] = useState<TestResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [envStatus, setEnvStatus] = useState<EnvStatus | null>(null);
  const testAdminLogin = async () => {
    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/admin/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();
      setResult({
        status: response.status,
        data,
      });
    } catch (error) {
      setResult({
        status: 'Error',
        data: { error: error instanceof Error ? error.message : 'Unknown error' },
      });
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    // Check environment variables
    const checkEnv = async () => {
      try {
        const response = await fetch('/api/test/connection');
        const data = await response.json();
        setEnvStatus(data);
      } catch (error) {
        setEnvStatus({
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    };
    
    checkEnv();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Admin Authentication Test</h1>
        
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <h2 className="text-xl font-semibold mb-4">Admin Login Test</h2>
          
          <div className="space-y-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Password
              </label>
              <input
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full p-2 border border-gray-300 rounded"
              />
            </div>
          </div>
          
          <button
            onClick={testAdminLogin}
            disabled={loading}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test Admin Login'}
          </button>
        </div>

        {result && (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Test Result</h2>
            <div className={`p-4 rounded mb-4 ${
              result.status === 200 
                ? 'bg-green-50 border border-green-200' 
                : 'bg-red-50 border border-red-200'
            }`}>
              <p className="font-semibold">Status: {result.status}</p>
            </div>
            <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
              {JSON.stringify(result.data, null, 2)}
            </pre>
          </div>
        )}        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded">
          <h3 className="font-semibold text-yellow-800">Default Admin Credentials:</h3>
          <p className="text-yellow-700">Email: <EMAIL></p>
          <p className="text-yellow-700">Password: admin123</p>
          <p className="text-yellow-700 text-sm mt-2">These were set up during database initialization.</p>
        </div>
        
        {envStatus && (
          <div className="mt-6 p-6 bg-blue-50 border border-blue-200 rounded">
            <h2 className="text-xl font-semibold mb-4">Supabase Connection Status</h2>
            
            {envStatus.error ? (
              <div className="p-4 bg-red-50 border border-red-200 rounded">
                <h3 className="font-semibold text-red-800">Connection Error:</h3>
                <p className="text-red-700">{envStatus.error}</p>
              </div>
            ) : (
              <>
                <div className={`p-4 mb-4 rounded ${
                  envStatus.success 
                    ? 'bg-green-50 border border-green-200' 
                    : 'bg-red-50 border border-red-200'
                }`}>
                  <p className="font-semibold">
                    Status: {envStatus.success ? '✅ Connected' : '❌ Failed'}
                  </p>
                  <p>
                    Environment: {envStatus.environment?.supabaseUrl} / {envStatus.environment?.supabaseKey}
                  </p>
                </div>
                
                {envStatus.roles && (
                  <div className="mb-4">
                    <h3 className="font-semibold mb-2">Roles Found: {envStatus.roles.length}</h3>
                    <div className="bg-white p-3 rounded border border-gray-200 overflow-auto max-h-40">
                      <pre className="text-xs">{JSON.stringify(envStatus.roles, null, 2)}</pre>
                    </div>
                  </div>
                )}
                
                {envStatus.adminUsers && (
                  <div>
                    <h3 className="font-semibold mb-2">Admin Users Found: {envStatus.adminUsers.length}</h3>
                    <div className="bg-white p-3 rounded border border-gray-200 overflow-auto max-h-40">
                      <pre className="text-xs">{JSON.stringify(envStatus.adminUsers, null, 2)}</pre>
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
