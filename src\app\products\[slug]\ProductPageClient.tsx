'use client';

import { useState, useEffect } from 'react';
import { notFound, useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ProductSpecifications } from '@/components/product/ProductSpecifications';
import { ProductDescription } from '@/components/product/ProductDescription';
import { RelatedProducts } from '@/components/product/RelatedProducts';
import { useAuth } from '@/contexts/AuthContext';

interface Product {
  id: string;
  name: string;
  brand?: string;
  brand_id?: string;
  condition_id?: string;
  model?: string;
  price: number;
  show_price?: boolean;
  images?: string[];
  is_active: boolean;
  stock_quantity?: number;
  status?: string;
  categories?: {
    id: string;
    name: string;
    slug: string;
  };
  brands?: {
    id: string;
    name: string;
    slug: string;
  };
  conditions?: {
    id: string;
    name: string;
    slug: string;
  };
  description?: string;
  slug: string;
  specifications?: any;
  special_features?: string[];
  additional_features?: any;
}

export default function ProductPageClient() {
  const { user, loading: authLoading } = useAuth();
  const params = useParams();
  const router = useRouter();
  const slug = params?.slug as string;
  const [product, setProduct] = useState<Product | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [hasPrivateAccess, setHasPrivateAccess] = useState(false);

  // Check for private access on component mount and when auth changes
  useEffect(() => {
    if (authLoading) return; // Wait for auth to load

    let newPrivateAccess = false;

    // Logged-in users automatically get private access
    if (user) {
      newPrivateAccess = true;
    } else {
      // For non-logged users, check localStorage
      const privateAccess = localStorage.getItem('privateProductsAccess');
      newPrivateAccess = privateAccess === 'true';
    }

    setHasPrivateAccess(newPrivateAccess);
  }, [user, authLoading]);

  useEffect(() => {
    if (authLoading) return; // Wait for auth to load
    fetchProduct();
  }, [slug, authLoading, hasPrivateAccess]);

  useEffect(() => {
    if (product?.categories?.slug) {
      fetchRelatedProducts();
    }
  }, [product]);

  const fetchProduct = async () => {
    try {
      // Determine private access: check user login or localStorage
      let shouldIncludePrivate = false;

      if (user) {
        shouldIncludePrivate = true; // Logged-in users get private access
      } else {
        const privateAccess = localStorage.getItem('privateProductsAccess');
        shouldIncludePrivate = privateAccess === 'true';
      }

      const params = new URLSearchParams();
      if (shouldIncludePrivate) params.append('includePrivate', 'true');

      const response = await fetch(`/api/products/${slug}?${params}`);
      if (response.ok) {
        const data = await response.json();
        setProduct(data.product);
      } else {
        const errorData = await response.json();
        console.error('Error fetching product:', errorData);
        notFound();
      }
    } catch (error) {
      console.error('Error fetching product:', error);
      notFound();
    } finally {
      setIsLoading(false);
    }
  };

  const fetchRelatedProducts = async () => {
    try {
      // Include private products in related products if user has access
      const params = new URLSearchParams();
      params.append('category', product?.categories?.slug || '');
      params.append('limit', '4');
      if (hasPrivateAccess) params.append('includePrivate', 'true');

      const response = await fetch(`/api/products?${params}`);
      if (response.ok) {
        const data = await response.json();
        setRelatedProducts(data.products || []);
      }
    } catch (error) {
      console.error('Error fetching related products:', error);
    }
  };

  if (isLoading || authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#03132a] text-white flex items-center justify-center">
        <div className="w-8 h-8 border-2 border-[#956358] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!product) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#03132a] text-white">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="text-sm text-gray-400 mb-8">
          <span>Home</span> / <span>Products</span> / <span className="text-white">{product.name}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="aspect-square relative rounded-lg overflow-hidden bg-gray-800">
              <Image
                src={product.images?.[0] || '/api/placeholder-image'}
                alt={product.name}
                fill
                className="object-cover"
              />
            </div>
            
            {/* Thumbnail Images */}
            {product.images && product.images.length > 1 && (
              <div className="grid grid-cols-4 gap-2">
                {product.images.slice(1).map((image, index) => (
                  <div key={index} className="aspect-square relative rounded-lg overflow-hidden bg-gray-800">
                    <Image
                      src={image}
                      alt={`${product.name} view ${index + 2}`}
                      fill
                      className="object-cover cursor-pointer hover:opacity-75 transition-opacity"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Product Details */}
          <div className="space-y-6">
            {/* Product Title and Brand */}
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">{product.name}</h1>
              <p className="text-gray-400">
                {product.brands?.name || product.brand} {product.model && `• ${product.model}`}
              </p>
            </div>

            {/* Price */}
            <div className="flex items-center gap-4">
              {product.show_price ? (
                <span className="text-4xl font-bold text-white">
                  ₹{product.price.toLocaleString('en-IN')}
                </span>
              ) : (
                <span className="text-2xl font-bold text-[#956358]">
                  Contact for Price
                </span>
              )}
              <Badge variant="gradient">
                {product.conditions?.name || 'Excellent'}
              </Badge>
            </div>

            {/* Stock Status */}
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${
                product.is_active && product.status === 'active' && (product.stock_quantity || 0) > 0 
                  ? 'bg-green-500' 
                  : 'bg-red-500'
              }`}></div>
              <span className="text-gray-300">
                {product.is_active && product.status === 'active' && (product.stock_quantity || 0) > 0 
                  ? 'In Stock' 
                  : 'Out of Stock'
                }
              </span>
            </div>

            {/* Key Features */}
            {product.special_features && product.special_features.length > 0 && (
              <div>
                <h3 className="text-lg font-semibold text-white mb-3">Key Features</h3>
                <ul className="space-y-2">
                  {product.special_features.map((feature, index) => (
                    <li key={index} className="flex items-center gap-2 text-gray-300">
                      <div className="w-1.5 h-1.5 bg-[#956358] rounded-full"></div>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex gap-4">
              <Button
                size="lg"
                className="flex-1"
                disabled={!product.is_active}
                onClick={() => router.push(`/inquiry?product=${product.id}&type=regular`)}
              >
                Inquire Now
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => router.push(`/inquiry?product=${product.id}&type=bulk`)}
                disabled={!product.is_active}
              >
                Bulk Order
              </Button>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="grid lg:grid-cols-2 gap-8 mb-12">
          <div>
            <ProductSpecifications
              product={{
                brand: product.brands?.name || product.brand || 'Unknown Brand',
                model: product.model || 'Unknown Model',
                specifications: product.specifications || {},
                specialFeatures: product.special_features || [],
                additionalFeatures: product.additional_features || {}
              }}
            />
          </div>
          <div>
            <ProductDescription
              description={product.description || 'No description available.'}
            />
          </div>
        </div>

        {/* Related Products */}
        <RelatedProducts
          products={relatedProducts.map(p => ({
            id: p.id,
            name: p.name,
            brand: p.brands?.name || p.brand || 'Unknown Brand',
            model: p.model || '',
            price: p.price,
            originalPrice: p.price * 1.2, // Mock original price
            condition: p.conditions?.name || 'Excellent',
            images: p.images || [],
            category: p.categories?.slug || 'products',
            inStock: p.is_active && p.status === 'active' && (p.stock_quantity || 0) > 0,
            ram: p.specifications?.['RAM'] || 'N/A',
            storage: p.specifications?.['Storage'] || 'N/A',
            slug: p.slug
          }))}
          currentProductId={product.id}
        />
      </div>
    </div>
  );
}
