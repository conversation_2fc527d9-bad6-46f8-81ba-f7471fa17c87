'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Edit3,
  Save,
  X,
  Settings,
  History,
  LogOut,
  Building2,
  Globe,
  FileText,
  Users
} from 'lucide-react';

interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  phone: string;
  address: string;
  created_at: string;
  // Business Information
  company_name?: string;
  business_type?: string;
  tax_id?: string;
  website?: string;
  business_address?: string;
  business_phone?: string;
}

export function UserProfilePage() {
  const { user, signOut, loading } = useAuth();
  const router = useRouter();
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState({
    full_name: '',
    phone_number: '',
    address: '',
    // Business Information
    company_name: '',
    business_type: '',
    tax_id: '',
    website: '',
    business_address: '',
    business_phone: ''
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin?redirect=/user/profile');
    }
  }, [user, loading, router]);

  // Fetch user profile
  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) return;

      try {
        const response = await fetch('/api/user/profile');
        if (response.ok) {
          const data = await response.json();
          setProfile(data.profile);
          setEditData({
            full_name: data.profile.full_name || '',
            phone_number: data.profile.phone || '',
            address: data.profile.address || '',
            // Business Information
            company_name: data.profile.company_name || '',
            business_type: data.profile.business_type || '',
            tax_id: data.profile.tax_id || '',
            website: data.profile.website || '',
            business_address: data.profile.business_address || '',
            business_phone: data.profile.business_phone || ''
          });
        } else {
          throw new Error('Failed to fetch profile');
        }
      } catch (error) {
        console.error('Error fetching profile:', error);
        setError('Failed to load profile');
      } finally {
        setIsLoading(false);
      }
    };

    fetchProfile();
  }, [user]);

  const handleEdit = () => {
    setIsEditing(true);
    setError(null);
  };

  const handleCancel = () => {
    setIsEditing(false);
    if (profile) {
      setEditData({
        full_name: profile.full_name || '',
        phone_number: profile.phone || '',
        address: profile.address || '',
        // Business Information
        company_name: profile.company_name || '',
        business_type: profile.business_type || '',
        tax_id: profile.tax_id || '',
        website: profile.website || '',
        business_address: profile.business_address || '',
        business_phone: profile.business_phone || ''
      });
    }
    setError(null);
  };

  const handleSave = async () => {
    if (!profile) return;

    setIsSaving(true);
    setError(null);

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editData),
      });

      if (response.ok) {
        const data = await response.json();
        setProfile(data.profile);
        setIsEditing(false);
      } else {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update profile');
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setIsSaving(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setEditData(prev => ({ ...prev, [name]: value }));
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      router.push('/');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#1a2b4a] flex items-center justify-center">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }

  if (!user || !profile) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#1a2b4a] py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">My Profile</h1>
          <p className="text-gray-300">Manage your account information</p>
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-[#1a2b4a]/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-6 shadow-2xl">
              <div className="text-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-r from-[#956358] to-[#f9c1b2] rounded-full mx-auto mb-4 flex items-center justify-center">
                  <User className="h-10 w-10 text-white" />
                </div>
                <h2 className="text-xl font-semibold text-white">{profile.full_name}</h2>
                <p className="text-gray-400">{profile.email}</p>
              </div>

              <nav className="space-y-2">
                <button className="w-full flex items-center space-x-3 px-4 py-3 text-left text-white bg-[#956358]/20 rounded-lg border border-[#956358]/30">
                  <Settings className="h-5 w-5" />
                  <span>Profile Settings</span>
                </button>
                
                <button 
                  onClick={() => router.push('/user/orders')}
                  className="w-full flex items-center space-x-3 px-4 py-3 text-left text-gray-300 hover:text-white hover:bg-[#0a1e3d] rounded-lg transition-colors"
                >
                  <History className="h-5 w-5" />
                  <span>Order History</span>
                </button>

                <button 
                  onClick={handleSignOut}
                  className="w-full flex items-center space-x-3 px-4 py-3 text-left text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-lg transition-colors"
                >
                  <LogOut className="h-5 w-5" />
                  <span>Sign Out</span>
                </button>
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-2">
            <div className="bg-[#1a2b4a]/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-semibold text-white">Account Information</h3>
                {!isEditing ? (
                  <Button
                    onClick={handleEdit}
                    variant="outline"
                    className="flex items-center space-x-2"
                  >
                    <Edit3 className="h-4 w-4" />
                    <span>Edit Profile</span>
                  </Button>
                ) : (
                  <div className="flex space-x-2">
                    <Button
                      onClick={handleSave}
                      disabled={isSaving}
                      className="flex items-center space-x-2 bg-gradient-to-r from-[#956358] to-[#f9c1b2]"
                    >
                      <Save className="h-4 w-4" />
                      <span>{isSaving ? 'Saving...' : 'Save'}</span>
                    </Button>
                    <Button
                      onClick={handleCancel}
                      variant="outline"
                      className="flex items-center space-x-2"
                    >
                      <X className="h-4 w-4" />
                      <span>Cancel</span>
                    </Button>
                  </div>
                )}
              </div>

              {/* Error Message */}
              {error && (
                <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                  <p className="text-red-400">{error}</p>
                </div>
              )}

              {/* Form */}
              <div className="space-y-6">
                {/* Full Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    Full Name
                  </label>
                  {isEditing ? (
                    <div className="relative">
                      <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        type="text"
                        name="full_name"
                        value={editData.full_name}
                        onChange={handleChange}
                        className="w-full pl-10 pr-4 py-3 bg-[#0a1e3d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent transition-all"
                        placeholder="Enter your full name"
                      />
                    </div>
                  ) : (
                    <div className="flex items-center space-x-3 p-3 bg-[#0a1e3d] rounded-lg">
                      <User className="h-5 w-5 text-gray-400" />
                      <span className="text-white">{profile.full_name || 'Not provided'}</span>
                    </div>
                  )}
                </div>

                {/* Email (Read-only) */}
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    Email Address
                  </label>
                  <div className="flex items-center space-x-3 p-3 bg-[#0a1e3d] rounded-lg opacity-60">
                    <Mail className="h-5 w-5 text-gray-400" />
                    <span className="text-white">{profile.email}</span>
                  </div>
                </div>

                {/* Phone Number */}
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    Phone Number
                  </label>
                  {isEditing ? (
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        type="tel"
                        name="phone_number"
                        value={editData.phone_number}
                        onChange={handleChange}
                        className="w-full pl-10 pr-4 py-3 bg-[#0a1e3d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent transition-all"
                        placeholder="Enter your phone number"
                      />
                    </div>
                  ) : (
                    <div className="flex items-center space-x-3 p-3 bg-[#0a1e3d] rounded-lg">
                      <Phone className="h-5 w-5 text-gray-400" />
                      <span className="text-white">{profile.phone || 'Not provided'}</span>
                    </div>
                  )}
                </div>

                {/* Address */}
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    Address
                  </label>
                  {isEditing ? (
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                      <textarea
                        name="address"
                        value={editData.address}
                        onChange={handleChange}
                        rows={3}
                        className="w-full pl-10 pr-4 py-3 bg-[#0a1e3d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent transition-all resize-none"
                        placeholder="Enter your full address"
                      />
                    </div>
                  ) : (
                    <div className="flex items-start space-x-3 p-3 bg-[#0a1e3d] rounded-lg">
                      <MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
                      <span className="text-white">{profile.address || 'Not provided'}</span>
                    </div>
                  )}
                </div>

                {/* Account Created */}
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    Member Since
                  </label>
                  <div className="flex items-center space-x-3 p-3 bg-[#0a1e3d] rounded-lg opacity-60">
                    <Calendar className="h-5 w-5 text-gray-400" />
                    <span className="text-white">{formatDate(profile.created_at)}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Business Information Section */}
            <div className="bg-[#1a2b4a]/50 backdrop-blur-sm border border-gray-700/50 rounded-2xl p-8 shadow-2xl mt-8">
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-semibold text-white flex items-center space-x-3">
                  <Building2 className="h-6 w-6 text-[#956358]" />
                  <span>Business Information</span>
                </h3>
              </div>

              {/* Business Form */}
              <div className="space-y-6">
                {/* Company Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    Company Name
                  </label>
                  {isEditing ? (
                    <div className="relative">
                      <Building2 className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        type="text"
                        name="company_name"
                        value={editData.company_name}
                        onChange={handleChange}
                        className="w-full pl-10 pr-4 py-3 bg-[#0a1e3d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent transition-all"
                        placeholder="Enter your company name"
                      />
                    </div>
                  ) : (
                    <div className="flex items-center space-x-3 p-3 bg-[#0a1e3d] rounded-lg">
                      <Building2 className="h-5 w-5 text-gray-400" />
                      <span className="text-white">{profile.company_name || 'Not provided'}</span>
                    </div>
                  )}
                </div>

                {/* Business Type */}
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    Business Type
                  </label>
                  {isEditing ? (
                    <div className="relative">
                      <Users className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <select
                        name="business_type"
                        value={editData.business_type}
                        onChange={handleChange}
                        className="w-full pl-10 pr-4 py-3 bg-[#0a1e3d] border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent transition-all"
                      >
                        <option value="">Select business type</option>
                        <option value="retailer">Retailer</option>
                        <option value="wholesaler">Wholesaler</option>
                        <option value="distributor">Distributor</option>
                        <option value="reseller">Reseller</option>
                        <option value="corporate">Corporate</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-3 p-3 bg-[#0a1e3d] rounded-lg">
                      <Users className="h-5 w-5 text-gray-400" />
                      <span className="text-white">{profile.business_type || 'Not provided'}</span>
                    </div>
                  )}
                </div>

                {/* Tax ID */}
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    Tax ID / Business Registration Number
                  </label>
                  {isEditing ? (
                    <div className="relative">
                      <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        type="text"
                        name="tax_id"
                        value={editData.tax_id}
                        onChange={handleChange}
                        className="w-full pl-10 pr-4 py-3 bg-[#0a1e3d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent transition-all"
                        placeholder="Enter your tax ID or business registration number"
                      />
                    </div>
                  ) : (
                    <div className="flex items-center space-x-3 p-3 bg-[#0a1e3d] rounded-lg">
                      <FileText className="h-5 w-5 text-gray-400" />
                      <span className="text-white">{profile.tax_id || 'Not provided'}</span>
                    </div>
                  )}
                </div>

                {/* Website */}
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    Website
                  </label>
                  {isEditing ? (
                    <div className="relative">
                      <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        type="url"
                        name="website"
                        value={editData.website}
                        onChange={handleChange}
                        className="w-full pl-10 pr-4 py-3 bg-[#0a1e3d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent transition-all"
                        placeholder="Enter your website URL"
                      />
                    </div>
                  ) : (
                    <div className="flex items-center space-x-3 p-3 bg-[#0a1e3d] rounded-lg">
                      <Globe className="h-5 w-5 text-gray-400" />
                      <span className="text-white">{profile.website || 'Not provided'}</span>
                    </div>
                  )}
                </div>

                {/* Business Phone */}
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    Business Phone Number
                  </label>
                  {isEditing ? (
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        type="tel"
                        name="business_phone"
                        value={editData.business_phone}
                        onChange={handleChange}
                        className="w-full pl-10 pr-4 py-3 bg-[#0a1e3d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent transition-all"
                        placeholder="Enter your business phone number"
                      />
                    </div>
                  ) : (
                    <div className="flex items-center space-x-3 p-3 bg-[#0a1e3d] rounded-lg">
                      <Phone className="h-5 w-5 text-gray-400" />
                      <span className="text-white">{profile.business_phone || 'Not provided'}</span>
                    </div>
                  )}
                </div>

                {/* Business Address */}
                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-2">
                    Business Address
                  </label>
                  {isEditing ? (
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                      <textarea
                        name="business_address"
                        value={editData.business_address}
                        onChange={handleChange}
                        rows={3}
                        className="w-full pl-10 pr-4 py-3 bg-[#0a1e3d] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent transition-all resize-none"
                        placeholder="Enter your business address"
                      />
                    </div>
                  ) : (
                    <div className="flex items-start space-x-3 p-3 bg-[#0a1e3d] rounded-lg">
                      <MapPin className="h-5 w-5 text-gray-400 mt-0.5" />
                      <span className="text-white">{profile.business_address || 'Not provided'}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
