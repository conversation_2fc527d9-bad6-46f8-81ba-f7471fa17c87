'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';
import Image from 'next/image';
import { Package } from 'lucide-react';

interface ExpandableCardProps {
  children: React.ReactNode;
  className?: string;
  expandedContent?: React.ReactNode;
  onExpand?: () => void;
  onCollapse?: () => void;
}

export function ExpandableCard({
  children,
  className,
  expandedContent,
  onExpand,
  onCollapse
}: ExpandableCardProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleMouseEnter = () => {
    setIsHovered(true);
    setIsExpanded(true);
    onExpand?.();
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setIsExpanded(false);
    onCollapse?.();
  };

  return (
    <motion.div
      className={cn(
        "relative overflow-visible rounded-lg cursor-pointer",
        className
      )}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      animate={{
        scale: isExpanded ? 1.3 : 1,
        zIndex: isExpanded ? 50 : 1,
      }}
      transition={{
        duration: 0.4,
        ease: "easeInOut",
      }}
      style={{
        transformOrigin: "center",
      }}
    >
      {/* Base Card Content */}
      <motion.div
        className="relative z-10"
        animate={{
          opacity: isExpanded ? 0 : 1,
        }}
        transition={{
          duration: 0.3,
          ease: "easeInOut",
        }}
      >
        {children}
      </motion.div>

      {/* Expanded Content */}
      <AnimatePresence>
        {isExpanded && expandedContent && (
          <motion.div
            className="absolute inset-0 z-20"
            initial={{
              opacity: 0,
            }}
            animate={{
              opacity: 1,
            }}
            exit={{
              opacity: 0,
            }}
            transition={{
              duration: 0.3,
              ease: "easeInOut",
            }}
          >
            {expandedContent}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

interface CategoryCardProps {
  category: {
    id: string;
    name: string;
    slug: string;
    image: string;
    products: Array<{
      id: string;
      name: string;
      price: number;
      images: string[];
      brand: string;
      model: string;
      show_price?: boolean;
      slug: string;
    }>;
  };
  gallerySettings: {
    products_per_category: number;
    grid_columns: number;
    show_price: boolean;
    hover_animation: boolean;
    auto_hide_delay: number;
  };
  formatPrice: (price: number) => string;
}

export function CategoryCard({ category, gallerySettings, formatPrice }: CategoryCardProps) {
  const [imageErrors, setImageErrors] = useState<Set<string>>(new Set());

  const handleImageError = (productId: string) => {
    setImageErrors(prev => new Set(prev).add(productId));
  };
  const baseCard = (
    <div className="relative bg-gradient-card border border-shadow-grey/20 hover:border-accent-secondary/30 transition-all duration-500 hover:shadow-glow overflow-hidden h-80 w-full rounded-lg">
      {/* Category Image Background */}
      <div className="absolute inset-0 w-full h-full overflow-hidden rounded-lg">
        <div
          className="w-full h-full bg-cover bg-center bg-no-repeat transition-transform duration-700 group-hover:scale-110"
          style={{
            backgroundImage: `url(${category.image || '/images/placeholder-category.jpg'})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            minHeight: '100%',
            minWidth: '100%'
          }}
        />
        {/* Enhanced Black Overlay for Better Text Visibility */}
        <div className="absolute inset-0 bg-black/60 w-full h-full" />
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-black/20 w-full h-full" />
      </div>

      {/* Category Info Overlay */}
      <div className="absolute inset-0 p-6 flex flex-col justify-end">
        <div className="relative z-10">
          <h3 className="text-2xl font-bold text-white mb-2 group-hover:text-accent-secondary transition-colors drop-shadow-lg">
            {category.name}
          </h3>
          <p className="text-white/90 text-sm drop-shadow-md">
            Explore our {category.name.toLowerCase()} collection
          </p>
        </div>
      </div>
    </div>
  );

  const expandedCard = (
    <div className="bg-white/10 backdrop-blur-xl rounded-lg h-full w-full p-8 border border-white/20 shadow-2xl">
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h4 className="text-white font-bold text-2xl drop-shadow-lg">{category.name}</h4>
            <p className="text-white/80 text-base drop-shadow-md">{category.products.length} Products Available</p>
          </div>
          <a href={`/products?category=${category.slug}`}>
            <button className="bg-gradient-primary hover:bg-gradient-primary/90 border-0 shadow-lg px-4 py-2 rounded-md text-white text-sm font-medium flex items-center space-x-2 transition-all duration-300 hover:scale-105">
              <span>View All</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </a>
        </div>

        {/* Product Grid - Up to 10 Products */}
        <div className="flex-1 overflow-y-auto max-h-100 w-88 pl-2 pr-10 scrollbar-left" style={{ direction: 'rtl' }}>
          <div className={`grid gap-6 ${gallerySettings.grid_columns === 3 ? 'grid-cols-3' : 'grid-cols-2'}`} style={{ direction: 'ltr' }}>
            {category.products.slice(0, 10).map((product, index) => (
              <a
                key={product.id}
                href={`/products/${product.slug}`}
                className="block"
              >
                <motion.div
                  className={`m-4 bg-white/20 rounded-xl p-3 hover:bg-white/30 transition-all duration-500 group/product border border-white/20 cursor-pointer backdrop-blur-sm w-full shadow-lg hover:shadow-2xl hover:shadow-accent-primary/20 ${gallerySettings.hover_animation ? 'hover:scale-105' : ''}`}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.05 }}
                  whileHover={{
                    scale: gallerySettings.hover_animation ? 1.05 : 1,
                    boxShadow: "0 25px 50px -12px rgba(149, 99, 88, 0.25)"
                  }}
                >
                  {/* Image Container */}
                  <div className="aspect-square bg-shadow-grey/30 rounded-lg mb-2 overflow-hidden relative flex items-center justify-center">
                    {imageErrors.has(product.id) || !product.images?.[0] ? (
                      <Package className="w-12 h-12 text-white/60" />
                    ) : (
                      <>
                        <Image
                          src={product.images[0]}
                          alt={product.name}
                          fill
                          className={`object-cover object-center transition-all duration-500 ${gallerySettings.hover_animation ? 'group-hover/product:scale-110 group-hover/product:brightness-110' : ''}`}
                          style={{ objectFit: 'cover' }}
                          onError={() => handleImageError(product.id)}
                        />
                        {/* Elegant overlay on hover */}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover/product:opacity-100 transition-opacity duration-300" />
                      </>
                    )}
                  </div>

                  {/* Product Name - 3 lines max */}
                  <div className="h-12">
                    <h6 className={`text-white font-medium transition-colors drop-shadow-md text-xs leading-tight line-clamp-3 ${gallerySettings.hover_animation ? 'group-hover/product:text-accent-secondary' : ''}`} title={product.name}>
                      {product.name}
                    </h6>
                  </div>
                </motion.div>
              </a>
            ))}
          </div>

          {/* Show More Button if there are more products */}
          {category.products.length > 10 && (
            <motion.div
              className="mt-6 text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <a href={`/products?category=${category.slug}`}>
                <div className="inline-block px-6 py-3 bg-white/20 rounded-xl border border-white/30 text-white text-base font-medium backdrop-blur-sm hover:bg-white/30 hover:scale-105 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-xl">
                  +{category.products.length - 10} More Products
                </div>
              </a>
            </motion.div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <ExpandableCard
      className="group"
      expandedContent={expandedCard}
    >
      {baseCard}
    </ExpandableCard>
  );
}
