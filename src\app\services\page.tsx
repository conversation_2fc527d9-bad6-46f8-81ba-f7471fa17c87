import { Metadata } from 'next';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

export const metadata: Metadata = {
  title: 'Our Services - Tisha International',
  description: 'Discover our comprehensive services including device refurbishment, bulk orders, technical support, and dealer programs.',
};

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--background-primary)] via-[var(--background-secondary)] to-[var(--background-card)]">
      {/* Hero Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Our <span className="bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light bg-clip-text text-transparent">
              Services
            </span>
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Comprehensive solutions for all your refurbished technology needs. From individual purchases to enterprise solutions.
          </p>
        </div>
      </section>

      {/* Main Services Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8 mb-16">
            <Card className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">🔧</span>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">Device Refurbishment</h2>
                  <Badge variant="gradient" className="mt-2">Premium Quality</Badge>
                </div>
              </div>
              <p className="text-gray-300 mb-6 leading-relaxed">
                Our comprehensive 47-point refurbishment process ensures every device meets 
                the highest standards of quality and performance. Each unit undergoes thorough 
                testing, cleaning, and certification.
              </p>
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Hardware diagnostics and testing</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Data wiping and OS installation</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Physical cleaning and cosmetic restoration</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Quality assurance certification</span>
                </li>
              </ul>
            </Card>

            <Card className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">📦</span>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">Bulk Orders</h2>
                  <Badge variant="gradient" className="mt-2">Enterprise Solutions</Badge>
                </div>
              </div>
              <p className="text-gray-300 mb-6 leading-relaxed">
                Special pricing and customized solutions for businesses, educational institutions, 
                and organizations requiring multiple devices. We handle orders from 10 to 1000+ units.
              </p>
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Volume discounts available</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Custom configuration options</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Flexible payment terms</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Dedicated account management</span>
                </li>
              </ul>
            </Card>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            <Card className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">🛠️</span>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">Technical Support</h2>
                  <Badge variant="gradient" className="mt-2">24/7 Available</Badge>
                </div>
              </div>
              <p className="text-gray-300 mb-6 leading-relaxed">
                Comprehensive technical support from our certified technicians. We provide 
                ongoing assistance to ensure your devices perform optimally throughout their lifecycle.
              </p>
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Remote troubleshooting support</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Hardware repair services</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Software installation and updates</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Performance optimization</span>
                </li>
              </ul>
            </Card>

            <Card className="p-8">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-lg flex items-center justify-center">
                  <span className="text-white text-2xl font-bold">🤝</span>
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-white">Dealer Program</h2>
                  <Badge variant="gradient" className="mt-2">Partnership Opportunity</Badge>
                </div>
              </div>
              <p className="text-gray-300 mb-6 leading-relaxed">
                Join our dealer network and expand your business with our premium refurbished 
                devices. We provide comprehensive support and competitive pricing for our partners.
              </p>
              <ul className="space-y-3">
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Competitive dealer pricing</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Marketing and sales support</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Training and certification programs</span>
                </li>
                <li className="flex items-center gap-3">
                  <span className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full"></span>
                  <span className="text-gray-300">Dedicated dealer portal</span>
                </li>
              </ul>
              <Button variant="primary" className="mt-6 bg-gradient-to-r from-[#f9c1b2] to-[#956358] hover:from-[#956358] hover:to-[#f9c1b2]">
                Learn More About Partnership
              </Button>
            </Card>
          </div>
        </div>
      </section>

      {/* Service Process Section */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <Card className="p-8">
            <h2 className="text-3xl font-bold text-white text-center mb-12">Our Service Process</h2>
            <div className="grid md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-xl">1</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-3">Inquiry</h3>
                <p className="text-gray-300 text-sm">
                  Submit your requirements through our easy inquiry system
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-xl">2</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-3">Consultation</h3>
                <p className="text-gray-300 text-sm">
                  Our experts provide personalized recommendations and quotes
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-xl">3</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-3">Processing</h3>
                <p className="text-gray-300 text-sm">
                  We prepare and test your devices according to specifications
                </p>
              </div>

              <div className="text-center">
                <div className="w-16 h-16 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white font-bold text-xl">4</span>
                </div>
                <h3 className="text-lg font-semibold text-white mb-3">Delivery</h3>
                <p className="text-gray-300 text-sm">
                  Fast and secure delivery with ongoing support included
                </p>
              </div>
            </div>
          </Card>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4">
        <div className="max-w-4xl mx-auto text-center">
          <Card className="p-8">
            <h2 className="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
            <p className="text-xl text-gray-300 mb-8">
              Contact us today to discuss your requirements and discover how we can help your business.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="primary" size="lg" className="bg-gradient-to-r from-[#f9c1b2] to-[#956358] hover:from-[#956358] hover:to-[#f9c1b2]">
                Request Quote
              </Button>
              <Button variant="outline" size="lg">
                Contact Support
              </Button>
            </div>
          </Card>
        </div>
      </section>
    </div>
  );
}
