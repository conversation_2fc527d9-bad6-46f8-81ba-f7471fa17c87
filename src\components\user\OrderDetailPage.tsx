'use client';

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import { formatPrice } from '@/lib/product-utils';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ChatWindow } from '@/components/chat/ChatWindow';
import { supabase } from '@/lib/supabase/client';
import {
  ArrowLeft,
  Package,
  Calendar,
  MessageCircle,
  User,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Phone,
  Mail,
  MapPin,
  Hash,
  ShoppingCart,
  Link,
  Send,
  Loader2
} from 'lucide-react';

interface Inquiry {
  id: string;
  product_id: string;
  type: 'regular' | 'bulk';
  quantity: number;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  subject: string;
  message: string;
  contact_details: {
    fullName: string;
    email: string;
    phone: string;
    whatsappNumber: string;
    address: string;
  };
  created_at: string;
  updated_at: string;
  product: {
    id: string;
    name: string;
    brand: string;
    model: string;
    price: number;
    images: string[];
    description?: string;
    specifications?: Record<string, any>;
    features?: string[];
  };
}

interface ChatMessage {
  id: string;
  inquiry_id: string;
  sender_type: 'user' | 'admin';
  sender_id: string;
  message_type: 'text' | 'image' | 'link';
  message: string;
  created_at: string;
}

interface OrderDetailPageProps {
  orderId: string;
}

export function OrderDetailPage({ orderId }: OrderDetailPageProps) {
  const { user, loading } = useAuth();
  const router = useRouter();
  
  const [inquiry, setInquiry] = useState<Inquiry | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [hasInquiryLoaded, setHasInquiryLoaded] = useState(false);

  // Redirect if not authenticated
  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin?redirect=/user/orders/' + orderId);
    }
  }, [user, loading, router, orderId]);

  // Fetch inquiry details (only once)
  useEffect(() => {
    const fetchInquiry = async () => {
      if (!user || hasInquiryLoaded) return;

      try {
        const response = await fetch(`/api/inquiries/${orderId}`);
        if (response.ok) {
          const data = await response.json();
          setInquiry(data.inquiry);
          setHasInquiryLoaded(true);
        } else {
          throw new Error('Failed to fetch inquiry details');
        }
      } catch (error) {
        console.error('Error fetching inquiry:', error);
        setError('Failed to load inquiry details');
      } finally {
        setIsLoading(false);
      }
    };

    fetchInquiry();
  }, [user, orderId, hasInquiryLoaded]);



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-400 bg-yellow-400/10 border-yellow-400/20';
      case 'in_progress':
        return 'text-blue-400 bg-blue-400/10 border-blue-400/20';
      case 'completed':
        return 'text-green-400 bg-green-400/10 border-green-400/20';
      case 'cancelled':
        return 'text-red-400 bg-red-400/10 border-red-400/20';
      default:
        return 'text-gray-400 bg-gray-400/10 border-gray-400/20';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4" />;
      case 'in_progress':
        return <AlertCircle className="h-4 w-4" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };



  if (loading || isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#1a2b4a] flex items-center justify-center">
        <div className="text-white text-lg">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  if (error || !inquiry) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#1a2b4a] flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-400 mb-4">{error || 'Inquiry not found'}</div>
          <Button onClick={() => router.back()}>Go Back</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#1a2b4a] py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </Button>
          
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white mb-2">Order Details</h1>
              <p className="text-gray-300">
                Inquiry ID: {inquiry.id.slice(0, 8)}...
              </p>
            </div>
            <div className={`inline-flex items-center space-x-2 px-4 py-2 rounded-full text-sm font-medium border ${getStatusColor(inquiry.status)}`}>
              {getStatusIcon(inquiry.status)}
              <span className="capitalize">{inquiry.status.replace('_', ' ')}</span>
            </div>
          </div>
        </div>

        <div className="grid gap-8 lg:grid-cols-3">
          {/* Order Details */}
          <div className="lg:col-span-1 space-y-6">
            {/* Product Info */}
            <Card className="p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Product Details</h3>
              
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="w-20 h-20 bg-gray-700 rounded-lg overflow-hidden flex-shrink-0">
                    {inquiry.product?.images?.[0] ? (
                      <Image
                        src={inquiry.product.images[0]}
                        alt={inquiry.product.name || 'Product'}
                        width={80}
                        height={80}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Package className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                  </div>

                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-white mb-1">
                      {inquiry.product?.brand || 'Unknown Brand'} {inquiry.product?.model || ''}
                    </h4>
                    <p className="text-gray-400 text-sm mb-2">
                      {inquiry.product?.name || 'Product information unavailable'}
                    </p>
                    <div className="text-2xl font-bold text-[#f9c1b2]">
                      {inquiry.product?.price ? formatPrice(inquiry.product.price) : 'Price unavailable'}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-700">
                  <div>
                    <div className="flex items-center space-x-2 text-sm text-gray-400 mb-1">
                      <ShoppingCart className="h-4 w-4" />
                      <span>Quantity</span>
                    </div>
                    <div className="text-white font-semibold">{inquiry.quantity}</div>
                  </div>
                  
                  <div>
                    <div className="flex items-center space-x-2 text-sm text-gray-400 mb-1">
                      <Hash className="h-4 w-4" />
                      <span>Type</span>
                    </div>
                    <div className="text-white font-semibold capitalize">
                      {inquiry.type === 'regular' ? 'Regular' : 'Bulk Order'}
                    </div>
                  </div>

                  <div className="col-span-2">
                    <div className="flex items-center space-x-2 text-sm text-gray-400 mb-1">
                      <Calendar className="h-4 w-4" />
                      <span>Created</span>
                    </div>
                    <div className="text-white font-semibold">{formatDate(inquiry.created_at)}</div>
                  </div>
                  
                  <div className="col-span-2">
                    <div className="text-sm text-gray-400 mb-1">Total Amount</div>
                    <div className="text-2xl font-bold text-[#f9c1b2]">
                      {inquiry.product?.price ? formatPrice(inquiry.product.price * inquiry.quantity) : 'Price unavailable'}
                    </div>
                  </div>
                </div>
              </div>
            </Card>

            {/* Customer Info */}
            <Card className="p-6">
              <h3 className="text-xl font-semibold text-white mb-4">Customer Information</h3>
              
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <User className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="text-sm text-gray-400">Full Name</div>
                    <div className="text-white">{inquiry.contact_details.fullName}</div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Mail className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="text-sm text-gray-400">Email</div>
                    <div className="text-white">{inquiry.contact_details.email}</div>
                  </div>
                </div>

                <div className="flex items-center space-x-3">
                  <Phone className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="text-sm text-gray-400">Phone</div>
                    <div className="text-white">{inquiry.contact_details.phone}</div>
                  </div>
                </div>

                {inquiry.contact_details.whatsappNumber && (
                  <div className="flex items-center space-x-3">
                    <MessageCircle className="h-5 w-5 text-gray-400" />
                    <div>
                      <div className="text-sm text-gray-400">WhatsApp</div>
                      <div className="text-white">{inquiry.contact_details.whatsappNumber}</div>
                    </div>
                  </div>
                )}

                <div className="flex items-start space-x-3">
                  <MapPin className="h-5 w-5 text-gray-400 mt-1" />
                  <div>
                    <div className="text-sm text-gray-400">Address</div>
                    <div className="text-white">{inquiry.contact_details.address}</div>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          {/* Chat Section */}
          <div className="lg:col-span-2">
            <ChatWindow
              inquiryId={orderId}
              currentUserId={user?.id}
              title="Chat Support"
              subtitle="Communicate with our team about your inquiry"
              isAdmin={false}
            />


          </div>
        </div>
      </div>
    </div>
  );
}
