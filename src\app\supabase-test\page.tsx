'use client';

import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase/client';

interface RoleData {
  id: string;
  name: string;
  [key: string]: unknown;
}

export default function ConnectionTestPage() {
  const [connectionStatus, setConnectionStatus] = useState('Testing...');
  const [dbData, setDbData] = useState<RoleData[] | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const testConnection = async () => {
      try {
        // Test basic connection
        const { data, error } = await supabase
          .from('roles')
          .select('*')
          .limit(5);

        if (error) {
          setError(error.message);
          setConnectionStatus('Connection failed');
        } else {
          setConnectionStatus('Connected successfully!');
          setDbData(data);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        setConnectionStatus('Connection failed');
      }
    };

    testConnection();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Supabase Connection Test</h1>
        
        <div className="bg-white p-6 rounded-lg shadow-md mb-6">
          <h2 className="text-xl font-semibold mb-4">Connection Status</h2>
          <p className={`text-lg ${
            connectionStatus.includes('successfully') 
              ? 'text-green-600' 
              : connectionStatus.includes('failed')
              ? 'text-red-600'
              : 'text-yellow-600'
          }`}>
            {connectionStatus}
          </p>
          
          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded">
              <h3 className="font-semibold text-red-800">Error:</h3>
              <p className="text-red-700">{error}</p>
            </div>
          )}
        </div>

        {dbData && (
          <div className="bg-white p-6 rounded-lg shadow-md">
            <h2 className="text-xl font-semibold mb-4">Database Data (Roles)</h2>
            <pre className="bg-gray-100 p-4 rounded overflow-auto">
              {JSON.stringify(dbData, null, 2)}
            </pre>
          </div>
        )}

        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded">
          <h3 className="font-semibold text-blue-800">Environment Variables:</h3>
          <p className="text-blue-700">
            Supabase URL: {process.env.NEXT_PUBLIC_SUPABASE_URL ? '✓ Set' : '✗ Not set'}
          </p>
          <p className="text-blue-700">
            Supabase Anon Key: {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✓ Set' : '✗ Not set'}
          </p>
        </div>
      </div>
    </div>
  );
}
