'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Menu, X, Search, User, ChevronDown, Settings, LogOut, UserCircle, Package, Shield } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useAuth } from '@/contexts/AuthContext'

const mainNavigation = [
  { name: 'Home', href: '/' },
  { name: 'Products', href: '/products' },
]

const moreNavigation = [
  { name: 'Submit Inquiry', href: '/inquiry' },
  { name: 'Become Dealer', href: '/become-dealer' },
  { name: 'About Us', href: '/about' },
  { name: 'Contact', href: '/contact' },
]

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false)
  const [isMoreDropdownOpen, setIsMoreDropdownOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const [isAdmin, setIsAdmin] = useState(false)
  const pathname = usePathname()
  const { user, signOut } = useAuth()
  const dropdownRef = useRef<HTMLDivElement>(null)
  const moreDropdownRef = useRef<HTMLDivElement>(null)

  // Handle hydration
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) {
        setIsAdmin(false)
        return
      }

      try {
        const response = await fetch('/api/admin/auth/verify', {
          credentials: 'include',
        })
        setIsAdmin(response.ok)
      } catch (error) {
        console.error('Error checking admin status:', error)
        setIsAdmin(false)
      }
    }

    checkAdminStatus()
  }, [user])

  // Close dropdowns when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsUserDropdownOpen(false)
      }
      if (moreDropdownRef.current && !moreDropdownRef.current.contains(event.target as Node)) {
        setIsMoreDropdownOpen(false)
      }
    }

    if (isUserDropdownOpen || isMoreDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isUserDropdownOpen, isMoreDropdownOpen])

  // Close dropdowns when pathname changes (after navigation)
  useEffect(() => {
    setIsMoreDropdownOpen(false)
    setIsMenuOpen(false)
    setIsUserDropdownOpen(false)
  }, [pathname]);


  // Dynamic button logic based on current page and user state
  const getDynamicButton = () => {
    // Special case: Always show only "Shop Now" on homepage
    if (pathname === '/') {
      return (
        <Link href="/products" className="btn-primary text-sm px-6 py-2.5">
          Shop Now
        </Link>
      )
    }

    // If user is logged in on other pages, show user dropdown
    if (user) {
      return (
        <div className="relative" ref={dropdownRef}>
          <button
            onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
            className="flex items-center space-x-3 hover:bg-[var(--accent-primary)]/10 rounded-lg p-2 transition-all duration-300"
          >
            <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
              <User className="h-4 w-4 text-white" />
            </div>
            <span className="text-[var(--soft-white)] text-sm font-medium">
              {user.user_metadata?.full_name || user.email?.split('@')[0] || 'User'}
            </span>
            <ChevronDown className={cn(
              "h-4 w-4 text-[var(--soft-white)] transition-transform duration-300",
              isUserDropdownOpen && "rotate-180"
            )} />
          </button>

          {/* Dropdown Menu */}
          {isMounted && isUserDropdownOpen && (
            <div className="absolute right-0 mt-2 w-48 bg-[var(--background-secondary)] border border-[var(--shadow-grey)] rounded-lg shadow-professional z-50 animate-fade-in">
              <div className="py-2">
                <Link
                  href="/user/orders"
                  className="flex items-center px-4 py-2 text-sm text-[var(--soft-white)] hover:bg-[var(--accent-primary)]/10 hover:text-[var(--accent-secondary)] transition-colors duration-300"
                  onClick={() => setIsUserDropdownOpen(false)}
                >
                  <Package className="h-4 w-4 mr-3" />
                  My Orders
                </Link>
                <Link
                  href="/user/profile"
                  className="flex items-center px-4 py-2 text-sm text-[var(--soft-white)] hover:bg-[var(--accent-primary)]/10 hover:text-[var(--accent-secondary)] transition-colors duration-300"
                  onClick={() => setIsUserDropdownOpen(false)}
                >
                  <UserCircle className="h-4 w-4 mr-3" />
                  Profile
                </Link>
                <Link
                  href="/user/profile"
                  className="flex items-center px-4 py-2 text-sm text-[var(--soft-white)] hover:bg-[var(--accent-primary)]/10 hover:text-[var(--accent-secondary)] transition-colors duration-300"
                  onClick={() => setIsUserDropdownOpen(false)}
                >
                  <Settings className="h-4 w-4 mr-3" />
                  Settings
                </Link>

                {/* Admin Dashboard - Only show for admin users */}
                {isAdmin && (
                  <>
                    <hr className="my-2 border-[var(--shadow-grey)]" />
                    <Link
                      href="/admin/dashboard"
                      className="flex items-center px-4 py-2 text-sm text-[var(--soft-white)] hover:bg-[var(--accent-primary)]/10 hover:text-[var(--accent-secondary)] transition-colors duration-300"
                      onClick={() => setIsUserDropdownOpen(false)}
                    >
                      <Shield className="h-4 w-4 mr-3" />
                      Admin Dashboard
                    </Link>
                  </>
                )}

                <hr className="my-2 border-[var(--shadow-grey)]" />
                <button
                  onClick={() => {
                    signOut()
                    setIsUserDropdownOpen(false)
                  }}
                  className="flex items-center w-full px-4 py-2 text-sm text-[var(--soft-white)] hover:bg-[var(--error)]/10 hover:text-[var(--error)] transition-colors duration-300"
                >
                  <LogOut className="h-4 w-4 mr-3" />
                  Sign Out
                </button>
              </div>
            </div>
          )}
        </div>
      )
    }

    // Dynamic buttons based on current page for non-logged-in users
    switch (pathname) {
      case '/products':
      case '/about':
      case '/services':
      case '/contact':
      case '/become-dealer':
        return (
          <Link href="/auth/signin" className="btn-secondary text-sm px-6 py-2.5">
            Sign In
          </Link>
        )
      case '/auth/signin':
        return (
          <Link href="/auth/signup" className="btn-primary text-sm px-6 py-2.5">
            Sign Up
          </Link>
        )
      case '/auth/signup':
        return (
          <Link href="/auth/signin" className="btn-secondary text-sm px-6 py-2.5">
            Sign In
          </Link>
        )
      default:
        return (
          <Link href="/products" className="btn-primary text-sm px-6 py-2.5">
            Shop Now
          </Link>
        )
    }
  }

  return (
    <div className="sticky top-0 z-50">
      <header className="bg-[var(--deep-night-blue)]/95 backdrop-blur-md border-b border-[var(--shadow-grey)]/50 shadow-professional">
        <nav className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8" aria-label="Top">
          <div className="flex w-full items-center justify-between h-[82px]">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-3 group">
                <img
                  src="/Images/Tisha_Logo.png"
                  alt="Tisha Logo"
                  className="h-10 w-auto group-hover:scale-110 transition-transform duration-300"
                />
                <div className="text-4xl font-bold font-['Poppins'] text-gradient group-hover:animate-pulse-glow transition-all duration-300">
                  TISHA
                </div>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <div className="hidden lg:flex lg:items-center lg:space-x-12">
              {/* Home Button */}
              <Link
                href="/"
                className={cn(
                  'text-lg font-semibold transition-all duration-300 hover:text-[var(--accent-secondary)] relative group',
                  pathname === '/'
                    ? 'text-[var(--accent-secondary)]'
                    : 'text-[var(--soft-white)]'
                )}
              >
                Home
                <span className={cn(
                  'absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-primary transition-all duration-300 group-hover:w-full',
                  pathname === '/' ? 'w-full' : ''
                )} />
              </Link>

              {/* Products Button */}
              <Link
                href="/products"
                className={cn(
                  'text-lg font-semibold transition-all duration-300 hover:text-[var(--accent-secondary)] relative group',
                  pathname === '/products'
                    ? 'text-[var(--accent-secondary)]'
                    : 'text-[var(--soft-white)]'
                )}
              >
                Products
                <span className={cn(
                  'absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-primary transition-all duration-300 group-hover:w-full',
                  pathname === '/products' ? 'w-full' : ''
                )} />
              </Link>

              {/* More Dropdown */}
              <div className="relative" ref={moreDropdownRef}>
                <button
                  onClick={() => setIsMoreDropdownOpen(!isMoreDropdownOpen)}
                  className={cn(
                    'text-lg font-semibold transition-all duration-300 hover:text-[var(--accent-secondary)] relative group flex items-center space-x-1',
                    (pathname === '/become-dealer' || pathname === '/about' || pathname === '/contact' || pathname === '/inquiry')
                      ? 'text-[var(--accent-secondary)]'
                      : 'text-[var(--soft-white)]'
                  )}
                >
                  <span>More</span>
                  <ChevronDown className={cn(
                    "h-4 w-4 transition-transform duration-300",
                    isMoreDropdownOpen && "rotate-180"
                  )} />
                  <span className={cn(
                    'absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-primary transition-all duration-300 group-hover:w-full',
                    (pathname === '/become-dealer' || pathname === '/about' || pathname === '/contact' || pathname === '/inquiry') ? 'w-full' : ''
                  )} />
                </button>

                {/* Dropdown Menu - Same style as user dropdown */}
                {isMounted && isMoreDropdownOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-[var(--background-secondary)] border border-[var(--shadow-grey)] rounded-lg shadow-professional z-50 animate-fade-in">
                    <div className="py-2">
                      {moreNavigation.map((item) => (
                        <Link
                          key={item.name}
                          href={item.href}
                          className="flex items-center px-4 py-2 text-sm text-[var(--soft-white)] hover:bg-[var(--accent-primary)]/10 hover:text-[var(--accent-secondary)] transition-colors duration-300"
                          onClick={() => setIsMoreDropdownOpen(false)}
                        >
                          {item.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Right side actions */}
            <div className="hidden lg:flex lg:items-center lg:space-x-6">
              {/* Show search only on products page */}
              {pathname === '/products' && (
                <button className="p-2 text-[var(--soft-white)] hover:text-[var(--accent-secondary)] transition-all duration-300 hover:bg-[var(--accent-primary)]/10 rounded-lg group">
                  <Search className="h-5 w-5 group-hover:scale-110 transition-transform duration-300" />
                  <span className="sr-only">Search</span>
                </button>
              )}

              {/* Dynamic Button */}
              {getDynamicButton()}
            </div>

            {/* Mobile menu button */}
            <div className="flex lg:hidden">
              <button
                type="button"
                className="p-2 text-[var(--soft-white)] hover:text-[var(--accent-secondary)] transition-all duration-300 hover:bg-[var(--accent-primary)]/10 rounded-lg"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                <span className="sr-only">Open main menu</span>
                {isMenuOpen ? (
                  <X className="h-6 w-6" aria-hidden="true" />
                ) : (
                  <Menu className="h-6 w-6" aria-hidden="true" />
                )}
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMounted && isMenuOpen && (
            <div className="lg:hidden animate-slide-up">
              <div className="border-t border-[var(--shadow-grey)]/50 pt-4 pb-3 space-y-1">
                {/* Main Navigation */}
                {mainNavigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className={cn(
                      'block px-3 py-2 text-base font-medium transition-all duration-300 rounded-lg mx-2',
                      pathname === item.href
                        ? 'text-[var(--accent-secondary)] bg-gradient-card'
                        : 'text-[var(--soft-white)] hover:text-[var(--accent-secondary)] hover:bg-gradient-card'
                    )}
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}

                {/* More Navigation */}
                <div className="mx-2 mt-2">
                  <div className="text-[var(--muted-grey)] text-sm font-medium px-3 py-2 uppercase tracking-wider">
                    More
                  </div>
                  {moreNavigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        'block px-3 py-2 text-base font-medium transition-all duration-300 rounded-lg ml-4',
                        pathname === item.href
                          ? 'text-[var(--accent-secondary)] bg-gradient-card'
                          : 'text-[var(--soft-white)] hover:text-[var(--accent-secondary)] hover:bg-gradient-card'
                      )}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>

                {/* Mobile actions */}
                <div className="border-t border-[var(--shadow-grey)]/50 pt-4 space-y-3 mx-2">
                  {/* Show search only on products page */}
                  {pathname === '/products' && (
                    <Link
                      href="/search"
                      className="flex items-center px-3 py-2 text-[var(--soft-white)] hover:text-[var(--accent-secondary)] hover:bg-gradient-card rounded-lg transition-all duration-300"
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <Search className="h-5 w-5 mr-3" />
                      Search Products
                    </Link>
                  )}

                  {/* Show user info if logged in */}
                  {user && (
                    <div className="flex items-center px-3 py-2 space-x-3">
                      <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
                        <User className="h-4 w-4 text-white" />
                      </div>
                      <span className="text-[var(--soft-white)] text-sm font-medium">
                        {user.user_metadata?.full_name || user.email?.split('@')[0] || 'User'}
                      </span>
                    </div>
                  )}

                  {/* Dynamic mobile button */}
                  <div className="px-3 pt-2">
                    {pathname === '/' ? (
                      <Link
                        href="/products"
                        className="btn-primary w-full justify-center"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Shop Now
                      </Link>
                    ) : !user && pathname === '/auth/signin' ? (
                      <Link
                        href="/auth/signup"
                        className="btn-primary w-full justify-center"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Sign Up
                      </Link>
                    ) : !user && pathname === '/auth/signup' ? (
                      <Link
                        href="/auth/signin"
                        className="btn-secondary w-full justify-center"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Sign In
                      </Link>
                    ) : !user ? (
                      <Link
                        href="/auth/signin"
                        className="btn-secondary w-full justify-center"
                        onClick={() => setIsMenuOpen(false)}
                      >
                        Sign In
                      </Link>
                    ) : null}
                  </div>
                </div>
              </div>
            </div>
          )}
        </nav>
      </header>


    </div>
  )
}