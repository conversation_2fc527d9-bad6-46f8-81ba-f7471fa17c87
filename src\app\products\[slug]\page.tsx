import { Metadata } from 'next';
import { createSupabaseAdminClient } from '@/lib/supabase/server';
import ProductPageClient from './ProductPageClient';

interface PageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Fetch product data for metadata generation
async function getProduct(slug: string) {
  const supabase = createSupabaseAdminClient();

  const { data: product, error } = await supabase
    .from('products')
    .select(`
      *,
      categories (
        id,
        name,
        slug
      ),
      brands (
        id,
        name,
        slug
      ),
      conditions (
        id,
        name,
        slug
      )
    `)
    .eq('slug', slug)
    .in('status', ['active', 'private'])
    .single();

  if (error || !product) {
    return null;
  }

  return product;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params;
  const product = await getProduct(slug);

  if (!product) {
    return {
      title: 'Product Not Found | Tisha International',
      description: 'The requested product could not be found.',
    };
  }

  const title = `${product.name} - ${product.brands?.name || product.brand} | Tisha International`;
  const description = product.description ||
    `${product.name} by ${product.brands?.name || product.brand}. ${product.model ? `Model: ${product.model}. ` : ''}Premium refurbished ${product.categories?.name?.toLowerCase() || 'computer'} with warranty. Contact for best prices.`;

  const keywords = [
    product.name,
    product.brands?.name || product.brand,
    product.model,
    product.categories?.name,
    'refurbished',
    'laptop',
    'desktop',
    'computer',
    'Tisha International'
  ].filter(Boolean).join(', ');

  const imageUrl = product.images?.[0] || '/api/placeholder-image';
  const productUrl = `https://tishainternational.com/products/${product.slug}`;

  const metadata: Metadata = {
    title,
    description,
    keywords,
    authors: [{ name: 'Tisha International' }],
    creator: 'Tisha International',
    publisher: 'Tisha International',
    openGraph: {
      type: 'website',
      locale: 'en_US',
      url: productUrl,
      siteName: 'Tisha International',
      title,
      description,
      images: [
        {
          url: imageUrl,
          width: 800,
          height: 600,
          alt: product.name,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [imageUrl],
    },
    alternates: {
      canonical: productUrl,
    },
  };

  // Add structured data for products with prices
  if (product.show_price && product.price) {
    const structuredData = {
      "@context": "https://schema.org/",
      "@type": "Product",
      "name": product.name,
      "image": imageUrl,
      "description": description,
      "brand": {
        "@type": "Brand",
        "name": product.brands?.name || product.brand
      },
      "model": product.model,
      "offers": {
        "@type": "Offer",
        "url": productUrl,
        "priceCurrency": "INR",
        "price": product.price,
        "availability": product.is_active && product.status === 'active' && (product.stock_quantity || 0) > 0
          ? "https://schema.org/InStock"
          : "https://schema.org/OutOfStock",
        "seller": {
          "@type": "Organization",
          "name": "Tisha International"
        }
      },
      "category": product.categories?.name
    };

    metadata.other = {
      'script:ld+json': JSON.stringify(structuredData)
    };
  }

  return metadata;
}

export default async function ProductPage({ params }: PageProps) {
  return <ProductPageClient />;
}
