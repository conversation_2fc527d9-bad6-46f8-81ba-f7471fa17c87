-- Migration: Update products table model field
-- This migration ensures the products table has the correct model field name
-- and updates the database structure to match the new admin interface

-- Check if model_name column exists and rename it to model
DO $$
BEGIN
    -- Check if model_name column exists and model doesn't
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'model_name'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'model'
    ) THEN
        -- Rename model_name to model
        ALTER TABLE products RENAME COLUMN model_name TO model;
    END IF;
    
    -- If both exist, copy data from model_name to model and drop model_name
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'model_name'
    ) AND EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'model'
    ) THEN
        -- Update model column with model_name data where model is empty
        UPDATE products 
        SET model = model_name 
        WHERE (model IS NULL OR model = '') AND model_name IS NOT NULL AND model_name != '';
        
        -- Drop the old model_name column
        ALTER TABLE products DROP COLUMN model_name;
    END IF;
    
    -- If neither exists, add model column
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'model'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'model_name'
    ) THEN
        ALTER TABLE products ADD COLUMN model VARCHAR(100);
    END IF;
END $$;

-- Ensure model column is not null for existing products
UPDATE products 
SET model = 'Unknown Model' 
WHERE model IS NULL OR model = '';

-- Make model column NOT NULL
ALTER TABLE products ALTER COLUMN model SET NOT NULL;

-- Add specifications column if it doesn't exist (for the new specification system)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'specifications'
    ) THEN
        ALTER TABLE products ADD COLUMN specifications JSONB DEFAULT '{}';
    END IF;
END $$;

-- Migrate existing specification data to the new specifications JSONB column
UPDATE products 
SET specifications = jsonb_build_object(
    'CPU', COALESCE(NULLIF(trim(operating_system), ''), NULL),
    'RAM', COALESCE(NULLIF(trim(ram), ''), NULL),
    'Storage', COALESCE(NULLIF(trim(storage), ''), NULL),
    'OS', COALESCE(NULLIF(trim(operating_system), ''), NULL),
    'Screen Size', COALESCE(NULLIF(trim(screen_size), ''), NULL),
    'Color', COALESCE(NULLIF(trim(color), ''), NULL),
    'GPU', COALESCE(NULLIF(trim(graphics_card), ''), NULL)
) - '{}'::jsonb -- Remove null values
WHERE specifications = '{}' OR specifications IS NULL;

-- Add status column if it doesn't exist (for the new status system)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' 
        AND column_name = 'status'
    ) THEN
        ALTER TABLE products ADD COLUMN status VARCHAR(20) DEFAULT 'active';
    END IF;
END $$;

-- Set status based on is_active for existing products
UPDATE products 
SET status = CASE 
    WHEN is_active = true THEN 'active'
    ELSE 'deactivated'
END
WHERE status IS NULL OR status = '';

-- Create index for the new model column
CREATE INDEX IF NOT EXISTS idx_products_model ON products(model);

-- Create index for specifications JSONB column
CREATE INDEX IF NOT EXISTS idx_products_specifications ON products USING GIN (specifications);

-- Create index for status column
CREATE INDEX IF NOT EXISTS idx_products_status ON products(status);
