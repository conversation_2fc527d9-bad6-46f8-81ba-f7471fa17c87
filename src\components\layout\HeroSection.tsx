'use client'

import Link from 'next/link'
import { ArrowRight, Shield, Award, Truck } from 'lucide-react'
import { motion } from 'framer-motion'

const features = [
  {
    icon: Shield,
    title: 'Quality Guaranteed',
    description: 'Every device undergoes rigorous testing and comes with warranty'
  },
  {
    icon: Award,
    title: 'Premium Brands',
    description: 'Top-tier laptops and desktops from trusted manufacturers'
  },
  {
    icon: Truck,
    title: 'Fast Delivery',
    description: 'Quick and secure shipping to your doorstep'
  }
]

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
}

export function HeroSection() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-[var(--background-primary)] via-[var(--background-secondary)] to-[var(--background-card)]" />
      
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-[var(--primary-gradient-light)]/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-[var(--primary-gradient-dark)]/10 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      <div className="relative z-10 mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="text-center"
        >
          {/* Main heading */}
          <motion.h1 
            variants={itemVariants}
            className="text-4xl sm:text-6xl lg:text-7xl font-bold font-['Poppins'] tracking-tight"
          >
            <span className="block text-white mb-2">Premium</span>
            <span className="block text-gradient mb-4">Refurbished</span>
            <span className="block text-white">Computers</span>
          </motion.h1>

          {/* Subtitle */}
          <motion.p 
            variants={itemVariants}
            className="mt-6 max-w-2xl mx-auto text-xl sm:text-2xl text-gray-300 leading-relaxed"
          >
            Discover high-quality refurbished laptops and desktops at unbeatable prices. 
            Your trusted partner for reliable technology solutions.
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            variants={itemVariants}
            className="mt-10 flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Link
              href="/products"
              className="btn-primary text-lg px-8 py-4 group hover:shadow-2xl hover:shadow-terra-cotta-rose/30"
            >
              Shop Now
              <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-2 group-hover:scale-110" />
            </Link>

            <Link
              href="/about"
              className="btn-secondary text-lg px-8 py-4 hover:shadow-2xl hover:shadow-terra-cotta-rose/30"
            >
              Learn More
            </Link>
          </motion.div>

          {/* Features */}
          <motion.div 
            variants={itemVariants}
            className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
          >
            {features.map((feature) => {
              const Icon = feature.icon
              return (
                <motion.div
                  key={feature.title}
                  variants={itemVariants}
                  className="card text-center group"
                >
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-gradient-primary rounded-xl group-hover:scale-110 transition-transform duration-300">
                      <Icon className="h-8 w-8 text-white" />
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2 font-['Poppins']">
                    {feature.title}
                  </h3>
                  <p className="text-gray-300 leading-relaxed">
                    {feature.description}
                  </p>
                </motion.div>
              )
            })}
          </motion.div>

          {/* Stats */}
          <motion.div 
            variants={itemVariants}
            className="mt-20 grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto"
          >
            <div className="text-center">
              <div className="text-3xl font-bold text-gradient font-['Poppins']">5000+</div>
              <div className="text-sm text-gray-400 mt-1">Happy Customers</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gradient font-['Poppins']">10,000+</div>
              <div className="text-sm text-gray-400 mt-1">Devices Sold</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gradient font-['Poppins']">99%</div>
              <div className="text-sm text-gray-400 mt-1">Satisfaction Rate</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gradient font-['Poppins']">24/7</div>
              <div className="text-sm text-gray-400 mt-1">Support</div>
            </div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 1, duration: 0.6 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
      >
        <div className="flex flex-col items-center text-gray-400">
          <span className="text-sm mb-2">Scroll to explore</span>
          <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-bounce" />
          </div>
        </div>
      </motion.div>
    </section>
  )
}
