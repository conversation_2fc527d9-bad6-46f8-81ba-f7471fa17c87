import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { auto_play_interval } = body;

    if (!auto_play_interval || auto_play_interval < 1000 || auto_play_interval > 30000) {
      return NextResponse.json({ 
        error: 'Auto play interval must be between 1000 and 30000 milliseconds' 
      }, { status: 400 });
    }

    // Check if settings exist
    const { data: existingSettings } = await supabase
      .from('home_carousel_settings')
      .select('id')
      .single();

    let result;
    if (existingSettings) {
      // Update existing settings
      result = await supabase
        .from('home_carousel_settings')
        .update({ auto_play_interval })
        .eq('id', existingSettings.id)
        .select()
        .single();
    } else {
      // Insert new settings
      result = await supabase
        .from('home_carousel_settings')
        .insert({ auto_play_interval })
        .select()
        .single();
    }

    if (result.error) {
      console.error('Error updating carousel settings:', result.error);
      return NextResponse.json({ error: 'Failed to update carousel settings' }, { status: 500 });
    }

    return NextResponse.json({ settings: result.data });

  } catch (error) {
    console.error('Error in carousel settings API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
