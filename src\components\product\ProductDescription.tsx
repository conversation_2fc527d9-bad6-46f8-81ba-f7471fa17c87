'use client';

import { useState } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { Card } from '@/components/ui/card';

interface ProductDescriptionProps {
  description: string;
  detailedPoints?: string[];
}

export function ProductDescription({ description, detailedPoints }: ProductDescriptionProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <Card className="p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-xl font-bold text-white">Description</h3>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-2 text-primary-gradient-light hover:text-primary-gradient-dark transition-colors"
        >
          <span className="text-sm font-medium">
            {isExpanded ? 'Show Less' : 'Show More'}
          </span>
          {isExpanded ? (
            <ChevronUp className="h-4 w-4" />
          ) : (
            <ChevronDown className="h-4 w-4" />
          )}
        </button>
      </div>

      <div className="space-y-4">
        <p className="text-gray-300 leading-relaxed">
          {description}
        </p>

        {isExpanded && detailedPoints && detailedPoints.length > 0 && (
          <div className="mt-6 space-y-3 animate-slide-up">
            <h4 className="text-lg font-semibold text-white">Detailed Features</h4>
            <ul className="space-y-2">
              {detailedPoints.map((point, index) => (
                <li key={index} className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-gradient-to-r from-primary-gradient-dark to-primary-gradient-light rounded-full mt-2 flex-shrink-0"></div>
                  <span className="text-gray-300 text-sm leading-relaxed">{point}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </Card>
  );
}
