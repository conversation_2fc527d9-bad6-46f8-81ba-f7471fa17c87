import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/admin';

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseAdminClient();
    
    // Fetch all brands
    const { data: brands, error } = await supabase
      .from('brands')
      .select('id, name, slug')
      .order('name', { ascending: true });

    if (error) {
      console.error('Error fetching brands:', error);
      return NextResponse.json({ error: 'Failed to fetch brands' }, { status: 500 });
    }

    return NextResponse.json({
      brands: brands || [],
      total: brands?.length || 0
    });
  } catch (error) {
    console.error('Error in brands API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
