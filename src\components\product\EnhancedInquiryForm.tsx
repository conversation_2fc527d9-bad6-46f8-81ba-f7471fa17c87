'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useInquiries } from '@/hooks/useInquiries';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatPrice } from '@/lib/product-utils';
import { 
  CheckCircle,
  AlertCircle,
  Minus,
  Plus,
  ShoppingBag,
  MessageCircle,
  Loader2,
  UserPlus,
  Phone
} from 'lucide-react';

interface Product {
  id: string;
  name: string;
  brand: string;
  model?: string;
  price: number;
  images: string[];
  stock_quantity?: number;
  is_active?: boolean;
}

interface InquiryFormProps {
  product: Product;
}

export function EnhancedInquiryForm({ product }: InquiryFormProps) {
  const { user, loading: authLoading } = useAuth();
  const { createInquiry, loading: submitting } = useInquiries();
  const router = useRouter();

  const [inquiryType, setInquiryType] = useState<'regular' | 'bulk'>('regular');
  const [quantity, setQuantity] = useState(1);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    whatsappNumber: '',
    address: '',
    message: ''
  });
  // Auto-fill form data for authenticated users
  useEffect(() => {
    if (user && !authLoading) {
      setFormData(prev => ({
        ...prev,
        fullName: (user as { full_name?: string }).full_name || '',
        email: user.email || '',
        phone: (user as { phone_number?: string }).phone_number || '',
        address: (user as { address?: string }).address || ''
      }));
    }
  }, [user, authLoading]);

  const isInStock = (product.stock_quantity && product.stock_quantity > 0) || false;
  const maxQuantity = product.stock_quantity || 100;

  const handleQuantityChange = (change: number) => {
    const newQuantity = quantity + change;
    if (newQuantity >= 1 && newQuantity <= maxQuantity) {
      setQuantity(newQuantity);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setSubmitError(null); // Clear error when user starts typing
  };

  const validateForm = () => {
    const errors: string[] = [];
    
    if (!formData.fullName.trim()) errors.push('Full name is required');
    if (!formData.email.trim()) errors.push('Email is required');
    if (!formData.phone.trim()) errors.push('Phone number is required');
    if (!formData.address.trim()) errors.push('Address is required');
    
    if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.push('Please enter a valid email address');
    }
    
    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitError(null);

    const errors = validateForm();
    if (errors.length > 0) {
      setSubmitError(errors.join(', '));
      return;
    }

    try {
      await createInquiry({
        productId: product.id,
        inquiryType,
        quantity,
        userDetails: {
          fullName: formData.fullName,
          email: formData.email,
          phone: formData.phone,
          address: formData.address
        }
      });

      setIsSubmitted(true);
      
      // Redirect to orders page after 3 seconds
      setTimeout(() => {
        if (user) {
          router.push('/user/orders');
        } else {
          router.push('/auth/signup?redirect=/user/orders');
        }
      }, 3000);

    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : 'Failed to submit inquiry');
    }
  };

  const totalPrice = product.price * quantity;

  // Success state
  if (isSubmitted) {
    return (
      <Card className="p-6 sticky top-24">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto">
            <CheckCircle className="h-8 w-8 text-green-400" />
          </div>
          <h3 className="text-xl font-semibold text-white">Inquiry Submitted!</h3>          <p className="text-gray-300">
            Thank you for your inquiry. We&apos;ll get back to you soon with more details.
          </p>
          <div className="pt-4">
            <Button
              onClick={() => router.push(user ? '/user/orders' : '/auth/signup')}
              className="w-full"
            >
              {user ? 'View Orders' : 'Create Account'}
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6 sticky top-24">
      <div className="space-y-6">
        {/* Authentication Status */}
        {!user && !authLoading && (
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <UserPlus className="h-5 w-5 text-blue-400 mt-0.5" />
              <div>
                <p className="text-sm text-blue-300 font-medium">Sign up for faster checkout</p>
                <p className="text-xs text-blue-200 mt-1">
                  Create an account to track your orders and save your information
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2 text-xs"
                  onClick={() => router.push('/auth/signup?redirect=' + encodeURIComponent(window.location.pathname))}
                >
                  Create Account
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Price and Stock Info */}
        <div>
          <div className="flex items-center gap-3 mb-2">
            <span className="text-3xl font-bold text-white">
              {formatPrice(product.price)}
            </span>
            <Badge variant={isInStock ? 'default' : 'outline'}>
              {isInStock ? 'In Stock' : 'Out of Stock'}
            </Badge>
          </div>
          <p className="text-gray-400 text-sm">
            {isInStock 
              ? `${product.stock_quantity || 0} units available` 
              : 'Currently unavailable'
            }
          </p>
        </div>

        {/* Inquiry Type Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-3">
            Inquiry Type
          </label>
          <div className="grid grid-cols-2 gap-2">
            <button
              type="button"
              onClick={() => setInquiryType('regular')}
              className={`p-3 text-sm font-medium rounded-lg border transition-colors ${
                inquiryType === 'regular'
                  ? 'bg-gradient-to-r from-[#956358] to-[#b87568] text-white border-transparent'
                  : 'bg-[#1a2b4a] text-gray-300 border-gray-600 hover:border-gray-500'
              }`}
            >
              Regular Order
            </button>
            <button
              type="button"
              onClick={() => setInquiryType('bulk')}
              className={`p-3 text-sm font-medium rounded-lg border transition-colors ${
                inquiryType === 'bulk'
                  ? 'bg-gradient-to-r from-[#956358] to-[#b87568] text-white border-transparent'
                  : 'bg-[#1a2b4a] text-gray-300 border-gray-600 hover:border-gray-500'
              }`}
            >
              Bulk Order
            </button>
          </div>
        </div>

        {/* Quantity Selection */}
        {isInStock && (
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-3">
              Quantity
            </label>
            <div className="flex items-center gap-3">
              <button
                type="button"
                onClick={() => handleQuantityChange(-1)}
                disabled={quantity <= 1}
                className="w-10 h-10 bg-[#1a2b4a] border border-gray-600 rounded-lg flex items-center justify-center text-gray-300 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Minus className="h-4 w-4" />
              </button>
              <span className="text-xl font-semibold text-white w-16 text-center">
                {quantity}
              </span>
              <button
                type="button"
                onClick={() => handleQuantityChange(1)}
                disabled={quantity >= maxQuantity}
                className="w-10 h-10 bg-[#1a2b4a] border border-gray-600 rounded-lg flex items-center justify-center text-gray-300 hover:text-white disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <Plus className="h-4 w-4" />
              </button>
            </div>
            {quantity > 1 && (
              <p className="text-sm text-gray-400 mt-2">
                Total: <span className="text-white font-semibold">{formatPrice(totalPrice)}</span>
              </p>
            )}
          </div>
        )}

        {/* Contact Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input
            label="Full Name *"
            type="text"
            required
            value={formData.fullName}
            onChange={(e) => handleInputChange('fullName', e.target.value)}
            placeholder="Enter your full name"
            disabled={submitting}
          />

          <Input
            label="Email Address *"
            type="email"
            required
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="Enter your email"
            disabled={submitting}
          />

          <Input
            label="Phone Number *"
            type="tel"
            required
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            placeholder="Enter your phone number"
            disabled={submitting}
          />

          <Input
            label="WhatsApp Number"
            type="tel"
            value={formData.whatsappNumber}
            onChange={(e) => handleInputChange('whatsappNumber', e.target.value)}
            placeholder="Enter your WhatsApp number"
            disabled={submitting}
          />

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Delivery Address *
            </label>
            <textarea
              required
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              placeholder="Enter your complete address"
              rows={3}
              disabled={submitting}
              className="w-full px-4 py-3 bg-[#1a2b4a] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent resize-vertical disabled:opacity-50"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Additional Message
            </label>
            <textarea
              value={formData.message}
              onChange={(e) => handleInputChange('message', e.target.value)}
              placeholder="Any special requirements or questions..."
              rows={3}
              disabled={submitting}
              className="w-full px-4 py-3 bg-[#1a2b4a] border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#956358] focus:border-transparent resize-vertical disabled:opacity-50"
            />
          </div>

          {/* Error Display */}
          {submitError && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-red-400" />
                <p className="text-sm text-red-300">{submitError}</p>
              </div>
            </div>
          )}

          <Button
            type="submit"
            className="w-full bg-gradient-to-r from-[#956358] to-[#b87568] hover:from-[#8a5a4f] hover:to-[#a56c5c] text-white"
            disabled={!isInStock || submitting || authLoading}
          >
            {submitting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Submitting...
              </>
            ) : (
              <>
                <ShoppingBag className="h-4 w-4 mr-2" />
                Submit Inquiry
              </>
            )}
          </Button>
        </form>

        {/* Quick Contact Options */}
        <div className="border-t border-gray-600 pt-6 space-y-3">
          <h4 className="text-sm font-semibold text-white">Quick Contact</h4>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="flex-1"
              onClick={() => window.open('https://wa.me/1234567890', '_blank')}
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              WhatsApp
            </Button>
            <Button 
              variant="outline" 
              size="sm" 
              className="flex-1"
              onClick={() => window.open('tel:+1234567890', '_self')}
            >
              <Phone className="h-4 w-4 mr-2" />
              Call Now
            </Button>
          </div>
          <p className="text-xs text-gray-400 text-center">
            Get instant support for your inquiries
          </p>
        </div>
      </div>
    </Card>
  );
}
