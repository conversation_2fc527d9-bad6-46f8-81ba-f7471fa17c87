import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const supabase = createSupabaseAdminClient();

    // Test 1: Check if we can connect
    console.log('Testing Supabase admin connection...');

    // Test 2: Check users table structure
    const { data: tableInfo, error: tableError } = await supabase
      .from('users')
      .select('*')
      .limit(1);

    console.log('Users table test:', { tableInfo, tableError });

    // Test 3: Try to insert a test user (we'll delete it after)
    const testUser = {
      email: '<EMAIL>',
      password_hash: 'test',
      full_name: 'Test User',
      phone: null,
      address: null,
      role: 'user' as const
    };

    const { data: insertData, error: insertError } = await supabase
      .from('users')
      .insert(testUser)
      .select();

    console.log('Insert test:', { insertData, insertError });

    // Clean up test user if insert was successful
    if (insertData && insertData.length > 0) {
      await supabase
        .from('users')
        .delete()
        .eq('id', insertData[0].id);
      console.log('Test user cleaned up');
    }

    return NextResponse.json({
      success: true,
      tests: {
        connection: !tableError,
        tableAccess: { data: tableInfo, error: tableError },
        insertTest: { data: insertData, error: insertError }
      }
    });

  } catch (error) {
    console.error('Supabase admin test error:', error);
    return NextResponse.json(
      { error: 'Test failed', details: error },
      { status: 500 }
    );
  }
}
