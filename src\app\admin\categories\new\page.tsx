'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Save, Upload, Image as ImageIcon } from 'lucide-react';

export default function NewCategoryPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    image_url: '',
    is_active: true
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      alert('Category name is required');
      return;
    }

    try {
      setIsLoading(true);

      const response = await fetch('/api/admin/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        alert('Category created successfully!');
        router.push('/admin/categories');
      } else {
        const error = await response.json();
        alert(`Error: ${error.error}`);
      }
    } catch (error) {
      console.error('Error creating category:', error);
      alert('Failed to create category. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4 border-b border-[var(--shadow-grey)] pb-4">
        <button
          onClick={() => router.back()}
          className="p-2 hover:bg-[var(--shadow-grey)] rounded-md transition-colors"
        >
          <ArrowLeft className="h-5 w-5 text-[var(--text-secondary)]" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-[var(--text-primary)]">Add New Category</h1>
          <p className="text-[var(--text-secondary)] mt-1">Create a new category for your products</p>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
          <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-6">Category Information</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Category Name */}
            <div className="md:col-span-2">
              <label htmlFor="name" className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                Category Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="w-full px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
                placeholder="Enter category name"
              />
            </div>

            {/* Description */}
            <div className="md:col-span-2">
              <label htmlFor="description" className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                Description (Optional)
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
                placeholder="Enter category description"
              />
            </div>

            {/* Image URL */}
            <div className="md:col-span-2">
              <label htmlFor="image_url" className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                Category Image URL (Optional)
              </label>
              <input
                type="url"
                id="image_url"
                name="image_url"
                value={formData.image_url}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
                placeholder="https://example.com/category-image.jpg"
              />
              <p className="mt-1 text-xs text-[var(--text-secondary)]">
                Enter the URL of the category image for the home page showcase
              </p>

              {/* Image Preview */}
              {formData.image_url && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                    Preview
                  </label>
                  <div className="w-32 h-32 bg-[var(--shadow-grey)] rounded-lg overflow-hidden border border-[var(--shadow-grey)]">
                    <img
                      src={formData.image_url}
                      alt="Category preview"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                        const errorDiv = target.nextElementSibling as HTMLElement;
                        if (errorDiv) errorDiv.classList.remove('hidden');
                      }}
                    />
                    <div className="hidden w-full h-full flex items-center justify-center text-[var(--text-secondary)]">
                      <div className="text-center">
                        <ImageIcon className="h-8 w-8 mx-auto mb-2" />
                        <span className="text-xs">Invalid Image URL</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {!formData.image_url && (
                <div className="mt-4">
                  <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                    Preview
                  </label>
                  <div className="w-32 h-32 bg-[var(--shadow-grey)] rounded-lg flex items-center justify-center border border-[var(--shadow-grey)]">
                    <div className="text-center text-[var(--text-secondary)]">
                      <Upload className="h-8 w-8 mx-auto mb-2" />
                      <span className="text-xs">No Image</span>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Status */}
            <div className="md:col-span-2">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-[var(--accent-primary)] bg-[var(--background-primary)] border-[var(--shadow-grey)] rounded focus:ring-[var(--accent-primary)] focus:ring-2"
                />
                <span className="text-sm font-medium text-[var(--text-primary)]">
                  Active Category
                </span>
              </label>
              <p className="text-xs text-[var(--text-secondary)] mt-1 ml-7">
                Only active categories will be visible to customers and available for product assignment
              </p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-6 py-2 border border-[var(--shadow-grey)] text-[var(--text-secondary)] rounded-md hover:bg-[var(--shadow-grey)] transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isLoading || !formData.name.trim()}
            className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating...</span>
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                <span>Create Category</span>
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
