-- =====================================================
-- Remove Product Images Script
-- =====================================================
-- This script removes all image links from the products table
-- Run this script carefully as it will affect all products

-- Step 1: Create a backup table (RECOMMENDED)
-- This creates a backup of all current image data before removal
CREATE TABLE IF NOT EXISTS products_images_backup AS
SELECT 
    id,
    name,
    images,
    created_at as backup_created_at,
    NOW() as backup_timestamp
FROM products 
WHERE images IS NOT NULL 
AND jsonb_array_length(images) > 0;

-- Verify backup was created
SELECT 
    COUNT(*) as total_products_backed_up,
    SUM(jsonb_array_length(images)) as total_images_backed_up
FROM products_images_backup;

-- Step 2: Show current state (for verification)
SELECT 
    COUNT(*) as total_products,
    COUNT(CASE WHEN images IS NOT NULL AND jsonb_array_length(images) > 0 THEN 1 END) as products_with_images,
    SUM(CASE WHEN images IS NOT NULL THEN jsonb_array_length(images) ELSE 0 END) as total_image_links
FROM products;

-- Step 3: Remove all image links from products table
-- This sets the images field to an empty JSON array for all products
UPDATE products 
SET 
    images = '[]'::jsonb,
    updated_at = NOW()
WHERE images IS NOT NULL 
AND jsonb_array_length(images) > 0;

-- Step 4: Verify the removal
SELECT 
    COUNT(*) as total_products,
    COUNT(CASE WHEN images IS NOT NULL AND jsonb_array_length(images) > 0 THEN 1 END) as products_with_images_after_removal,
    SUM(CASE WHEN images IS NOT NULL THEN jsonb_array_length(images) ELSE 0 END) as total_image_links_after_removal
FROM products;

-- Step 5: Show the backup table for reference
SELECT 
    'Backup table created with ' || COUNT(*) || ' products and ' || 
    SUM(jsonb_array_length(images)) || ' image links' as backup_summary
FROM products_images_backup;

-- =====================================================
-- OPTIONAL: Restore from backup (if needed)
-- =====================================================
-- Uncomment and run this section ONLY if you need to restore the images

/*
-- Restore images from backup
UPDATE products 
SET 
    images = backup.images,
    updated_at = NOW()
FROM products_images_backup backup
WHERE products.id = backup.id;

-- Verify restoration
SELECT 
    COUNT(*) as total_products,
    COUNT(CASE WHEN images IS NOT NULL AND jsonb_array_length(images) > 0 THEN 1 END) as products_with_images_restored,
    SUM(CASE WHEN images IS NOT NULL THEN jsonb_array_length(images) ELSE 0 END) as total_image_links_restored
FROM products;
*/

-- =====================================================
-- CLEANUP: Remove backup table (run only when sure)
-- =====================================================
-- Uncomment this line ONLY when you're absolutely sure you don't need the backup anymore
-- DROP TABLE IF EXISTS products_images_backup;

-- =====================================================
-- NOTES:
-- =====================================================
-- 1. This script only removes image LINKS from the database
-- 2. The actual image FILES in Supabase storage are NOT deleted
-- 3. To delete actual files, use the admin interface or API endpoint
-- 4. Always keep the backup table until you're sure everything is working
-- 5. The backup table can be used to restore images if needed
