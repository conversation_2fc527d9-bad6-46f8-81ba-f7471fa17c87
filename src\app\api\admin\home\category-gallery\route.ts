import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { category_id, product_id, display_order } = body;

    if (!category_id || !product_id) {
      return NextResponse.json({ 
        error: 'Category ID and Product ID are required' 
      }, { status: 400 });
    }

    const { data, error } = await supabase
      .from('home_category_gallery')
      .insert({
        category_id,
        product_id,
        display_order: display_order || 0,
        is_active: true
      })
      .select(`
        id,
        category_id,
        product_id,
        display_order,
        is_active,
        products (
          id,
          name,
          price,
          images,
          brand,
          model,
          show_price
        )
      `)
      .single();

    if (error) {
      console.error('Error creating category gallery item:', error);
      return NextResponse.json({ 
        error: 'Failed to create category gallery item' 
      }, { status: 500 });
    }

    return NextResponse.json({ gallery_item: data });

  } catch (error) {
    console.error('Error in category gallery POST:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('category_id');

    let query = supabase
      .from('home_category_gallery')
      .select(`
        id,
        category_id,
        product_id,
        display_order,
        is_active,
        products (
          id,
          name,
          price,
          images,
          brand,
          model,
          show_price
        )
      `)
      .order('display_order');

    if (categoryId) {
      query = query.eq('category_id', categoryId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching category gallery:', error);
      return NextResponse.json({ 
        error: 'Failed to fetch category gallery' 
      }, { status: 500 });
    }

    return NextResponse.json({ gallery_items: data || [] });

  } catch (error) {
    console.error('Error in category gallery GET:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
