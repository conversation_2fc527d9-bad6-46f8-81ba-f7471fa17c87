-- Migration: Add brands and conditions tables
-- This migration creates separate tables for brands and conditions
-- and updates the products table to reference them

-- Create brands table
CREATE TABLE IF NOT EXISTS brands (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    logo_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create conditions table
CREATE TABLE IF NOT EXISTS conditions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default brands
INSERT INTO brands (name, slug, description) VALUES
('Apple', 'apple', 'Premium technology products'),
('Dell', 'dell', 'Reliable computers and laptops'),
('HP', 'hp', 'Hewlett-Packard computers and printers'),
('Lenovo', 'lenovo', 'ThinkPad and IdeaPad series'),
('ASUS', 'asus', 'Gaming and professional computers'),
('Acer', 'acer', 'Affordable laptops and desktops'),
('MSI', 'msi', 'Gaming computers and components'),
('Samsung', 'samsung', 'Electronics and displays'),
('LG', 'lg', 'Monitors and electronics'),
('Microsoft', 'microsoft', 'Surface devices and software')
ON CONFLICT (name) DO NOTHING;

-- Insert default conditions
INSERT INTO conditions (name, slug, description) VALUES
('New', 'new', 'Brand new, unused items'),
('Like New', 'like-new', 'Excellent condition, minimal use'),
('Very Good', 'very-good', 'Minor signs of use, fully functional'),
('Good', 'good', 'Some wear but works perfectly'),
('Fair', 'fair', 'Noticeable wear but functional'),
('Refurbished', 'refurbished', 'Professionally restored to working condition'),
('Open Box', 'open-box', 'New item with opened packaging')
ON CONFLICT (name) DO NOTHING;

-- Add brand_id and condition_id columns to products table
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS brand_id UUID REFERENCES brands(id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS condition_id UUID REFERENCES conditions(id) ON DELETE SET NULL;

-- Migrate existing brand data to brands table and update products
DO $$
DECLARE
    product_record RECORD;
    brand_id_var UUID;
BEGIN
    -- Loop through products with brand names
    FOR product_record IN 
        SELECT DISTINCT brand FROM products WHERE brand IS NOT NULL AND brand != ''
    LOOP
        -- Insert brand if it doesn't exist and get its ID
        INSERT INTO brands (name, slug, description)
        VALUES (
            product_record.brand,
            lower(regexp_replace(product_record.brand, '[^a-zA-Z0-9\s]', '', 'g')),
            'Migrated from existing product data'
        )
        ON CONFLICT (name) DO NOTHING;
        
        -- Get the brand ID
        SELECT id INTO brand_id_var FROM brands WHERE name = product_record.brand;
        
        -- Update products to use brand_id
        UPDATE products 
        SET brand_id = brand_id_var 
        WHERE brand = product_record.brand;
    END LOOP;
END $$;

-- Set default condition for existing products (New)
UPDATE products 
SET condition_id = (SELECT id FROM conditions WHERE slug = 'new' LIMIT 1)
WHERE condition_id IS NULL;

-- Enable RLS on new tables
ALTER TABLE brands ENABLE ROW LEVEL SECURITY;
ALTER TABLE conditions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for brands and conditions
CREATE POLICY "Anyone can view active brands" ON brands FOR SELECT USING (is_active = true);
CREATE POLICY "Anyone can view active conditions" ON conditions FOR SELECT USING (is_active = true);

-- Admin policies for brands
CREATE POLICY "Admins can manage brands" ON brands FOR ALL USING (
    EXISTS (
        SELECT 1 FROM admin_users 
        WHERE id = auth.uid() 
        AND is_active = true
    )
);

-- Admin policies for conditions  
CREATE POLICY "Admins can manage conditions" ON conditions FOR ALL USING (
    EXISTS (
        SELECT 1 FROM admin_users 
        WHERE id = auth.uid() 
        AND is_active = true
    )
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_brands_slug ON brands(slug);
CREATE INDEX IF NOT EXISTS idx_brands_active ON brands(is_active);
CREATE INDEX IF NOT EXISTS idx_conditions_slug ON conditions(slug);
CREATE INDEX IF NOT EXISTS idx_conditions_active ON conditions(is_active);
CREATE INDEX IF NOT EXISTS idx_products_brand_id ON products(brand_id);
CREATE INDEX IF NOT EXISTS idx_products_condition_id ON products(condition_id);
