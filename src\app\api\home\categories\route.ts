import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET() {
  try {
    // Get featured categories for home page
    const { data: featuredCategories, error: categoriesError } = await supabase
      .from('home_featured_categories')
      .select(`
        id,
        category_id,
        custom_image,
        display_order,
        categories (
          id,
          name,
          slug
        )
      `)
      .eq('is_active', true)
      .order('display_order')
      .limit(3);

    if (categoriesError) {
      console.error('Error fetching featured categories:', categoriesError);
      return NextResponse.json({ error: 'Failed to fetch categories' }, { status: 500 });
    }

    // For each category, get its products
    const categoriesWithProducts = await Promise.all(
      (featuredCategories || []).map(async (featuredCategory) => {
        // Get category products from the gallery
        const { data: galleryProducts } = await supabase
          .from('home_category_gallery')
          .select(`
            product_id,
            display_order,
            products (
              id,
              name,
              price,
              images,
              brand,
              model,
              show_price,
              slug
            )
          `)
          .eq('category_id', featuredCategory.category_id)
          .eq('is_active', true)
          .order('display_order')
          .limit(10);

        const products = galleryProducts?.map(item => ({
          id: item.products.id,
          name: item.products.name,
          price: item.products.price,
          images: item.products.images || [],
          brand: item.products.brand,
          model: item.products.model,
          show_price: item.products.show_price,
          slug: item.products.slug
        })) || [];

        return {
          id: featuredCategory.categories.id,
          name: featuredCategory.categories.name,
          slug: featuredCategory.categories.slug,
          image: featuredCategory.custom_image || '/images/default-category.jpg',
          products
        };
      })
    );

    return NextResponse.json({
      categories: categoriesWithProducts
    });

  } catch (error) {
    console.error('Error in categories API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
