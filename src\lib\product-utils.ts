/**
 * Generate a URL-friendly slug from a product name
 */
export function generateSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/\s+/g, '-')           // Replace spaces with hyphens
    .replace(/[^\w-]/g, '')         // Remove non-word characters except hyphens
    .replace(/--+/g, '-')           // Replace multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, '');       // Remove leading/trailing hyphens
}

/**
 * Generate category slug from category name
 */
export function generateCategorySlug(category: string): string {
  return category.toLowerCase().replace(/\s+/g, '-');
}

/**
 * Format price with currency symbol
 */
export function formatPrice(price: number, currency: string = '₹'): string {
  return `${currency}${price.toLocaleString('en-IN')}`;
}

/**
 * Calculate discount percentage
 */
export function calculateDiscount(originalPrice: number, currentPrice: number): number {
  return Math.round(((originalPrice - currentPrice) / originalPrice) * 100);
}

/**
 * Generate product URL path using product slug
 */
export function getProductUrl(productSlug: string): string {
  return `/products/${productSlug}`;
}

/**
 * Generate product URL path using product ID (for backward compatibility)
 */
export function getProductUrlById(productId: string): string {
  return `/products/${productId}`;
}

/**
 * Legacy function for backward compatibility - now uses slug
 * @deprecated Use getProductUrl(productSlug) instead
 */
export function getProductUrlLegacy(category: string, productName: string): string {
  const categorySlug = generateCategorySlug(category);
  const productSlug = generateSlug(productName);
  return `/products/${categorySlug}/${productSlug}`;
}
