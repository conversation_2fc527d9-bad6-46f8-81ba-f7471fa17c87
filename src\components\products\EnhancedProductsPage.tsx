'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { SpotlightCard } from '@/components/ui/spotlight';
import { Search, Filter, Package, Eye, ShoppingCart, Star, Laptop, Monitor, Smartphone, Tablet, Headphones, Camera, ArrowRight, Grid3X3, List, SlidersHorizontal, Heart, Zap, MessageSquare, Cpu, HardDrive, Monitor as MonitorIcon, Palette } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/contexts/AuthContext';
import { formatPrice } from '@/lib/utils';
import { motion } from 'framer-motion';


interface Product {
  id: string;
  name: string;
  price: number;
  images: string[];
  brand: string;
  model: string;
  show_price?: boolean;
  slug: string;
  status: string;
  is_active: boolean;
  stock_quantity?: number;
  specifications?: Record<string, string>;
  special_features?: string[];
  additional_features?: Record<string, any>;
  categories?: {
    id: string;
    name: string;
    slug: string;
  };
  brands?: {
    id: string;
    name: string;
    slug: string;
  };
  conditions?: {
    id: string;
    name: string;
    slug: string;
  };
}

interface Category {
  id: string;
  name: string;
  slug: string;
  image?: string;
  productCount?: number;
}

const categoryIcons = {
  'laptops': Laptop,
  'desktops': Monitor,
  'smartphones': Smartphone,
  'tablets': Tablet,
  'accessories': Headphones,
  'cameras': Camera,
  'default': Package
};

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

const productCardVariants = {
  hidden: { opacity: 0, scale: 0.9 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
      ease: "easeOut"
    }
  }
};

interface EnhancedProductsPageProps {
  initialProducts?: Product[];
  initialCategories?: Category[];
  isLoading?: boolean;
  isLoadingMore?: boolean;
  hasMoreActive?: boolean;
  showLoadMoreButton?: boolean;
  hasPrivateAccess?: boolean;
  activeProductsCount?: number;
  loadMoreRef?: React.RefObject<HTMLDivElement>;
  onLoadMore?: () => void;
  onShowQueryForm?: () => void;
  searchTerm?: string;
  setSearchTerm?: (term: string) => void;
  selectedCategory?: string;
  setSelectedCategory?: (category: string) => void;
  selectedBrand?: string;
  setSelectedBrand?: (brand: string) => void;
  sortBy?: string;
  setSortBy?: (sort: string) => void;
}

export default function EnhancedProductsPage({
  initialProducts = [],
  initialCategories = [],
  isLoading: externalIsLoading = false,
  isLoadingMore: externalIsLoadingMore = false,
  hasMoreActive = true,
  showLoadMoreButton: externalShowLoadMoreButton = false,
  hasPrivateAccess: externalHasPrivateAccess = false,
  activeProductsCount = 0,
  loadMoreRef,
  onLoadMore,
  onShowQueryForm,
  searchTerm: externalSearchTerm = '',
  setSearchTerm: externalSetSearchTerm,
  selectedCategory: externalSelectedCategory = '',
  setSelectedCategory: externalSetSelectedCategory,
  selectedBrand: externalSelectedBrand = '',
  setSelectedBrand: externalSetSelectedBrand,
  sortBy: externalSortBy = 'name',
  setSortBy: externalSetSortBy
}: EnhancedProductsPageProps) {
  const [products, setProducts] = useState<Product[]>(initialProducts);
  const [categories, setCategories] = useState<Category[]>(initialCategories);
  const [hoveredProductId, setHoveredProductId] = useState<string | null>(null);

  const [searchQuery, setSearchQuery] = useState(externalSearchTerm);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const { user } = useAuth();

  // Use external state when available, fallback to internal state
  const isLoading = externalIsLoading;
  const isLoadingMore = externalIsLoadingMore;
  const showLoadMoreButton = externalShowLoadMoreButton;
  const hasPrivateAccess = externalHasPrivateAccess;
  const selectedCategory = externalSelectedCategory;
  const selectedBrand = externalSelectedBrand;
  const searchTerm = externalSearchTerm;
  const sortBy = externalSortBy;



  // Update products when initialProducts change
  useEffect(() => {
    setProducts(initialProducts);
  }, [initialProducts]);

  // Update categories when initialCategories change
  useEffect(() => {
    setCategories(initialCategories);
  }, [initialCategories]);



  // Update internal search query when external changes
  useEffect(() => {
    setSearchQuery(externalSearchTerm);
  }, [externalSearchTerm]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (externalSetSearchTerm) {
      externalSetSearchTerm(searchQuery);
    }
  };

  // Use products directly since filtering is handled by the API
  const filteredProducts = products;



  return (
    <div className="min-h-screen bg-gradient-to-br from-[var(--background-primary)] via-[var(--background-secondary)] to-[var(--background-card)]">
      {/* Enhanced Hero Section */}
      <section className="relative py-20 px-4 overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-72 h-72 bg-gradient-primary/10 rounded-full blur-3xl animate-pulse-glow" />
          <div className="absolute bottom-20 right-10 w-96 h-96 bg-accent-secondary/10 rounded-full blur-3xl animate-pulse-glow" style={{ animationDelay: '2s' }} />
        </div>

        <div className="max-w-7xl mx-auto relative z-10">
          <motion.div 
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="text-center mb-16"
          >
            <motion.div variants={itemVariants}>
              <Badge className="bg-gradient-primary text-white border-0 mb-6 px-6 py-3 text-lg">
                <Zap className="w-5 h-5 mr-2" />
                Premium Collection
              </Badge>
            </motion.div>
            
            <motion.h1 
              variants={itemVariants}
              className="text-5xl md:text-7xl font-bold text-soft-white mb-6"
            >
              Our <span className="text-gradient">Products</span>
            </motion.h1>
            
            <motion.p 
              variants={itemVariants}
              className="text-xl md:text-2xl text-muted-grey max-w-4xl mx-auto leading-relaxed mb-12"
            >
              Discover our extensive collection of premium refurbished devices. 
              Each product is carefully tested, certified, and comes with warranty.
            </motion.p>

            {/* Enhanced Search Bar */}
            <motion.form 
              variants={itemVariants}
              onSubmit={handleSearch}
              className="max-w-2xl mx-auto"
            >
              <div className="relative group">
                <Search className="absolute left-6 top-1/2 transform -translate-y-1/2 text-muted-grey w-6 h-6 group-focus-within:text-accent-primary transition-colors" />
                <Input
                  type="text"
                  placeholder="Search for laptops, desktops, accessories..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-16 pr-32 py-6 text-lg bg-gradient-card border-shadow-grey/30 focus:border-accent-primary/50 rounded-2xl shadow-xl backdrop-blur-sm"
                />
                <Button 
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-primary hover:bg-gradient-primary/90 hover:scale-105 px-8 py-3 rounded-xl transition-all duration-300"
                >
                  Search
                </Button>
              </div>
            </motion.form>
          </motion.div>
        </div>
      </section>

      {/* Enhanced Filters and Controls Section */}
      <section className="px-4 pb-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={containerVariants}
            className="flex flex-col lg:flex-row gap-8"
          >
            {/* Categories Sidebar */}
            <motion.div variants={itemVariants} className="lg:w-1/4">
              <SpotlightCard
                className="sticky top-24 overflow-hidden hover:shadow-xl hover:shadow-terra-cotta-rose/10 transition-all duration-700"
                spotlightColor="rgba(149, 99, 88, 0.1)"
              >
                <div className="p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center">
                      <SlidersHorizontal className="w-5 h-5 text-white" />
                    </div>
                    <h2 className="text-xl font-bold text-soft-white">Categories</h2>
                  </div>

                  <div className="space-y-2">
                    <button
                      onClick={() => externalSetSelectedCategory && externalSetSelectedCategory('')}
                      className={`w-full flex items-center justify-between p-4 text-left hover:bg-shadow-grey/30 hover:scale-[1.02] rounded-xl transition-all duration-300 group ${!selectedCategory ? 'bg-gradient-primary/20 border border-accent-primary/30' : ''}`}
                    >
                      <div className="flex items-center gap-3">
                        <Package className="w-5 h-5 text-accent-secondary group-hover:scale-110 transition-transform duration-300" />
                        <span className="text-soft-white group-hover:text-accent-secondary transition-colors duration-300">
                          All Products
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="bg-gradient-primary/20 backdrop-blur-sm rounded-full px-2 py-1 min-w-[24px] h-6 flex items-center justify-center group-hover:bg-gradient-primary/30 transition-colors duration-300">
                          <span className="text-xs font-medium text-soft-white">
                            {(() => {
                              const totalCount = categories.reduce((total, cat) => total + (cat.productCount || 0), 0);
                              return totalCount > 0 ? totalCount : filteredProducts.length;
                            })()}
                          </span>
                        </div>
                        {!selectedCategory && (
                          <div className="w-2 h-2 bg-accent-primary rounded-full animate-pulse" />
                        )}
                      </div>
                    </button>

                    {categories.length > 0 ? (
                      categories.map((category) => {
                        const IconComponent = categoryIcons[category.slug as keyof typeof categoryIcons] || categoryIcons.default;
                        const isSelected = selectedCategory === category.slug;

                        return (
                          <button
                            key={category.id}
                            onClick={() => externalSetSelectedCategory && externalSetSelectedCategory(category.slug)}
                            className={`w-full flex items-center justify-between p-4 text-left hover:bg-shadow-grey/30 hover:scale-[1.02] rounded-xl transition-all duration-300 group ${isSelected ? 'bg-gradient-primary/20 border border-accent-primary/30' : ''}`}
                          >
                            <div className="flex items-center gap-3">
                              <IconComponent className="w-5 h-5 text-accent-secondary group-hover:scale-110 transition-transform duration-300" />
                              <span className="text-soft-white group-hover:text-accent-secondary transition-colors duration-300">
                                {category.name}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <div className="bg-gradient-primary/20 backdrop-blur-sm rounded-full px-2 py-1 min-w-[24px] h-6 flex items-center justify-center group-hover:bg-gradient-primary/30 transition-colors duration-300">
                                <span className="text-xs font-medium text-soft-white">
                                  {(category as any).productCount || 0}
                                </span>
                              </div>
                              {isSelected && (
                                <div className="w-2 h-2 bg-accent-primary rounded-full animate-pulse" />
                              )}
                            </div>
                          </button>
                        );
                      })
                    ) : (
                      <div className="p-4 text-center text-muted-grey">
                        <Package className="w-8 h-8 mx-auto mb-2 opacity-50" />
                        <p className="text-sm">No categories available</p>
                      </div>
                    )}
                  </div>

                  {/* View Mode Toggle */}
                  <div className="mt-8 pt-6 border-t border-shadow-grey/30">
                    <h3 className="text-sm font-semibold text-soft-white mb-4 uppercase tracking-wider">View Mode</h3>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setViewMode('grid')}
                        className={`flex-1 flex items-center justify-center gap-2 p-3 rounded-lg transition-all duration-300 ${viewMode === 'grid' ? 'bg-gradient-primary text-white' : 'bg-shadow-grey/30 text-muted-grey hover:bg-shadow-grey/50 hover:text-soft-white'}`}
                      >
                        <Grid3X3 className="w-4 h-4" />
                        <span className="text-sm font-medium">Grid</span>
                      </button>
                      <button
                        onClick={() => setViewMode('list')}
                        className={`flex-1 flex items-center justify-center gap-2 p-3 rounded-lg transition-all duration-300 ${viewMode === 'list' ? 'bg-gradient-primary text-white' : 'bg-shadow-grey/30 text-muted-grey hover:bg-shadow-grey/50 hover:text-soft-white'}`}
                      >
                        <List className="w-4 h-4" />
                        <span className="text-sm font-medium">List</span>
                      </button>
                    </div>
                  </div>
                </div>
              </SpotlightCard>
            </motion.div>

            {/* Products Grid */}
            <motion.div variants={itemVariants} className="lg:w-3/4">
              {/* Results Header */}
              <div className="flex items-center justify-between mb-8">
                <div>
                  <h3 className="text-2xl font-bold text-soft-white mb-2">
                    {selectedCategory ? categories.find(c => c.slug === selectedCategory)?.name : 'All Products'}
                  </h3>
                  <p className="text-muted-grey">
                    {filteredProducts.length} products shown
                    {hasPrivateAccess && activeProductsCount > 0 && (
                      <span className="ml-2 text-accent-primary">
                        • Private access enabled
                      </span>
                    )}
                  </p>
                </div>

                <div className="flex items-center gap-4">
                  <select
                    value={sortBy}
                    onChange={(e) => externalSetSortBy && externalSetSortBy(e.target.value)}
                    className="bg-gradient-card border border-shadow-grey/30 text-soft-white rounded-lg px-4 py-2 focus:border-accent-primary/50 focus:outline-none"
                  >
                    <option value="name">Sort by Name</option>
                    <option value="price_asc">Price: Low to High</option>
                    <option value="price_desc">Price: High to Low</option>
                    <option value="newest">Newest First</option>
                  </select>
                </div>
              </div>

              {/* Loading State */}
              {isLoading ? (
                <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                  {Array.from({ length: 6 }).map((_, index) => (
                    <SpotlightCard key={index} className="animate-pulse">
                      <div className="p-6">
                        <div className="aspect-video bg-shadow-grey/30 rounded-xl mb-4" />
                        <div className="space-y-3">
                          <div className="h-4 bg-shadow-grey/30 rounded w-3/4" />
                          <div className="h-3 bg-shadow-grey/30 rounded w-1/2" />
                          <div className="h-6 bg-shadow-grey/30 rounded w-1/3" />
                        </div>
                      </div>
                    </SpotlightCard>
                  ))}
                </div>
              ) : (
                <div className="relative">
                  {/* Background Overlay for Focus Effect - Only dims non-hovered products */}
                  {hoveredProductId && (
                    <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-10 transition-all duration-300 pointer-events-none" />
                  )}

                  <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    className={`grid relative z-20 ${
                      viewMode === 'grid'
                        ? 'gap-6 grid-cols-1 md:grid-cols-2 xl:grid-cols-3 items-start'
                        : 'gap-6 grid-cols-1'
                    }`}
                    style={{
                      gridAutoRows: 'auto'
                    }}
                  >
                    {filteredProducts.map((product, index) => (
                      <ProductCard
                        key={product.id}
                        product={product}
                        viewMode={viewMode}
                        index={index}
                        isHovered={hoveredProductId === product.id}
                        onHover={setHoveredProductId}
                        hoveredProductId={hoveredProductId}
                      />
                    ))}
                  </motion.div>
                </div>
              )}

              {/* No Products Found */}
              {!isLoading && !isLoadingMore && filteredProducts.length === 0 && (
                <motion.div
                  variants={itemVariants}
                  className="text-center py-16"
                >
                  <SpotlightCard className="max-w-md mx-auto">
                    <div className="p-8">
                      <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                        <Package className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-lg font-semibold text-soft-white mb-2">No Products Found</h3>
                      <p className="text-muted-grey mb-6">
                        {selectedCategory || searchTerm ?
                          'Try adjusting your search or filter criteria' :
                          'No products are currently available'
                        }
                      </p>
                      <Button
                        onClick={() => {
                          if (externalSetSelectedCategory) externalSetSelectedCategory('');
                          if (externalSetSearchTerm) externalSetSearchTerm('');
                          setSearchQuery('');
                        }}
                        className="bg-gradient-primary hover:bg-gradient-primary/90"
                      >
                        Clear Filters
                      </Button>
                    </div>
                  </SpotlightCard>
                </motion.div>
              )}

              {/* Infinite Scroll Loading */}
              {hasMoreActive && !isLoading && (
                <div ref={loadMoreRef} className="flex items-center justify-center py-8">
                  {isLoadingMore ? (
                    <div className="flex items-center gap-2 text-muted-grey">
                      <div className="w-5 h-5 border-2 border-accent-primary border-t-transparent rounded-full animate-spin" />
                      <span>Loading more products...</span>
                    </div>
                  ) : (
                    <Button
                      onClick={onLoadMore}
                      variant="outline"
                      size="lg"
                      className="border-shadow-grey/30 bg-gradient-card hover:bg-gradient-card/80 text-soft-white hover:border-accent-primary/50 hover:scale-105 transition-all duration-300"
                    >
                      Load More Products
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  )}
                </div>
              )}

              {/* Load More Button for Private Products */}
              {showLoadMoreButton && !hasPrivateAccess && !user && (
                <motion.div
                  variants={itemVariants}
                  className="text-center py-16"
                >
                  <SpotlightCard className="max-w-lg mx-auto">
                    <div className="p-8">
                      <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                        <Zap className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-soft-white mb-4">
                        Want to see more products?
                      </h3>
                      <p className="text-muted-grey mb-2">
                        Access our exclusive private collection with premium devices
                      </p>
                      <p className="text-sm text-muted-grey mb-8">
                        Or <Link href="/auth/signin" className="text-accent-primary hover:underline">sign in</Link> to automatically access all products
                      </p>
                      <Button
                        onClick={onShowQueryForm}
                        size="lg"
                        className="bg-gradient-primary hover:bg-gradient-primary/90 hover:scale-105 transition-all duration-300 shadow-xl hover:shadow-2xl"
                      >
                        <Eye className="w-5 h-5 mr-2" />
                        Load More Products
                      </Button>
                    </div>
                  </SpotlightCard>
                </motion.div>
              )}
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
}

// Enhanced Product Card Component with Hover Effects
interface ProductCardProps {
  product: Product;
  viewMode: 'grid' | 'list';
  index: number;
  isHovered: boolean;
  onHover: (productId: string | null) => void;
  hoveredProductId: string | null;
}

function ProductCard({ product, viewMode, index, isHovered, onHover, hoveredProductId }: ProductCardProps) {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  // Get key specifications for hover display
  const getKeySpecifications = () => {
    if (!product.specifications) return [];

    const keySpecs = ['CPU', 'RAM', 'Storage', 'OS', 'Screen Size', 'GPU'];
    return keySpecs
      .map(key => ({ key, value: product.specifications?.[key] }))
      .filter(spec => spec.value && spec.value.trim() !== '')
      .slice(0, 4); // Show max 4 specs
  };

  const keySpecs = getKeySpecifications();

  // Determine if this product should be dimmed (when another product is hovered)
  const shouldDim = hoveredProductId && hoveredProductId !== product.id;

  if (viewMode === 'list') {
    return (
      <div
        className={`relative transition-all duration-700 ${isHovered ? 'z-30' : 'z-20'} ${shouldDim ? 'opacity-40 scale-95' : 'opacity-100 scale-100'}`}
        onMouseEnter={() => onHover(product.id)}
        onMouseLeave={() => onHover(null)}
      >
        <SpotlightCard
          className={`group overflow-visible transition-all duration-700 cursor-pointer ${
            isHovered
              ? 'shadow-2xl shadow-terra-cotta-rose/40 z-30'
              : 'hover:shadow-2xl hover:shadow-terra-cotta-rose/20'
          }`}
          spotlightColor="rgba(149, 99, 88, 0.15)"
        >
          <div
            className={`p-6 flex gap-6 transition-all duration-300 ${isHovered ? 'pb-8' : ''}`}
            onClick={() => window.location.href = `/products/${product.slug}`}
          >
              {/* Product Image */}
              <div className="w-48 h-32 bg-shadow-grey/30 rounded-xl overflow-hidden relative flex-shrink-0 group-hover:shadow-lg group-hover:shadow-terra-cotta-rose/20 transition-all duration-500">
                {imageError || !product.images?.[0] ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <Package className="w-12 h-12 text-muted-grey" />
                  </div>
                ) : (
                  <Image
                    src={product.images[0]}
                    alt={product.name}
                    fill
                    className="object-cover transition-all duration-500 group-hover:scale-110 group-hover:brightness-110"
                    onError={handleImageError}
                  />
                )}
                {/* Hover Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
              </div>

              {/* Product Details */}
              <div className="flex-1 flex flex-col justify-between">
                <div>
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h3 className="text-xl font-bold text-soft-white group-hover:text-accent-secondary transition-colors duration-300 line-clamp-1">
                        {product.name}
                      </h3>
                      <p className="text-muted-grey group-hover:text-soft-white transition-colors duration-300">
                        {product.brands?.name ||
                         product.brand ||
                         'No Brand'} {product.model && `• ${product.model}`}
                      </p>
                    </div>
                    <Heart className="w-5 h-5 text-muted-grey hover:text-accent-primary transition-colors cursor-pointer" />
                  </div>

                  {/* Badges */}
                  <div className="flex items-center gap-2 mb-4">
                    <Badge className="bg-gradient-primary text-white border-0">
                      {product.conditions?.name || 'Excellent'}
                    </Badge>
                    <div className="flex items-center gap-1">
                      <div className={`w-2 h-2 rounded-full ${product.is_active && (product.stock_quantity || 0) > 0 ? 'bg-success' : 'bg-error'}`} />
                      <span className="text-xs text-muted-grey">
                        {product.is_active && (product.stock_quantity || 0) > 0 ? 'In Stock' : 'Out of Stock'}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Price and Action */}
                <div className="flex items-center justify-between">
                  <div>
                    {product.show_price ? (
                      <span className="text-2xl font-bold text-soft-white group-hover:text-accent-primary transition-colors duration-300">
                        ₹{product.price.toLocaleString('en-IN')}
                      </span>
                    ) : (
                      <span className="text-lg font-bold text-accent-primary">
                        Contact for Price
                      </span>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Link href={`/products/${product.slug}`}>
                      <Button
                        size="sm"
                        className="bg-gradient-primary hover:bg-gradient-primary/90 transition-all duration-300 group-hover:shadow-lg"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Eye className="w-4 h-4 mr-2" />
                        View Details
                      </Button>
                    </Link>
                    {isHovered && (
                      <Link href={`/inquiry?product=${product.slug}&type=regular`}>
                        <Button
                          variant="outline"
                          size="sm"
                          className="border-accent-primary text-accent-primary hover:bg-accent-primary hover:text-white transition-all duration-300"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MessageSquare className="w-4 h-4 mr-2" />
                          Inquiry
                        </Button>
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            </div>

          {/* Specifications Preview (shown below the card on hover) */}
          {isHovered && keySpecs.length > 0 && (
            <div
              className="px-6 pb-6 border-t border-white/10 animate-in slide-in-from-top duration-300"
            >
              <div className="pt-4">
                <h4 className="text-sm font-semibold text-accent-primary mb-3 flex items-center gap-2">
                  <Cpu className="w-4 h-4" />
                  Key Specifications
                </h4>
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-2">
                  {keySpecs.slice(0, 4).map((spec, index) => (
                    <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg p-2">
                      <div className="text-xs text-muted-grey">{spec.key}</div>
                      <div className="text-sm font-medium text-soft-white truncate">{spec.value}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </SpotlightCard>
      </div>
    );
  }

  // Grid View
  return (
    <div
      className={`relative transition-all duration-700 ${isHovered ? 'z-30' : 'z-20'} ${shouldDim ? 'opacity-40 scale-95' : 'opacity-100 scale-100'}`}
      onMouseEnter={() => onHover(product.id)}
      onMouseLeave={() => onHover(null)}
    >
      {!isHovered ? (
        <SpotlightCard
          className="group overflow-visible transition-all duration-700 cursor-pointer animate-card-float hover:shadow-2xl hover:shadow-terra-cotta-rose/20"
          spotlightColor="rgba(149, 99, 88, 0.15)"
        >
          <div
            className="p-6"
            onClick={() => window.location.href = `/products/${product.slug}`}
          >
              {/* Product Image */}
              <div className="aspect-video bg-shadow-grey/30 rounded-xl mb-4 overflow-hidden relative group-hover:shadow-xl group-hover:shadow-terra-cotta-rose/30 transition-all duration-500">
                {imageError || !product.images?.[0] ? (
                  <div className="w-full h-full flex items-center justify-center">
                    <Package className="w-16 h-16 text-muted-grey group-hover:text-accent-secondary transition-colors duration-300" />
                  </div>
                ) : (
                  <Image
                    src={product.images[0]}
                    alt={product.name}
                    fill
                    className="object-cover transition-all duration-500 group-hover:scale-110 group-hover:brightness-110"
                    onError={handleImageError}
                  />
                )}

                {/* Stock Status Indicator */}
                <div className="absolute top-4 right-4">
                  <div className={`w-3 h-3 rounded-full ${product.is_active && (product.stock_quantity || 0) > 0 ? 'bg-success' : 'bg-error'} shadow-lg`} />
                </div>
              </div>

              {/* Product Info */}
              <div className="space-y-3">
                <div>
                  <h3 className="text-lg font-bold text-soft-white group-hover:text-accent-secondary transition-colors duration-300 line-clamp-1">
                    {product.name}
                  </h3>
                  <p className="text-sm text-muted-grey group-hover:text-soft-white transition-colors duration-300">
                    {product.brands?.name || product.brand || 'No Brand'} {product.model && `• ${product.model}`}
                  </p>
                </div>

                {/* Price */}
                <div>
                  {product.show_price ? (
                    <span className="text-xl font-bold text-soft-white group-hover:text-accent-primary transition-colors duration-300">
                      ₹{product.price.toLocaleString('en-IN')}
                    </span>
                  ) : (
                    <span className="text-sm font-bold text-accent-primary">
                      Contact for Price
                    </span>
                  )}
                </div>

                {/* Action Button */}
                <Link href={`/products/${product.slug}`}>
                  <Button
                    className="w-full bg-gradient-primary hover:bg-gradient-primary/90 hover:shadow-lg transition-all duration-300 group-hover:shadow-terra-cotta-rose/30"
                    size="sm"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    View Details
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                  </Button>
                </Link>
              </div>
            </div>
        </SpotlightCard>
      ) : (
        <div
          className="bg-gradient-to-br from-deep-night-blue/95 via-background-secondary/95 to-shadow-grey/95 backdrop-blur-sm rounded-xl p-6 border border-accent-primary/30 shadow-2xl shadow-terra-cotta-rose/60 z-30 animate-in fade-in duration-300 ring-2 ring-accent-primary/20 ring-offset-2 ring-offset-transparent"
          style={{ height: 'auto', minHeight: 'fit-content' }}
        >
          <div className="flex flex-col h-full space-y-4">
            {/* Product Header */}
            <div className="flex-shrink-0">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1 pr-4">
                  <h3 className="text-lg font-bold text-accent-secondary mb-1">
                    {product.name}
                  </h3>
                  <p className="text-sm text-soft-white">
                    {product.brands?.name || product.brand || 'No Brand'} {product.model && `• ${product.model}`}
                  </p>
                </div>
                <Badge className="bg-gradient-primary text-white border-0 shadow-lg flex-shrink-0">
                  {product.conditions?.name || 'Excellent'}
                </Badge>
              </div>
            </div>

            {/* Product Image - Smaller in hover state */}
            <div className="aspect-video bg-shadow-grey/30 rounded-xl mb-4 overflow-hidden relative flex-shrink-0">
              {imageError || !product.images?.[0] ? (
                <div className="w-full h-full flex items-center justify-center">
                  <Package className="w-12 h-12 text-muted-grey" />
                </div>
              ) : (
                <Image
                  src={product.images[0]}
                  alt={product.name}
                  fill
                  className="object-cover"
                  onError={handleImageError}
                />
              )}
              {/* Stock Status Indicator */}
              <div className="absolute top-3 right-3">
                <div className={`w-3 h-3 rounded-full ${product.is_active && (product.stock_quantity || 0) > 0 ? 'bg-success' : 'bg-error'} shadow-lg`} />
              </div>
            </div>

            {/* Key Specifications */}
            {keySpecs.length > 0 && (
              <div className="mb-4 flex-grow">
                <h4 className="text-sm font-semibold text-accent-primary mb-3 flex items-center gap-2">
                  <Cpu className="w-4 h-4" />
                  Key Specifications
                </h4>
                <div className="grid grid-cols-2 gap-2">
                  {keySpecs.map((spec, index) => (
                    <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg p-2">
                      <div className="text-xs text-muted-grey">{spec.key}</div>
                      <div className="text-sm font-medium text-soft-white truncate">{spec.value}</div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Bottom Section */}
            <div className="space-y-3 mt-auto flex-shrink-0">
              {/* Price */}
              <div className="text-center">
                {product.show_price ? (
                  <span className="text-2xl font-bold text-accent-primary">
                    ₹{product.price.toLocaleString('en-IN')}
                  </span>
                ) : (
                  <span className="text-lg font-bold text-accent-primary">
                    Contact for Price
                  </span>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Link href={`/products/${product.slug}`} className="flex-1">
                  <Button
                    className="w-full bg-gradient-primary hover:bg-gradient-primary/90 transition-all duration-300"
                    size="sm"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Eye className="w-4 h-4 mr-2" />
                    View Details
                  </Button>
                </Link>
                <Link href={`/inquiry?product=${product.slug}&type=regular`} className="flex-1">
                  <Button
                    variant="outline"
                    className="w-full border-accent-primary text-accent-primary hover:bg-accent-primary hover:text-white transition-all duration-300"
                    size="sm"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <MessageSquare className="w-4 h-4 mr-2" />
                    Inquiry
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
