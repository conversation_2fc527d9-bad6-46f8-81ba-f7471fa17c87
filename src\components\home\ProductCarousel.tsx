'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Play, Pause } from 'lucide-react';
import { formatPrice } from '@/lib/product-utils';

interface CarouselProduct {
  id: string;
  name: string;
  price: number;
  image: string;
  brand: string;
  model: string;
  description?: string;
  show_price?: boolean;
}

export default function ProductCarousel() {
  const [products, setProducts] = useState<CarouselProduct[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

  useEffect(() => {
    fetchCarouselProducts();
  }, []);

  useEffect(() => {
    if (!isPlaying || products.length <= 1) return;

    const interval = setInterval(() => {
      goToNext();
    }, 5000);

    return () => clearInterval(interval);
  }, [isPlaying, products.length, currentIndex]);

  const fetchCarouselProducts = async () => {
    try {
      const response = await fetch('/api/home/<USER>');
      if (response.ok) {
        const data = await response.json();
        console.log('Carousel API response:', data); // Debug log
        setProducts(data.products || []);
      }
    } catch (error) {
      console.error('Error fetching carousel products:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const goToSlide = (index: number) => {
    if (index === currentIndex || isTransitioning) return;

    setIsTransitioning(true);
    setCurrentIndex(index);

    setTimeout(() => {
      setIsTransitioning(false);
    }, 600);
  };

  const goToPrevious = () => {
    if (isTransitioning) return;
    const newIndex = (currentIndex - 1 + products.length) % products.length;
    goToSlide(newIndex);
  };

  const goToNext = () => {
    if (isTransitioning) return;
    const newIndex = (currentIndex + 1) % products.length;
    goToSlide(newIndex);
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const getSlideIndex = (offset: number) => {
    return (currentIndex + offset + products.length) % products.length;
  };

  if (isLoading) {
    return (
      <div className="relative h-[600px] bg-gradient-to-br from-[var(--background-primary)] to-[var(--background-secondary)] rounded-2xl overflow-hidden">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="w-8 h-8 border-2 border-[var(--accent-primary)] border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="relative h-[600px] bg-gradient-to-br from-[var(--background-primary)] to-[var(--background-secondary)] rounded-2xl overflow-hidden flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-2">No Featured Products</h3>
          <p className="text-[var(--text-secondary)]">Add products to the carousel to display them here</p>
        </div>
      </div>
    );
  }

  const prevIndex = getSlideIndex(-1);
  const nextIndex = getSlideIndex(1);

  // Safety checks for products
  const currentProduct = products[currentIndex];
  const prevProduct = products[prevIndex];
  const nextProduct = products[nextIndex];

  if (!currentProduct) {
    return (
      <div className="relative h-[600px] bg-gradient-to-br from-[var(--background-primary)] to-[var(--background-secondary)] rounded-2xl overflow-hidden flex items-center justify-center">
        <div className="text-center">
          <h3 className="text-xl font-semibold text-[var(--text-primary)] mb-2">No Featured Products</h3>
          <p className="text-[var(--text-secondary)]">Add products to the carousel to display them here</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full h-[600px] bg-gradient-to-br from-[var(--background-primary)] via-[var(--background-secondary)] to-[var(--background-card)] rounded-3xl overflow-hidden">
      {/* Background Blur Effect */}
      <div className="absolute inset-0 bg-gradient-to-br from-[var(--accent-primary)]/5 via-[var(--accent-secondary)]/5 to-[var(--background-card)]/10 backdrop-blur-sm"></div>

      {/* Carousel Container */}
      <div className="relative w-full h-full flex items-center justify-center px-12">

        {/* Previous Card (Left) */}
        <div
          className="absolute left-12 w-80 h-96 transition-all duration-700 ease-out cursor-pointer"
          style={{
            transform: isTransitioning
              ? 'translateX(-100px) scale(0.8)'
              : 'translateX(0) scale(0.85)',
            opacity: 0.7,
            zIndex: 1
          }}
          onClick={goToPrevious}
        >
          <div className="w-full h-full rounded-2xl overflow-hidden shadow-2xl bg-[var(--background-card)] border border-[var(--shadow-grey)]">
            {prevProduct && (
              <>
                <div className="w-full h-64 overflow-hidden">
                  <img
                    src={prevProduct.image || 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500&h=400&fit=crop'}
                    alt={prevProduct.name || 'Product'}
                    className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                  />
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-bold text-[var(--text-primary)] mb-1 line-clamp-1">
                    {prevProduct.name}
                  </h3>
                  <p className="text-sm text-[var(--text-secondary)] mb-2">
                    {prevProduct.brand} • {prevProduct.model}
                  </p>
                  {prevProduct.show_price && prevProduct.price && (
                    <p className="text-xl font-bold text-[var(--accent-primary)]">
                      {formatPrice(parseFloat(prevProduct.price.toString()))}
                    </p>
                  )}
                </div>
              </>
            )}
          </div>
        </div>

        {/* Current Card (Center) */}
        <div
          className="relative w-96 h-[500px] transition-all duration-700 ease-out"
          style={{
            transform: isTransitioning
              ? 'scale(1.05)'
              : 'scale(1)',
            zIndex: 10
          }}
        >
          <div className="w-full h-full rounded-3xl overflow-hidden shadow-2xl bg-[var(--background-card)] border border-[var(--shadow-grey)] transform hover:scale-105 transition-transform duration-300">
            <div className="w-full h-80 overflow-hidden">
              <img
                src={currentProduct.image || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=500&fit=crop'}
                alt={currentProduct.name || 'Product'}
                className="w-full h-full object-cover"
              />
            </div>

            {/* Card Content */}
            <div className="p-6">
              <div className="mb-3">
                <span className="inline-block px-3 py-1 bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] text-white text-xs font-medium rounded-full">
                  Featured
                </span>
              </div>

              <h2 className="text-2xl font-bold text-[var(--text-primary)] mb-2 line-clamp-1">
                {currentProduct.name || 'Featured Product'}
              </h2>

              <p className="text-[var(--text-secondary)] mb-3">
                {currentProduct.brand} • {currentProduct.model}
              </p>

              {currentProduct.show_price && currentProduct.price && (
                <p className="text-2xl font-bold text-[var(--accent-primary)] mb-4">
                  {formatPrice(parseFloat(currentProduct.price.toString()))}
                </p>
              )}

              <div className="flex items-center space-x-3">
                <button className="flex-1 bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] text-white py-2 px-4 rounded-xl font-medium hover:shadow-lg transition-all duration-300 transform hover:scale-105">
                  View Details
                </button>
                <button className="p-2 border-2 border-[var(--accent-primary)]/30 text-[var(--accent-primary)] rounded-xl hover:bg-[var(--accent-primary)]/10 transition-colors duration-300">
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Next Card (Right) */}
        <div
          className="absolute right-12 w-80 h-96 transition-all duration-700 ease-out cursor-pointer"
          style={{
            transform: isTransitioning
              ? 'translateX(100px) scale(0.8)'
              : 'translateX(0) scale(0.85)',
            opacity: 0.7,
            zIndex: 1
          }}
          onClick={goToNext}
        >
          <div className="w-full h-full rounded-2xl overflow-hidden shadow-2xl bg-[var(--background-card)] border border-[var(--shadow-grey)]">
            {nextProduct && (
              <>
                <div className="w-full h-64 overflow-hidden">
                  <img
                    src={nextProduct.image || 'https://images.unsplash.com/photo-1525966222134-fcfa99b8ae77?w=500&h=400&fit=crop'}
                    alt={nextProduct.name || 'Product'}
                    className="w-full h-full object-cover transition-transform duration-500 hover:scale-110"
                  />
                </div>
                <div className="p-4">
                  <h3 className="text-lg font-bold text-[var(--text-primary)] mb-1 line-clamp-1">
                    {nextProduct.name}
                  </h3>
                  <p className="text-sm text-[var(--text-secondary)] mb-2">
                    {nextProduct.brand} • {nextProduct.model}
                  </p>
                  {nextProduct.show_price && nextProduct.price && (
                    <p className="text-xl font-bold text-[var(--accent-primary)]">
                      {formatPrice(parseFloat(nextProduct.price.toString()))}
                    </p>
                  )}
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Navigation Controls */}
      <div className="absolute left-4 top-1/2 transform -translate-y-1/2 z-20">
        <button
          onClick={goToPrevious}
          disabled={isTransitioning}
          className="p-3 bg-[var(--background-card)]/90 hover:bg-[var(--background-card)] text-[var(--text-primary)] border border-[var(--shadow-grey)] rounded-full shadow-lg backdrop-blur-sm transition-all duration-300 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronLeft className="h-5 w-5" />
        </button>
      </div>

      <div className="absolute right-4 top-1/2 transform -translate-y-1/2 z-20">
        <button
          onClick={goToNext}
          disabled={isTransitioning}
          className="p-3 bg-[var(--background-card)]/90 hover:bg-[var(--background-card)] text-[var(--text-primary)] border border-[var(--shadow-grey)] rounded-full shadow-lg backdrop-blur-sm transition-all duration-300 hover:scale-110 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ChevronRight className="h-5 w-5" />
        </button>
      </div>

      {/* Play/Pause Control */}
      <div className="absolute top-6 right-6 z-20">
        <button
          onClick={togglePlayPause}
          className="p-2 bg-[var(--background-card)]/90 hover:bg-[var(--background-card)] text-[var(--text-primary)] border border-[var(--shadow-grey)] rounded-full shadow-lg backdrop-blur-sm transition-all duration-300 hover:scale-110"
        >
          {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
        </button>
      </div>

      {/* Slide Indicators */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 z-20">
        <div className="flex space-x-2 bg-[var(--background-card)]/80 backdrop-blur-sm rounded-full px-4 py-2 border border-[var(--shadow-grey)]">
          {products.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              disabled={isTransitioning}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-[var(--accent-primary)] scale-125'
                  : 'bg-[var(--text-secondary)]/40 hover:bg-[var(--text-secondary)]/60 hover:scale-110'
              } disabled:cursor-not-allowed`}
            />
          ))}
        </div>
      </div>

      {/* Progress Bar */}
      <div className="absolute bottom-0 left-0 right-0 h-1 bg-[var(--shadow-grey)]/30 z-20 rounded-b-3xl overflow-hidden">
        <div
          className="h-full bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] transition-all duration-100 ease-linear"
          style={{
            width: isPlaying ? `${((currentIndex + 1) / products.length) * 100}%` : '0%'
          }}
        />
      </div>
    </div>
  );
}
