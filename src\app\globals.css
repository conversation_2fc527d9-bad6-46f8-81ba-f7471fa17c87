@import "tailwindcss";

/* Import 3D Carousel Styles */
@import '../styles/carousel-3d.css';
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

:root {
  /* === PROFESSIONAL COLOR PALETTE === */

  /* Core Brand Colors */
  --deep-night-blue: #03132a;
  --muted-steel-blue: #273549;
  --terra-cotta-rose: #956358;
  --pale-peach: #f9c1b2;

  /* Supporting Colors */
  --soft-white: #E0E0E0;
  --muted-grey: #A0A0A0;
  --shadow-grey: #404B5C;

  /* Primary System Colors */
  --background-primary: var(--deep-night-blue);
  --background-secondary: var(--muted-steel-blue);
  --background-card: var(--muted-steel-blue);
  --text-primary: var(--soft-white);
  --text-secondary: var(--muted-grey);
  --accent-primary: var(--terra-cotta-rose);
  --accent-secondary: var(--pale-peach);

  /* Functional Colors */
  --success: #66BB6A;
  --warning: #FFCA28;
  --error: #EF5350;
  --info: #42A5F5;

  /* Professional Gradients */
  --gradient-primary: linear-gradient(135deg, var(--terra-cotta-rose) 0%, var(--pale-peach) 100%);
  --gradient-background: linear-gradient(135deg, var(--deep-night-blue) 0%, var(--muted-steel-blue) 100%);
  --gradient-card: linear-gradient(145deg, var(--muted-steel-blue) 0%, var(--shadow-grey) 100%);

  /* Transitions & Animations */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-smooth: cubic-bezier(0.25, 0.46, 0.45, 0.94);

  /* Professional Shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.25);
  --shadow-glow: 0 0 32px rgba(249, 193, 178, 0.3);
  --shadow-card: 0 4px 16px rgba(0, 0, 0, 0.1);
  --shadow-button: 0 2px 8px rgba(149, 99, 88, 0.3);
  --radius: 0.75rem;

  /* === SHADCN/UI INTEGRATION === */
  /* shadcn/ui CSS variables mapped to our design system */
  --background: var(--deep-night-blue);
  --foreground: var(--soft-white);
  --card: var(--muted-steel-blue);
  --card-foreground: var(--soft-white);
  --popover: var(--muted-steel-blue);
  --popover-foreground: var(--soft-white);
  --primary: var(--terra-cotta-rose);
  --primary-foreground: #ffffff;
  --secondary: var(--shadow-grey);
  --secondary-foreground: var(--soft-white);
  --muted: var(--shadow-grey);
  --muted-foreground: var(--muted-grey);
  --accent: var(--pale-peach);
  --accent-foreground: var(--deep-night-blue);
  --destructive: var(--error);
  --destructive-foreground: #ffffff;
  --border: var(--shadow-grey);
  --input: var(--muted-steel-blue);
  --ring: var(--terra-cotta-rose);
  --chart-1: var(--terra-cotta-rose);
  --chart-2: var(--pale-peach);
  --chart-3: var(--info);
  --chart-4: var(--success);
  --chart-5: var(--warning);
  --sidebar: var(--muted-steel-blue);
  --sidebar-foreground: var(--soft-white);
  --sidebar-primary: var(--terra-cotta-rose);
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: var(--pale-peach);
  --sidebar-accent-foreground: var(--deep-night-blue);
  --sidebar-border: var(--shadow-grey);
  --sidebar-ring: var(--terra-cotta-rose);
}

@theme inline {
  --color-background: var(--background-primary);
  --color-foreground: var(--text-primary);
  --font-sans: 'Poppins', ui-sans-serif, system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', ui-monospace, monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

/* === GLOBAL STYLES === */
body {
  background: var(--gradient-background);
  color: var(--text-primary);
  font-family: var(--font-sans);
  font-feature-settings: "rlig" 1, "calt" 1;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  scroll-behavior: smooth;
}

/* Professional Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--shadow-grey);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: 4px;
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-secondary);
}

/* Scrollbar positioning */
.scrollbar-right {
  direction: ltr;
}

.scrollbar-right::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-right::-webkit-scrollbar-track {
  background: var(--shadow-grey);
  border-radius: 4px;
}

.scrollbar-right::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: 4px;
  transition: background var(--transition-fast);
}

.scrollbar-right::-webkit-scrollbar-thumb:hover {
  background: var(--accent-secondary);
}

/* Left-side scrollbar */
.scrollbar-left {
  direction: rtl;
}

.scrollbar-left::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-left::-webkit-scrollbar-track {
  background: var(--shadow-grey);
  border-radius: 4px;
}

.scrollbar-left::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: 4px;
  transition: background var(--transition-fast);
}

.scrollbar-left::-webkit-scrollbar-thumb:hover {
  background: var(--accent-secondary);
}

/* === PROFESSIONAL BUTTON SYSTEM === */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-button);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--pale-peach) 0%, var(--terra-cotta-rose) 100%);
  box-shadow: var(--shadow-glow);
  transform: translateY(-3px) scale(1.05);
}

.btn-primary:active {
  transform: translateY(-1px) scale(1.02);
  box-shadow: var(--shadow-md);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-primary:focus {
  outline: 2px solid var(--accent-secondary);
  outline-offset: 2px;
}

.btn-secondary {
  background: transparent;
  border: 2px solid var(--accent-primary);
  color: var(--accent-primary);
  font-weight: 600;
  font-size: 0.875rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all var(--transition-normal);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.btn-secondary:hover {
  background: var(--gradient-primary);
  color: white;
  border-color: transparent;
  box-shadow: var(--shadow-glow);
  transform: translateY(-3px) scale(1.05);
}

.btn-secondary:active {
  transform: translateY(-1px) scale(1.02);
  box-shadow: var(--shadow-md);
}

.btn-ghost {
  background: transparent;
  border: none;
  color: var(--text-secondary);
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
}

.btn-ghost:hover {
  background: rgba(149, 99, 88, 0.15);
  color: var(--accent-primary);
  transform: translateY(-2px) scale(1.03);
  box-shadow: var(--shadow-sm);
}

.btn-ghost:active {
  transform: translateY(0) scale(1.01);
}

/* === ENHANCED BUTTON EFFECTS === */
.btn-pulse {
  animation: button-pulse 2s infinite;
}

.btn-glow {
  animation: button-glow 2s ease-in-out infinite alternate;
}

/* Button ripple effect */
.btn-ripple {
  position: relative;
  overflow: hidden;
}

.btn-ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-ripple:hover::before {
  width: 300px;
  height: 300px;
}

/* Enhanced focus states */
.btn-primary:focus,
.btn-secondary:focus,
.btn-ghost:focus {
  outline: 2px solid var(--accent-secondary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(149, 99, 88, 0.2);
}

/* === PROFESSIONAL CARD SYSTEM === */
.card {
  background: var(--gradient-card);
  border: 1px solid var(--shadow-grey);
  border-radius: 12px;
  padding: 1.5rem;
  transition: all var(--transition-normal);
  box-shadow: var(--shadow-card);
  backdrop-filter: blur(10px);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  border-color: var(--accent-secondary);
}

.card-glass {
  background: rgba(39, 53, 73, 0.8);
  border: 1px solid rgba(224, 224, 224, 0.1);
  border-radius: 16px;
  padding: 1.5rem;
  backdrop-filter: blur(20px);
  box-shadow: var(--shadow-xl);
  transition: all var(--transition-normal);
}

.card-glass:hover {
  background: rgba(39, 53, 73, 0.9);
  border-color: rgba(249, 193, 178, 0.3);
  transform: translateY(-2px);
}

/* === PROFESSIONAL INPUT SYSTEM === */
.input {
  background: var(--background-secondary);
  border: 2px solid var(--shadow-grey);
  border-radius: 8px;
  padding: 0.875rem 1rem;
  color: var(--text-primary);
  font-size: 0.875rem;
  transition: all var(--transition-normal);
  width: 100%;
  box-shadow: var(--shadow-sm);
}

.input::placeholder {
  color: var(--text-secondary);
  font-weight: 400;
}

.input:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(149, 99, 88, 0.1);
  background: var(--deep-night-blue);
}

.input:hover {
  border-color: var(--accent-secondary);
}

.input:disabled {
  background: var(--shadow-grey);
  border-color: var(--shadow-grey);
  color: var(--text-secondary);
  cursor: not-allowed;
}

/* === PROFESSIONAL BADGE SYSTEM === */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.375rem 0.875rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border: none;
  box-shadow: var(--shadow-sm);
}

.badge-pending {
  background: linear-gradient(135deg, #FFCA28 0%, #FFA726 100%);
  color: #1A1A1A;
}

.badge-in-progress {
  background: linear-gradient(135deg, #42A5F5 0%, #1E88E5 100%);
  color: white;
}

.badge-accepted {
  background: linear-gradient(135deg, #66BB6A 0%, #43A047 100%);
  color: white;
}

.badge-rejected {
  background: linear-gradient(135deg, #EF5350 0%, #E53935 100%);
  color: white;
}

.badge-completed {
  background: var(--gradient-primary);
  color: white;
}

.badge-success {
  background: var(--success);
  color: white;
}

.badge-warning {
  background: var(--warning);
  color: #1A1A1A;
}

.badge-error {
  background: var(--error);
  color: white;
}

/* === PROFESSIONAL UTILITY CLASSES === */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.text-gradient-reverse {
  background: linear-gradient(135deg, var(--pale-peach) 0%, var(--terra-cotta-rose) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-background {
  background: var(--gradient-background);
}

.bg-gradient-card {
  background: var(--gradient-card);
}

.shadow-glow {
  box-shadow: var(--shadow-glow);
}

.shadow-professional {
  box-shadow: var(--shadow-lg);
}

.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(var(--background-secondary), var(--background-secondary)) padding-box,
              var(--gradient-primary) border-box;
}

/* === PROFESSIONAL ANIMATIONS === */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes button-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(149, 99, 88, 0.4);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(149, 99, 88, 0);
  }
}

@keyframes button-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(149, 99, 88, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(149, 99, 88, 0.6);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(149, 99, 88, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(149, 99, 88, 0.6);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes slideDownFade {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === MOVING PARTICLE ANIMATIONS === */
@keyframes particle-1 {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0.3;
  }
  25% {
    transform: translate(20px, -30px) rotate(90deg);
    opacity: 0.6;
  }
  50% {
    transform: translate(-15px, -60px) rotate(180deg);
    opacity: 0.4;
  }
  75% {
    transform: translate(-35px, -30px) rotate(270deg);
    opacity: 0.5;
  }
}

@keyframes particle-2 {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.4;
  }
  33% {
    transform: translate(-25px, 40px) scale(1.2);
    opacity: 0.6;
  }
  66% {
    transform: translate(30px, 20px) scale(0.8);
    opacity: 0.3;
  }
}

@keyframes particle-3 {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
    opacity: 0.25;
  }
  50% {
    transform: translate(40px, -50px) rotate(180deg) scale(1.5);
    opacity: 0.5;
  }
}

@keyframes particle-4 {
  0%, 100% {
    transform: translate(0, 0);
    opacity: 0.35;
  }
  25% {
    transform: translate(-30px, 25px);
    opacity: 0.6;
  }
  50% {
    transform: translate(-60px, -20px);
    opacity: 0.4;
  }
  75% {
    transform: translate(-30px, -45px);
    opacity: 0.5;
  }
}

@keyframes particle-5 {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translate(35px, -40px) rotate(360deg);
    opacity: 0.6;
  }
}

@keyframes particle-6 {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.4;
  }
  33% {
    transform: translate(20px, -30px) scale(1.3);
    opacity: 0.6;
  }
  66% {
    transform: translate(-25px, -15px) scale(0.7);
    opacity: 0.3;
  }
}

@keyframes particle-slow-1 {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
    opacity: 0.2;
  }
  50% {
    transform: translate(-50px, 30px) rotate(180deg);
    opacity: 0.4;
  }
}

@keyframes particle-slow-2 {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.25;
  }
  50% {
    transform: translate(45px, -35px) scale(1.2);
    opacity: 0.5;
  }
}

@keyframes particle-slow-3 {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg) scale(1);
    opacity: 0.3;
  }
  50% {
    transform: translate(-40px, 25px) rotate(180deg) scale(0.8);
    opacity: 0.5;
  }
}

/* === CARD MOVEMENT ANIMATIONS === */
@keyframes card-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  25% {
    transform: translateY(-3px) rotate(0.5deg);
  }
  50% {
    transform: translateY(-6px) rotate(0deg);
  }
  75% {
    transform: translateY(-3px) rotate(-0.5deg);
  }
}

@keyframes card-bounce {
  0% {
    transform: translateY(0px) scale(1);
  }
  30% {
    transform: translateY(-8px) scale(1.02);
  }
  50% {
    transform: translateY(-12px) scale(1.03);
  }
  70% {
    transform: translateY(-8px) scale(1.02);
  }
  100% {
    transform: translateY(0px) scale(1);
  }
}

@keyframes product-hover {
  0% {
    transform: translateY(0) scale(1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
  100% {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 20px 40px rgba(149, 99, 88, 0.2);
  }
}

@keyframes image-zoom {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(149, 99, 88, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(149, 99, 88, 0.6);
  }
}

/* Professional Animation Classes */
.animate-fade-in {
  animation: fade-in 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-up {
  animation: slide-up 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale-in {
  animation: scale-in 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* === PARTICLE ANIMATION CLASSES === */
.animate-particle-1 {
  animation: particle-1 8s ease-in-out infinite;
}

.animate-particle-2 {
  animation: particle-2 6s ease-in-out infinite;
}

.animate-particle-3 {
  animation: particle-3 10s ease-in-out infinite;
}

.animate-particle-4 {
  animation: particle-4 7s ease-in-out infinite;
}

.animate-particle-5 {
  animation: particle-5 9s ease-in-out infinite;
}

.animate-particle-6 {
  animation: particle-6 5s ease-in-out infinite;
}

.animate-particle-slow-1 {
  animation: particle-slow-1 15s ease-in-out infinite;
}

.animate-particle-slow-2 {
  animation: particle-slow-2 12s ease-in-out infinite;
}

.animate-particle-slow-3 {
  animation: particle-slow-3 18s ease-in-out infinite;
}

/* === CARD ANIMATION CLASSES === */
.animate-card-float {
  animation: card-float 4s ease-in-out infinite;
}

.animate-card-bounce:hover {
  animation: card-bounce 0.6s ease-in-out;
}

.animate-product-hover {
  animation: product-hover 0.3s ease-out forwards;
}

.animate-image-zoom {
  animation: image-zoom 0.5s ease-out forwards;
}

.animate-glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
}

/* === ENHANCED PRODUCT CARD STYLES === */
.product-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.product-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(149, 99, 88, 0.2);
}

.product-card .product-image {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:hover .product-image {
  transform: scale(1.1);
  filter: brightness(1.1);
}

.product-card .product-overlay {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
}

.product-card:hover .product-overlay {
  opacity: 1;
  backdrop-filter: blur(8px);
}

/* Enhanced hover effects for product cards */
.product-card-enhanced {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.product-card-enhanced:hover {
  transform: translateY(-12px) scale(1.05);
  box-shadow: 0 25px 50px rgba(149, 99, 88, 0.25);
  z-index: 10;
}

.product-card-enhanced .card-glow {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(149, 99, 88, 0.1), rgba(249, 193, 178, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: inherit;
}

.product-card-enhanced:hover .card-glow {
  opacity: 1;
}

/* Enhanced Product Hover Effects */
.product-hover-overlay {
  backdrop-filter: blur(12px);
  background: linear-gradient(135deg,
    rgba(39, 53, 73, 0.95) 0%,
    rgba(45, 60, 82, 0.95) 50%,
    rgba(52, 67, 91, 0.95) 100%);
  border: 1px solid rgba(149, 99, 88, 0.2);
}

.product-hover-specs {
  animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.product-focus-background {
  backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease-in-out;
}

/* Smooth transitions for product cards */
.product-card-transition {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.product-card-hover-scale {
  transform: scale(1.1) translateZ(0);
  box-shadow: 0 30px 60px rgba(149, 99, 88, 0.4);
  z-index: 30;
}

/* Prevent layout shift during hover */
.product-grid-container {
  display: grid;
  gap: 2rem;
  align-items: start;
}

.product-card-wrapper {
  min-height: 400px;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.product-card-wrapper.hovered {
  min-height: 420px;
}

/* Enhanced hover state for better visibility */
.product-card-enhanced-hover {
  position: relative;
  z-index: 40;
  transform: scale(1.1);
  box-shadow:
    0 25px 50px rgba(149, 99, 88, 0.4),
    0 0 0 1px rgba(149, 99, 88, 0.2);
}

/* Smooth grid transitions */
.product-grid-adaptive {
  transition: grid-template-columns 0.3s ease-in-out;
}

/* List view hover enhancements */
.product-list-item {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-list-item.hovered {
  transform: scale(1.02);
  margin-bottom: 1rem;
}

/* Height-only scaling for product cards */
.product-card-height-scale {
  transform: scaleY(1.1);
  transform-origin: center top;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.product-card-height-scale-list {
  transform: scaleY(1.05);
  transform-origin: center top;
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Ensure smooth height transitions */
.product-card-container {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-origin: center top;
}

/* Dynamic height expansion for product cards */
.product-card-dynamic-height {
  height: auto !important;
  min-height: 400px;
  transition: min-height 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.product-card-dynamic-height.hovered {
  min-height: 500px;
}

.product-card-list-dynamic-height {
  height: auto !important;
  min-height: 200px;
  transition: min-height 0.7s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.product-card-list-dynamic-height.hovered {
  min-height: 250px;
}

/* Grid layout adjustments for dynamic heights */
.product-grid-auto-rows {
  grid-auto-rows: auto;
  align-items: start;
}

/* Natural height expansion for product cards */
.product-card-natural-height {
  height: auto;
  min-height: fit-content;
  transition: all 0.3s ease-in-out;
}

.product-card-hover-expand {
  height: auto;
  min-height: fit-content;
}

/* Professional Shadow Effects */
.shadow-glow {
  box-shadow: 0 0 20px rgba(149, 99, 88, 0.3);
}

.shadow-glow:hover {
  box-shadow: 0 0 30px rgba(149, 99, 88, 0.5);
}

/* Professional Shimmer Effect */
.shimmer {
  background: linear-gradient(90deg,
    transparent,
    rgba(224, 224, 224, 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* === PROFESSIONAL UTILITIES === */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-portrait {
  aspect-ratio: 3 / 4;
}

/* === PROFESSIONAL TYPOGRAPHY === */
.heading-primary {
  font-family: 'Poppins', ui-sans-serif, system-ui, sans-serif;
  font-weight: 700;
  color: var(--text-primary);
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.heading-secondary {
  font-family: 'Poppins', ui-sans-serif, system-ui, sans-serif;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
}

.text-body {
  font-family: 'Poppins', ui-sans-serif, system-ui, sans-serif;
  font-weight: 400;
  color: var(--text-primary);
  line-height: 1.6;
}

.text-caption {
  font-family: 'Poppins', ui-sans-serif, system-ui, sans-serif;
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.875rem;
  line-height: 1.4;
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 768px) {
  .card {
    padding: 1rem;
    border-radius: 8px;
  }

  .btn-primary, .btn-secondary {
    padding: 0.625rem 1.25rem;
    font-size: 0.8rem;
  }

  .input {
    padding: 0.75rem 0.875rem;
    font-size: 0.8rem;
  }
}

/* === ELEGANT CARD ENHANCEMENTS === */
.elegant-card {
  position: relative;
  overflow: hidden;
}

.elegant-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(149, 99, 88, 0.1), transparent);
  transition: left 0.8s ease-in-out;
}

.elegant-card:hover::before {
  left: 100%;
}

/* Elegant border triangle */
.border-triangle {
  width: 0;
  height: 0;
  border-style: solid;
}

.border-triangle-tr {
  border-width: 0 16px 16px 0;
  border-color: transparent currentColor transparent transparent;
}

/* Enhanced backdrop blur */
.backdrop-blur-elegant {
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
}

/* Elegant glow effects */
.glow-terra-cotta {
  box-shadow: 0 0 20px rgba(149, 99, 88, 0.3);
}

.glow-terra-cotta:hover {
  box-shadow: 0 0 30px rgba(149, 99, 88, 0.5);
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus styles for accessibility */
.focus-ring:focus {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border-width: 2px;
  }

  .btn-primary, .btn-secondary {
    border-width: 2px;
  }
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Global button cursor pointer */
  button,
  [role="button"],
  input[type="button"],
  input[type="submit"],
  input[type="reset"],
  .btn,
  .btn-primary,
  .btn-secondary,
  .btn-ghost {
    cursor: pointer;
  }

  /* Disabled buttons should not have pointer cursor */
  button:disabled,
  [role="button"]:disabled,
  input[type="button"]:disabled,
  input[type="submit"]:disabled,
  input[type="reset"]:disabled,
  .btn:disabled,
  .btn-primary:disabled,
  .btn-secondary:disabled,
  .btn-ghost:disabled {
    cursor: not-allowed;
  }
}
