import { NextResponse } from 'next/server';
import { verifyAdminAuth } from '@/lib/auth/admin';

export async function GET() {
  try {
    const admin = await verifyAdminAuth();

    if (!admin) {
      return NextResponse.json(
        { error: 'Not authenticated or not an admin' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      success: true,
      admin,
    });
  } catch (error) {
    console.error('Admin auth verification error:', error);
    return NextResponse.json(
      { error: 'Authentication verification failed' },
      { status: 500 }
    );
  }
}
