import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    const { email, password, full_name, phone_number, address } = await request.json();

    if (!email || !password || !full_name) {
      return NextResponse.json(
        { error: 'Email, password, and full name are required' },
        { status: 400 }
      );
    }

    const supabase = createSupabaseAdminClient();

    // Create user in Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: full_name,
          phone_number: phone_number || null,
          address: address || null
        }
      }
    });

    if (authError) {
      console.error('Auth signup error:', authError);
      return NextResponse.json(
        { error: authError.message },
        { status: 400 }
      );
    }

    if (authData.user) {
      console.log('Auth user created:', authData.user.id);

      // Insert user data into our users table
      const userRecord = {
        id: authData.user.id,
        email,
        password_hash: 'supabase_auth', // Using Supabase Auth, placeholder value
        full_name: full_name,
        phone: phone_number || null,
        address: address || null,
        role: 'user' as const,
        is_admin: false // Default to non-admin for new users
      };

      console.log('Inserting user record:', userRecord);

      const { data: insertedUser, error: dbError } = await supabase
        .from('users')
        .insert(userRecord)
        .select();

      if (dbError) {
        console.error('Database insert error:', dbError);
        // If database insert fails, we should clean up the auth user
        try {
          await supabase.auth.admin.deleteUser(authData.user.id);
        } catch (cleanupError) {
          console.error('Failed to cleanup auth user:', cleanupError);
        }
        return NextResponse.json(
          { error: 'Failed to create user profile' },
          { status: 500 }
        );
      }

      console.log('User record inserted successfully:', insertedUser);

      // Link any existing inquiries made with this email to the new user account
      try {
        await supabase
          .from('inquiries')
          .update({ user_id: authData.user.id })
          .eq('contact_details->email', email)
          .is('user_id', null);

        console.log('Linked existing inquiries to new user account');
      } catch (linkError) {
        console.error('Error linking existing inquiries:', linkError);
        // Don't fail the signup if inquiry linking fails
      }
    }

    return NextResponse.json({
      message: 'User created successfully',
      user: authData.user,
      session: authData.session
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
