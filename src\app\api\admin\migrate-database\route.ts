import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';
import { generateSlug } from '@/lib/product-utils';

export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseAdminClient();
    const results = [];

    // Step 1: First, let's check what products exist and their current structure
    const { data: sampleProduct, error: sampleError } = await supabase
      .from('products')
      .select('*')
      .limit(1)
      .single();

    if (sampleError) {
      console.error('Error fetching sample product:', sampleError);
      results.push('Error fetching sample product: ' + sampleError.message);
    } else {
      results.push('Sample product structure: ' + JSON.stringify(Object.keys(sampleProduct || {})));
    }

    // Step 3: Generate slugs for products that don't have them
    try {
      const { data: products, error: fetchError } = await supabase
        .from('products')
        .select('id, name, slug')
        .or('slug.is.null,slug.eq.');

      if (fetchError) {
        console.error('Error fetching products without slugs:', fetchError);
        results.push('Error fetching products: ' + fetchError.message);
      } else if (products && products.length > 0) {
        const updates = [];
        const slugMap = new Map();

        for (const product of products) {
          let baseSlug = generateSlug(product.name);
          let finalSlug = baseSlug;
          let counter = 1;

          // Ensure slug uniqueness
          while (slugMap.has(finalSlug)) {
            finalSlug = `${baseSlug}-${counter}`;
            counter++;
          }

          // Check if slug already exists in database
          const { data: existingProduct } = await supabase
            .from('products')
            .select('id')
            .eq('slug', finalSlug)
            .neq('id', product.id)
            .single();

          while (existingProduct) {
            finalSlug = `${baseSlug}-${counter}`;
            counter++;
            
            const { data: checkAgain } = await supabase
              .from('products')
              .select('id')
              .eq('slug', finalSlug)
              .neq('id', product.id)
              .single();
            
            if (!checkAgain) break;
          }

          slugMap.set(finalSlug, product.id);
          updates.push({
            id: product.id,
            name: product.name,
            slug: finalSlug
          });
        }

        // Update products with generated slugs
        const updatePromises = updates.map(update => 
          supabase
            .from('products')
            .update({ slug: update.slug })
            .eq('id', update.id)
        );

        const updateResults = await Promise.all(updatePromises);
        const errors = updateResults.filter(result => result.error);
        
        if (errors.length > 0) {
          results.push(`Generated slugs with ${errors.length} errors`);
        } else {
          results.push(`Successfully generated ${updates.length} slugs`);
        }
      } else {
        results.push('All products already have slugs');
      }
    } catch (error) {
      console.error('Error with slug generation:', error);
      results.push('Error with slug generation: ' + error);
    }

    return NextResponse.json({
      message: 'Database migration completed',
      results
    });

  } catch (error) {
    console.error('Unexpected error in database migration:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error },
      { status: 500 }
    );
  }
}
