import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseServerClient } from '@/lib/supabase/server';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await getSupabaseServerClient();

    // Get authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('Authentication error:', authError);
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
    }

    console.log('Authenticated user:', user.id);
    const { id: inquiryId } = await params;
    console.log('Fetching inquiry:', inquiryId);

    // Fetch inquiry with product details
    const { data: inquiry, error } = await supabase
      .from('inquiries')
      .select(`
        *,
        product:products (
          id,
          name,
          brand,
          model,
          price,
          images,
          description,
          specifications,
          features,
          categories (
            id,
            name,
            slug
          )
        )
      `)
      .eq('id', inquiryId)
      .eq('user_id', user.id) // Ensure user can only access their own inquiries
      .single();

    if (error) {
      console.error('Error fetching inquiry:', error);
      console.error('Error details:', {
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint
      });
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Inquiry not found' }, { status: 404 });
      }
      return NextResponse.json({
        error: 'Failed to fetch inquiry',
        details: error.message
      }, { status: 500 });
    }

    return NextResponse.json({ inquiry });
  } catch (error) {
    console.error('Error in inquiry detail API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
