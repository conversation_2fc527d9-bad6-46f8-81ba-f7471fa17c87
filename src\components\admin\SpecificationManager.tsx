'use client';

import { useState, useEffect, useRef } from 'react';
import { Plus, X, Edit2, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface SpecificationItem {
  id: string;
  key: string;
  value: string;
}

interface SpecificationManagerProps {
  title: string;
  description?: string;
  initialData?: Record<string, string>;
  onChange: (data: Record<string, string>) => void;
  placeholder?: {
    key: string;
    value: string;
  };
}

export function SpecificationManager({
  title,
  description,
  initialData = {},
  onChange,
  placeholder = { key: 'Screen Size', value: '14 inch' }
}: SpecificationManagerProps) {
  // Default specification keys
  const defaultKeys = ['CPU', 'RAM', 'Storage', 'OS', 'Screen Size', 'Color', 'GPU'];

  const [specifications, setSpecifications] = useState<SpecificationItem[]>(() => {
    // Start with existing data
    const existingSpecs = Object.entries(initialData).map(([key, value], index) => ({
      id: `spec-${index}`,
      key,
      value: String(value)
    }));

    // Add default keys that don't already exist
    const existingKeys = existingSpecs.map(spec => spec.key);
    const missingDefaults = defaultKeys.filter(key => !existingKeys.includes(key));

    const defaultSpecs = missingDefaults.map((key, index) => ({
      id: `default-spec-${index}`,
      key,
      value: ''
    }));

    return [...existingSpecs, ...defaultSpecs];
  });
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newKey, setNewKey] = useState('');
  const [newValue, setNewValue] = useState('');
  const [isAdding, setIsAdding] = useState(false);

  // Update parent component when specifications change
  useEffect(() => {
    const specObject = specifications.reduce((acc, spec) => {
      if (spec.key.trim() && spec.value.trim()) {
        acc[spec.key.trim()] = spec.value.trim();
      }
      return acc;
    }, {} as Record<string, string>);
    onChange(specObject);
  }, [specifications]);

  const addSpecification = () => {
    if (newKey.trim() && newValue.trim()) {
      const newSpec: SpecificationItem = {
        id: `spec-${Date.now()}`,
        key: newKey.trim(),
        value: newValue.trim()
      };
      setSpecifications(prev => [...prev, newSpec]);
      setNewKey('');
      setNewValue('');
      setIsAdding(false);
    }
  };

  const updateSpecification = (id: string, key: string, value: string) => {
    setSpecifications(prev =>
      prev.map(spec =>
        spec.id === id ? { ...spec, key: key.trim(), value: value.trim() } : spec
      )
    );
    setEditingId(null);
  };

  const deleteSpecification = (id: string) => {
    setSpecifications(prev => prev.filter(spec => spec.id !== id));
  };

  const startEditing = (id: string) => {
    setEditingId(id);
    setIsAdding(false);
  };

  const cancelEditing = () => {
    setEditingId(null);
    setIsAdding(false);
    setNewKey('');
    setNewValue('');
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-semibold text-gray-800 mb-2">
          {title}
        </label>
        {description && (
          <p className="text-sm text-gray-600 mb-4">{description}</p>
        )}
      </div>

      {/* Existing Specifications */}
      <div className="space-y-3">
        {specifications.map((spec) => (
          <SpecificationRow
            key={spec.id}
            specification={spec}
            isEditing={editingId === spec.id}
            onEdit={() => startEditing(spec.id)}
            onSave={(key, value) => updateSpecification(spec.id, key, value)}
            onCancel={cancelEditing}
            onDelete={() => deleteSpecification(spec.id)}
          />
        ))}
      </div>

      {/* Add New Specification */}
      {isAdding ? (
        <div className="border-2 border-dashed border-[#956358] rounded-lg p-4 bg-orange-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <input
              type="text"
              placeholder={placeholder.key}
              value={newKey}
              onChange={(e) => setNewKey(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-[#956358] focus:border-[#956358]"
              autoFocus
            />
            <input
              type="text"
              placeholder={placeholder.value}
              value={newValue}
              onChange={(e) => setNewValue(e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-[#956358] focus:border-[#956358]"
              onKeyPress={(e) => e.key === 'Enter' && addSpecification()}
            />
          </div>
          <div className="flex justify-end space-x-2 mt-3">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={cancelEditing}
            >
              Cancel
            </Button>
            <Button
              type="button"
              size="sm"
              onClick={addSpecification}
              disabled={!newKey.trim() || !newValue.trim()}
              className="bg-[#956358] hover:bg-[#7d5249]"
            >
              <Check className="h-4 w-4 mr-1" />
              Add
            </Button>
          </div>
        </div>
      ) : (
        <Button
          type="button"
          variant="outline"
          onClick={() => setIsAdding(true)}
          className="w-full border-dashed border-2 border-gray-300 hover:border-[#956358] hover:bg-orange-50 text-gray-600 hover:text-[#956358]"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add {title.slice(0, -1)}
        </Button>
      )}

      {specifications.length === 0 && !isAdding && (
        <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
          <AlertCircle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm">No {title.toLowerCase()} added yet</p>
          <p className="text-xs text-gray-400 mt-1">Click "Add {title.slice(0, -1)}" to get started</p>
        </div>
      )}
    </div>
  );
}

interface SpecificationRowProps {
  specification: SpecificationItem;
  isEditing: boolean;
  onEdit: () => void;
  onSave: (key: string, value: string) => void;
  onCancel: () => void;
  onDelete: () => void;
}

function SpecificationRow({
  specification,
  isEditing,
  onEdit,
  onSave,
  onCancel,
  onDelete
}: SpecificationRowProps) {
  const [editKey, setEditKey] = useState(specification.key);
  const [editValue, setEditValue] = useState(specification.value);

  useEffect(() => {
    if (isEditing) {
      setEditKey(specification.key);
      setEditValue(specification.value);
    }
  }, [isEditing, specification]);

  const handleSave = () => {
    if (editKey.trim() && editValue.trim()) {
      onSave(editKey, editValue);
    }
  };

  if (isEditing) {
    return (
      <div className="border-2 border-[#956358] rounded-lg p-4 bg-orange-50">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <input
            type="text"
            value={editKey}
            onChange={(e) => setEditKey(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-[#956358] focus:border-[#956358]"
            autoFocus
          />
          <input
            type="text"
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-[#956358] focus:border-[#956358]"
            onKeyPress={(e) => e.key === 'Enter' && handleSave()}
          />
        </div>
        <div className="flex justify-end space-x-2 mt-3">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            type="button"
            size="sm"
            onClick={handleSave}
            disabled={!editKey.trim() || !editValue.trim()}
            className="bg-[#956358] hover:bg-[#7d5249]"
          >
            <Check className="h-4 w-4 mr-1" />
            Save
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
      <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <span className="text-sm font-medium text-gray-900">{specification.key}</span>
        </div>
        <div>
          <span className="text-sm text-gray-600">{specification.value}</span>
        </div>
      </div>
      <div className="flex space-x-2 ml-4">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onEdit}
          className="text-gray-600 hover:text-[#956358]"
        >
          <Edit2 className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onDelete}
          className="text-gray-600 hover:text-red-600"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
