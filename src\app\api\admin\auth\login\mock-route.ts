import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';

const JWT_SECRET = process.env.JWT_SECRET || 'development_jwt_secret_key_change_in_production';

interface AdminUser {
  id: string;
  email: string;
  password_hash: string;
  role_id: string;
  full_name: string;
  is_active: boolean;
}

// Mock database for development testing
const mockDatabase = {
  roles: [
    {
      id: 'b4f1c8d2-9a7e-4f2b-8c3d-6e5f9a8b7c6d',
      name: 'Super Admin',
      permissions: [
        {"resource": "products", "actions": ["create", "read", "update", "delete"]},
        {"resource": "inquiries", "actions": ["create", "read", "update", "delete"]},
        {"resource": "users", "actions": ["create", "read", "update", "delete"]},
        {"resource": "roles", "actions": ["create", "read", "update", "delete"]},
        {"resource": "chat", "actions": ["create", "read", "update", "delete"]},
        {"resource": "inventory", "actions": ["create", "read", "update", "delete"]}
      ]
    },
    {
      id: 'd6h3e0f4-bc9g-6h4d-ae5f-8g7h1c0d9e8f',
      name: 'Support',
      permissions: [
        {"resource": "inquiries", "actions": ["read", "update"]},
        {"resource": "chat", "actions": ["create", "read", "update"]}
      ]
    }
  ],
  admin_users: [] as AdminUser[]
};

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Initialize mock users if empty
    if (mockDatabase.admin_users.length === 0) {
      const adminPassword = await bcrypt.hash('admin123', 10);
      const supportPassword = await bcrypt.hash('support123', 10);

      mockDatabase.admin_users = [
        {
          id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
          email: '<EMAIL>',
          password_hash: adminPassword,
          role_id: 'b4f1c8d2-9a7e-4f2b-8c3d-6e5f9a8b7c6d',
          full_name: 'System Administrator',
          is_active: true
        },
        {
          id: 'b2c3d4e5-f6g7-8h9i-0j1k-l2m3n4o5p6q7',
          email: '<EMAIL>',
          password_hash: supportPassword,
          role_id: 'd6h3e0f4-bc9g-6h4d-ae5f-8g7h1c0d9e8f',
          full_name: 'Support Admin',
          is_active: true
        }
      ];
    }

    // Find admin user by email
    const adminUser = mockDatabase.admin_users.find(user => user.email === email);

    if (!adminUser) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    if (!adminUser.is_active) {
      return NextResponse.json(
        { error: 'Account is deactivated' },
        { status: 401 }
      );
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, adminUser.password_hash);

    if (!isValidPassword) {
      return NextResponse.json(
        { error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Find user's role
    const role = mockDatabase.roles.find(r => r.id === adminUser.role_id);

    if (!role) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 500 }
      );
    }

    // Create JWT token
    const token = jwt.sign(
      {
        adminId: adminUser.id,
        email: adminUser.email,
        role: role.name,
        roleId: role.id
      },
      JWT_SECRET,
      { expiresIn: '8h' }
    );

    // Set HTTP-only cookie
    const cookieStore = await cookies();
    cookieStore.set('admin-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      maxAge: 60 * 60 * 8 // 8 hours
    });

    // Return success response with user data
    return NextResponse.json({
      success: true,
      admin: {
        id: adminUser.id,
        email: adminUser.email,
        fullName: adminUser.full_name,
        role: {
          id: role.id,
          name: role.name,
          permissions: role.permissions
        }
      }
    });

  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Authentication failed' },
      { status: 500 }
    );
  }
}
