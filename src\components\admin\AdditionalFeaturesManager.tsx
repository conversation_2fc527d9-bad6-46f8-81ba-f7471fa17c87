'use client';

import { useState, useEffect, useRef } from 'react';
import { Plus, X, Edit2, Check, AlertCircle, ToggleLeft, ToggleRight } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface AdditionalFeature {
  id: string;
  key: string;
  value: string | boolean;
  type: 'text' | 'boolean';
}

interface AdditionalFeaturesManagerProps {
  title: string;
  description?: string;
  initialData?: Record<string, any>;
  onChange: (data: Record<string, any>) => void;
  placeholder?: {
    key: string;
    value: string;
  };
}

export function AdditionalFeaturesManager({
  title,
  description,
  initialData = {},
  onChange,
  placeholder = { key: 'Webcam', value: 'HD 720p' }
}: AdditionalFeaturesManagerProps) {
  const [features, setFeatures] = useState<AdditionalFeature[]>(() =>
    Object.entries(initialData).map(([key, value], index) => ({
      id: `feature-${index}`,
      key,
      value,
      type: typeof value === 'boolean' ? 'boolean' as const : 'text' as const
    }))
  );
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newKey, setNewKey] = useState('');
  const [newValue, setNewValue] = useState('');
  const [newType, setNewType] = useState<'text' | 'boolean'>('text');
  const [isAdding, setIsAdding] = useState(false);

  // Update parent component when features change
  useEffect(() => {
    const featureObject = features.reduce((acc, feature) => {
      if (feature.key.trim()) {
        acc[feature.key.trim()] = feature.value;
      }
      return acc;
    }, {} as Record<string, any>);
    onChange(featureObject);
  }, [features]);

  const addFeature = () => {
    if (newKey.trim()) {
      const value = newType === 'boolean' ? false : newValue.trim();
      const newFeature: AdditionalFeature = {
        id: `feature-${Date.now()}`,
        key: newKey.trim(),
        value,
        type: newType
      };
      setFeatures(prev => [...prev, newFeature]);
      setNewKey('');
      setNewValue('');
      setNewType('text');
      setIsAdding(false);
    }
  };

  const updateFeature = (id: string, key: string, value: string | boolean, type: 'text' | 'boolean') => {
    setFeatures(prev =>
      prev.map(feature =>
        feature.id === id ? { ...feature, key: key.trim(), value, type } : feature
      )
    );
    setEditingId(null);
  };

  const deleteFeature = (id: string) => {
    setFeatures(prev => prev.filter(feature => feature.id !== id));
  };

  const startEditing = (id: string) => {
    setEditingId(id);
    setIsAdding(false);
  };

  const cancelEditing = () => {
    setEditingId(null);
    setIsAdding(false);
    setNewKey('');
    setNewValue('');
    setNewType('text');
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-semibold text-gray-800 mb-2">
          {title}
        </label>
        {description && (
          <p className="text-sm text-gray-600 mb-4">{description}</p>
        )}
      </div>

      {/* Existing Features */}
      <div className="space-y-3">
        {features.map((feature) => (
          <AdditionalFeatureRow
            key={feature.id}
            feature={feature}
            isEditing={editingId === feature.id}
            onEdit={() => startEditing(feature.id)}
            onSave={(key, value, type) => updateFeature(feature.id, key, value, type)}
            onCancel={cancelEditing}
            onDelete={() => deleteFeature(feature.id)}
          />
        ))}
      </div>

      {/* Add New Feature */}
      {isAdding ? (
        <div className="border-2 border-dashed border-[#956358] rounded-lg p-4 bg-orange-50">
          <div className="space-y-3">
            <input
              type="text"
              placeholder={placeholder.key}
              value={newKey}
              onChange={(e) => setNewKey(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-[#956358] focus:border-[#956358]"
              autoFocus
            />
            
            <div className="flex items-center space-x-4">
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="newType"
                  value="text"
                  checked={newType === 'text'}
                  onChange={(e) => setNewType(e.target.value as 'text')}
                  className="text-[#956358] focus:ring-[#956358]"
                />
                <span className="text-sm text-gray-700">Text Value</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="radio"
                  name="newType"
                  value="boolean"
                  checked={newType === 'boolean'}
                  onChange={(e) => setNewType(e.target.value as 'boolean')}
                  className="text-[#956358] focus:ring-[#956358]"
                />
                <span className="text-sm text-gray-700">Yes/No</span>
              </label>
            </div>

            {newType === 'text' && (
              <input
                type="text"
                placeholder={placeholder.value}
                value={newValue}
                onChange={(e) => setNewValue(e.target.value)}
                className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-[#956358] focus:border-[#956358]"
                onKeyPress={(e) => e.key === 'Enter' && addFeature()}
              />
            )}
          </div>
          
          <div className="flex justify-end space-x-2 mt-3">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={cancelEditing}
            >
              Cancel
            </Button>
            <Button
              type="button"
              size="sm"
              onClick={addFeature}
              disabled={!newKey.trim() || (newType === 'text' && !newValue.trim())}
              className="bg-[#956358] hover:bg-[#7d5249]"
            >
              <Check className="h-4 w-4 mr-1" />
              Add
            </Button>
          </div>
        </div>
      ) : (
        <Button
          type="button"
          variant="outline"
          onClick={() => setIsAdding(true)}
          className="w-full border-dashed border-2 border-gray-300 hover:border-[#956358] hover:bg-orange-50 text-gray-600 hover:text-[#956358]"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Additional Feature
        </Button>
      )}

      {features.length === 0 && !isAdding && (
        <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
          <AlertCircle className="h-8 w-8 mx-auto mb-2 text-gray-400" />
          <p className="text-sm">No {title.toLowerCase()} added yet</p>
          <p className="text-xs text-gray-400 mt-1">Click "Add Additional Feature" to get started</p>
        </div>
      )}
    </div>
  );
}

interface AdditionalFeatureRowProps {
  feature: AdditionalFeature;
  isEditing: boolean;
  onEdit: () => void;
  onSave: (key: string, value: string | boolean, type: 'text' | 'boolean') => void;
  onCancel: () => void;
  onDelete: () => void;
}

function AdditionalFeatureRow({
  feature,
  isEditing,
  onEdit,
  onSave,
  onCancel,
  onDelete
}: AdditionalFeatureRowProps) {
  const [editKey, setEditKey] = useState(feature.key);
  const [editValue, setEditValue] = useState(feature.type === 'text' ? String(feature.value) : '');
  const [editBoolValue, setEditBoolValue] = useState(feature.type === 'boolean' ? Boolean(feature.value) : false);
  const [editType, setEditType] = useState(feature.type);

  useEffect(() => {
    if (isEditing) {
      setEditKey(feature.key);
      setEditType(feature.type);
      if (feature.type === 'text') {
        setEditValue(String(feature.value));
      } else {
        setEditBoolValue(Boolean(feature.value));
      }
    }
  }, [isEditing, feature]);

  const handleSave = () => {
    if (editKey.trim()) {
      const value = editType === 'boolean' ? editBoolValue : editValue.trim();
      if (editType === 'text' && !editValue.trim()) return;
      onSave(editKey, value, editType);
    }
  };

  if (isEditing) {
    return (
      <div className="border-2 border-[#956358] rounded-lg p-4 bg-orange-50">
        <div className="space-y-3">
          <input
            type="text"
            value={editKey}
            onChange={(e) => setEditKey(e.target.value)}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-[#956358] focus:border-[#956358]"
            autoFocus
          />
          
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name={`editType-${feature.id}`}
                value="text"
                checked={editType === 'text'}
                onChange={(e) => setEditType(e.target.value as 'text')}
                className="text-[#956358] focus:ring-[#956358]"
              />
              <span className="text-sm text-gray-700">Text Value</span>
            </label>
            <label className="flex items-center space-x-2">
              <input
                type="radio"
                name={`editType-${feature.id}`}
                value="boolean"
                checked={editType === 'boolean'}
                onChange={(e) => setEditType(e.target.value as 'boolean')}
                className="text-[#956358] focus:ring-[#956358]"
              />
              <span className="text-sm text-gray-700">Yes/No</span>
            </label>
          </div>

          {editType === 'text' ? (
            <input
              type="text"
              value={editValue}
              onChange={(e) => setEditValue(e.target.value)}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-[#956358] focus:border-[#956358]"
              onKeyPress={(e) => e.key === 'Enter' && handleSave()}
            />
          ) : (
            <div className="flex items-center space-x-3">
              <button
                type="button"
                onClick={() => setEditBoolValue(!editBoolValue)}
                className="flex items-center space-x-2 text-sm"
              >
                {editBoolValue ? (
                  <ToggleRight className="h-6 w-6 text-[#956358]" />
                ) : (
                  <ToggleLeft className="h-6 w-6 text-gray-400" />
                )}
                <span className={editBoolValue ? 'text-[#956358] font-medium' : 'text-gray-500'}>
                  {editBoolValue ? 'Yes' : 'No'}
                </span>
              </button>
            </div>
          )}
        </div>
        
        <div className="flex justify-end space-x-2 mt-3">
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button
            type="button"
            size="sm"
            onClick={handleSave}
            disabled={!editKey.trim() || (editType === 'text' && !editValue.trim())}
            className="bg-[#956358] hover:bg-[#7d5249]"
          >
            <Check className="h-4 w-4 mr-1" />
            Save
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-between p-4 bg-white border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
      <div className="flex-1 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <span className="text-sm font-medium text-gray-900">{feature.key}</span>
        </div>
        <div>
          {feature.type === 'boolean' ? (
            <span className={`text-sm font-medium ${feature.value ? 'text-green-600' : 'text-gray-500'}`}>
              {feature.value ? 'Yes' : 'No'}
            </span>
          ) : (
            <span className="text-sm text-gray-600">{String(feature.value)}</span>
          )}
        </div>
      </div>
      <div className="flex space-x-2 ml-4">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onEdit}
          className="text-gray-600 hover:text-[#956358]"
        >
          <Edit2 className="h-4 w-4" />
        </Button>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={onDelete}
          className="text-gray-600 hover:text-red-600"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
