import { NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import bcrypt from 'bcryptjs';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST() {
  try {
    // This is a development-only endpoint for setting up initial admin users
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    // Create default roles if they don't exist
    const roles = [
      {
        id: 'b4f1c8d2-9a7e-4f2b-8c3d-6e5f9a8b7c6d',
        name: 'Super Admin',
        permissions: [
          {"resource": "products", "actions": ["create", "read", "update", "delete"]},
          {"resource": "inquiries", "actions": ["create", "read", "update", "delete"]},
          {"resource": "users", "actions": ["create", "read", "update", "delete"]},
          {"resource": "roles", "actions": ["create", "read", "update", "delete"]},
          {"resource": "chat", "actions": ["create", "read", "update", "delete"]},
          {"resource": "inventory", "actions": ["create", "read", "update", "delete"]}
        ]
      },
      {
        id: 'd6h3e0f4-bc9g-6h4d-ae5f-8g7h1c0d9e8f',
        name: 'Support',
        permissions: [
          {"resource": "inquiries", "actions": ["read", "update"]},
          {"resource": "chat", "actions": ["create", "read"]},
          {"resource": "products", "actions": ["read"]}
        ]
      }
    ];

    for (const role of roles) {
      await supabase
        .from('roles')
        .upsert(role, { onConflict: 'id' });
    }

    // Create default admin users
    const adminPassword = await bcrypt.hash('admin123', 10);
    const supportPassword = await bcrypt.hash('support123', 10);

    const adminUsers = [
      {
        id: 'a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6',
        email: '<EMAIL>',
        password_hash: adminPassword,
        role_id: 'b4f1c8d2-9a7e-4f2b-8c3d-6e5f9a8b7c6d',
        full_name: 'System Administrator',
        is_active: true
      },
      {
        id: 'b2c3d4e5-f6g7-8h9i-0j1k-l2m3n4o5p6q7',
        email: '<EMAIL>',
        password_hash: supportPassword,
        role_id: 'd6h3e0f4-bc9g-6h4d-ae5f-8g7h1c0d9e8f',
        full_name: 'Support Admin',
        is_active: true
      }
    ];

    for (const user of adminUsers) {
      await supabase
        .from('admin_users')
        .upsert(user, { onConflict: 'email' });
    }

    return NextResponse.json({
      message: 'Admin users and roles created successfully',
      users: [
        { email: '<EMAIL>', password: 'admin123', role: 'Super Admin' },
        { email: '<EMAIL>', password: 'support123', role: 'Support' }
      ]
    });

  } catch (error) {
    console.error('Setup error:', error);
    return NextResponse.json(
      { error: 'Failed to setup admin users' },
      { status: 500 }
    );
  }
}
