import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';
import { verifyAdminAuth } from '@/lib/auth/admin';

export async function GET(request: NextRequest) {
  try {
    // Use the new unified admin authentication
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createSupabaseAdminClient();
    const searchParams = request.nextUrl.searchParams;
    
    // Parse query parameters
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const brand = searchParams.get('brand');
    const status = searchParams.get('status') || 'all';
    const minPrice = searchParams.get('minPrice');
    const maxPrice = searchParams.get('maxPrice');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sortBy = searchParams.get('sortBy') || 'created_at';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build query - try with full schema first, fallback to basic
    let query = supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          slug
        ),
        brands (
          id,
          name,
          slug
        ),
        conditions (
          id,
          name,
          slug
        )
      `);

    // Apply filters
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,brands.name.ilike.%${search}%`);
    }

    if (category && category !== 'all') {
      query = query.eq('category_id', category);
    }

    if (brand && brand !== 'all') {
      query = query.eq('brand_id', brand);
    }

    if (status !== 'all') {
      if (status === 'active') {
        query = query.eq('status', 'active');
      } else {
        query = query.eq('status', status);
      }
    }

    if (minPrice) {
      query = query.gte('price', parseFloat(minPrice));
    }

    if (maxPrice) {
      query = query.lte('price', parseFloat(maxPrice));
    }

    // Apply sorting with stable secondary sort
    query = query.order(sortBy, { ascending: sortOrder === 'asc' });

    // Add secondary sort by ID to ensure stable pagination
    if (sortBy !== 'id') {
      query = query.order('id', { ascending: true });
    }

    // Apply pagination
    const from = (page - 1) * limit;
    const to = from + limit - 1;
    query = query.range(from, to);

    const { data: products, error } = await query;

    if (error) {
      console.error('Error fetching products:', error);
      return NextResponse.json(
        { error: 'Failed to fetch products' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('products')
      .select('*', { count: 'exact', head: true });

    if (search) {
      countQuery = countQuery.or(`name.ilike.%${search}%,brand.ilike.%${search}%,description.ilike.%${search}%`);
    }

    if (category && category !== 'all') {
      countQuery = countQuery.eq('category_id', category);
    }

    if (brand && brand !== 'all') {
      countQuery = countQuery.eq('brand_id', brand);
    }

    if (status !== 'all') {
      if (status === 'active') {
        countQuery = countQuery.eq('status', 'active');
      } else {
        countQuery = countQuery.eq('status', status);
      }
    }

    if (minPrice) {
      countQuery = countQuery.gte('price', parseFloat(minPrice));
    }

    if (maxPrice) {
      countQuery = countQuery.lte('price', parseFloat(maxPrice));
    }

    const { count: totalCount } = await countQuery;

    return NextResponse.json({
      products,
      pagination: {
        page,
        limit,
        total: totalCount || 0,
        totalPages: Math.ceil((totalCount || 0) / limit)
      }
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Use the new unified admin authentication
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createSupabaseAdminClient();
    const productData = await request.json();

    // Validate required fields
    const { name, brand_id, condition_id, price, category_id } = productData;
    if (!name || !price || !category_id) {
      return NextResponse.json(
        { error: 'Name, price, and category are required' },
        { status: 400 }
      );
    }

    // Remove fields that don't exist in the database or need special handling
    const { model_name, ...cleanProductData } = productData;

    // Map model_name to model if it exists
    if (productData.model_name) {
      cleanProductData.model = productData.model_name;
    }

    // Include stock fields
    cleanProductData.stock_quantity = productData.stock_quantity || 0;
    cleanProductData.low_stock_threshold = productData.low_stock_threshold || 5;

    // Set is_active based on status (for backward compatibility)
    // Only 'active' status should be visible to customers
    cleanProductData.is_active = productData.status === 'active';

    // Generate slug from name
    const slug = name.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    // Create product
    const { data: product, error } = await supabase
      .from('products')
      .insert({
        ...cleanProductData,
        slug,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select(`
        *,
        categories (
          id,
          name,
          slug
        ),
        brands (
          id,
          name,
          slug
        ),
        conditions (
          id,
          name,
          slug
        )
      `)
      .single();

    if (error) {
      console.error('Error creating product:', error);
      return NextResponse.json(
        { error: 'Failed to create product' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Product created successfully',
      product
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
