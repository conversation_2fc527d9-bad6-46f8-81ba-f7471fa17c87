import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';
import { verifyAdminAuth } from '@/lib/auth/admin';

// Helper function to delete images from Supabase storage
async function deleteProductImages(imageUrls: string[], supabase: any) {
  if (!imageUrls || imageUrls.length === 0) {
    return { success: true, deletedCount: 0 };
  }

  try {
    // Extract file paths from image URLs
    const imagePaths = imageUrls.map((imageUrl: string) => {
      // Extract the file path from the Supabase storage URL
      // URL format: https://[project].supabase.co/storage/v1/object/public/product-images/[path]
      const urlParts = imageUrl.split('/storage/v1/object/public/product-images/');
      return urlParts.length > 1 ? urlParts[1] : null;
    }).filter(Boolean); // Remove null values

    if (imagePaths.length === 0) {
      return { success: true, deletedCount: 0 };
    }

    // Delete images from storage
    const { error: storageError } = await supabase.storage
      .from('product-images')
      .remove(imagePaths);

    if (storageError) {
      console.error('Error deleting images from storage:', storageError);
      return { success: false, error: storageError, deletedCount: 0 };
    }

    console.log(`Successfully deleted ${imagePaths.length} images from storage`);
    return { success: true, deletedCount: imagePaths.length };
  } catch (error) {
    console.error('Error processing image deletion:', error);
    return { success: false, error, deletedCount: 0 };
  }
}

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET single product
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const supabase = createSupabaseAdminClient();

    const { data: product, error } = await supabase
      .from('products')
      .select(`
        *,
        categories (
          id,
          name,
          slug
        ),
        brands (
          id,
          name,
          slug
        ),
        conditions (
          id,
          name,
          slug
        )
      `)
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching product:', error);
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ product });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// UPDATE product
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const supabase = createSupabaseAdminClient();
    const productData = await request.json();

    // Validate required fields
    const { name, brand_id, condition_id, price, category_id, product_id } = productData;
    if (!name || !brand_id || !condition_id || !price || !category_id || !product_id) {
      return NextResponse.json(
        { error: 'Name, brand, condition, price, category, and product ID are required' },
        { status: 400 }
      );
    }

    // Validate product_id format (6 digits)
    if (!/^\d{6}$/.test(product_id)) {
      return NextResponse.json(
        { error: 'Product ID must be exactly 6 digits' },
        { status: 400 }
      );
    }

    // Check if product_id already exists (excluding current product)
    const { data: existingProduct } = await supabase
      .from('products')
      .select('id')
      .eq('product_id', product_id)
      .neq('id', id)
      .single();

    if (existingProduct) {
      return NextResponse.json(
        { error: 'Product ID already exists. Please use a different ID.' },
        { status: 400 }
      );
    }

    // Include stock fields and set is_active based on status
    const { ...cleanProductData } = productData;
    cleanProductData.stock_quantity = productData.stock_quantity || 0;
    cleanProductData.low_stock_threshold = productData.low_stock_threshold || 5;
    // Only 'active' status should be visible to customers
    cleanProductData.is_active = productData.status === 'active';

    // Generate slug from name
    const slug = name.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    // Update product
    const { data: product, error } = await supabase
      .from('products')
      .update({
        ...cleanProductData,
        slug,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select(`
        *,
        categories (
          id,
          name,
          slug
        ),
        brands (
          id,
          name,
          slug
        ),
        conditions (
          id,
          name,
          slug
        )
      `)
      .single();

    if (error) {
      console.error('Error updating product:', error);
      return NextResponse.json(
        { error: 'Failed to update product' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      message: 'Product updated successfully',
      product
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE product
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const supabase = createSupabaseAdminClient();

    // Check if product exists and get its images
    const { data: existingProduct, error: checkError } = await supabase
      .from('products')
      .select('id, name, images')
      .eq('id', id)
      .single();

    if (checkError || !existingProduct) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Check for foreign key references
    const { data: inquiries, error: inquiryError } = await supabase
      .from('inquiries')
      .select('id')
      .eq('product_id', id)
      .limit(1);

    if (inquiryError) {
      console.error('Error checking inquiries:', inquiryError);
      return NextResponse.json(
        { error: 'Failed to check product references' },
        { status: 500 }
      );
    }

    if (inquiries && inquiries.length > 0) {
      return NextResponse.json(
        {
          error: 'Cannot delete product',
          message: 'This product has associated inquiries and cannot be deleted. Please archive it instead or contact support.',
          canArchive: true
        },
        { status: 409 }
      );
    }

    // Delete product from database first
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting product:', error);

      // Check if it's a foreign key constraint error
      if (error.code === '23503') {
        return NextResponse.json(
          {
            error: 'Cannot delete product',
            message: 'This product is referenced by other records and cannot be deleted. Please archive it instead.',
            canArchive: true
          },
          { status: 409 }
        );
      }

      return NextResponse.json(
        { error: 'Failed to delete product' },
        { status: 500 }
      );
    }

    // After successful product deletion, delete associated images from storage
    const imageDeleteResult = await deleteProductImages(existingProduct.images, supabase);

    let message = 'Product deleted successfully';
    if (imageDeleteResult.success && imageDeleteResult.deletedCount > 0) {
      message = `Product and ${imageDeleteResult.deletedCount} associated images deleted successfully`;
    } else if (!imageDeleteResult.success) {
      console.error('Failed to delete some images:', imageDeleteResult.error);
      message = 'Product deleted successfully, but some images could not be removed from storage';
    }

    return NextResponse.json({
      message: message
    });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
