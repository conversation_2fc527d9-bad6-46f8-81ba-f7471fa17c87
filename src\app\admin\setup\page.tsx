'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Shield, CheckCircle, AlertCircle } from 'lucide-react';

export default function AdminSetupPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const handleSetup = async () => {
    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/admin/setup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (response.ok) {
        setResult(data);
      } else {
        setError(data.error || 'Setup failed');
      }
    } catch (err) {
      setError('Network error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#1a2b4a] flex items-center justify-center p-4">
      <Card className="max-w-2xl w-full p-8">
        <div className="text-center mb-8">
          <div className="mx-auto h-16 w-16 bg-gradient-to-r from-[#956358] to-[#f9c1b2] rounded-full flex items-center justify-center mb-4">
            <Shield className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-white mb-2">Admin Setup</h1>
          <p className="text-gray-400">Initialize admin users and roles for the system</p>
        </div>

        {!result && !error && (
          <div className="text-center">
            <p className="text-gray-300 mb-6">
              This will create default admin users and roles in the database.
              <br />
              <strong>Note:</strong> This is only available in development mode.
            </p>
            <Button
              onClick={handleSetup}
              disabled={isLoading}
              className="bg-gradient-to-r from-[#956358] to-[#f9c1b2] hover:from-[#f9c1b2] hover:to-[#956358]"
            >
              {isLoading ? 'Setting up...' : 'Setup Admin Users'}
            </Button>
          </div>
        )}

        {error && (
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <span className="text-red-400 font-medium">Setup Failed</span>
            </div>
            <p className="text-red-300 mt-2">{error}</p>
            <Button
              onClick={handleSetup}
              variant="outline"
              className="mt-4"
              disabled={isLoading}
            >
              Try Again
            </Button>
          </div>
        )}

        {result && (
          <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-4">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <span className="text-green-400 font-medium">Setup Successful!</span>
            </div>
            
            <p className="text-green-300 mb-4">{result.message}</p>
            
            <div className="space-y-3">
              <h3 className="text-white font-medium">Default Admin Accounts:</h3>
              {result.users?.map((user: any, index: number) => (
                <div key={index} className="bg-[#0a1e3d] p-3 rounded-lg">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm">
                    <div>
                      <span className="text-gray-400">Email:</span>
                      <span className="text-white ml-2">{user.email}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Password:</span>
                      <span className="text-white ml-2 font-mono">{user.password}</span>
                    </div>
                    <div>
                      <span className="text-gray-400">Role:</span>
                      <span className="text-white ml-2">{user.role}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 text-center">
              <Button
                onClick={() => window.location.href = '/admin/login'}
                className="bg-gradient-to-r from-[#956358] to-[#f9c1b2] hover:from-[#f9c1b2] hover:to-[#956358]"
              >
                Go to Admin Login
              </Button>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
}
