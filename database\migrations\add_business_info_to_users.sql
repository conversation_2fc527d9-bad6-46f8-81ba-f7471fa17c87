-- Migration: Add business information fields to users table
-- This migration adds business-related fields to the users table
-- to support business customers and dealers

-- Add business information columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS company_name VA<PERSON>HAR(255),
ADD COLUMN IF NOT EXISTS business_type VARCHAR(100),
ADD COLUMN IF NOT EXISTS tax_id VARCHAR(100),
ADD COLUMN IF NOT EXISTS website VARCHAR(500),
ADD COLUMN IF NOT EXISTS business_address TEXT,
ADD COLUMN IF NOT EXISTS business_phone VARCHAR(20);

-- Create index for business lookups
CREATE INDEX IF NOT EXISTS idx_users_company_name ON users(company_name);
CREATE INDEX IF NOT EXISTS idx_users_business_type ON users(business_type);
CREATE INDEX IF NOT EXISTS idx_users_tax_id ON users(tax_id);

-- Add comments to document the new fields
COMMENT ON COLUMN users.company_name IS 'Name of the business/company';
COMMENT ON COLUMN users.business_type IS 'Type of business (retailer, wholesaler, distributor, reseller, corporate, other)';
COMMENT ON COLUMN users.tax_id IS 'Tax ID or business registration number';
COMMENT ON COLUMN users.website IS 'Company website URL';
COMMENT ON COLUMN users.business_address IS 'Business address (separate from personal address)';
COMMENT ON COLUMN users.business_phone IS 'Business phone number (separate from personal phone)';
