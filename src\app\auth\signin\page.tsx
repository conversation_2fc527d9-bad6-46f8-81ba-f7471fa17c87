import { Suspense } from 'react';
import { SigninForm } from '@/components/auth/SigninForm';

function SigninFormWrapper() {
  return <SigninForm />;
}

export default function SigninPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gradient-to-br from-[#03132a] via-[#0a1e3d] to-[#1a2b4a] flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#956358]"></div>
      </div>
    }>
      <SigninFormWrapper />
    </Suspense>
  );
}
