import { NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    // This is a development-only endpoint for fixing database schema
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    const supabase = createSupabaseAdminClient();

    // First, let's check the current structure of admin_users table
    const { data: adminUsersStructure, error: structureError } = await supabase
      .from('admin_users')
      .select('*')
      .limit(1);

    console.log('Current admin_users structure:', adminUsersStructure);
    console.log('Structure error:', structureError);

    // Add the role_id column if it doesn't exist
    const addRoleIdColumn = `
      ALTER TABLE admin_users 
      ADD COLUMN IF NOT EXISTS role_id UUID REFERENCES roles(id);
    `;

    // Schema change would be executed here
    console.log('Schema change would execute:', addRoleIdColumn);

    // Check if the column exists
    const { error: directError } = await supabase
      .from('admin_users')
      .select('role_id')
      .limit(1);

    if (directError && directError.code === '42703') {
      return NextResponse.json({
        message: 'role_id column is missing and needs to be added manually',
        error: 'Please add the role_id column to admin_users table in Supabase dashboard',
        sql: 'ALTER TABLE admin_users ADD COLUMN role_id UUID REFERENCES roles(id);',
        structureError,
        directError
      });
    }

    // Check if the column was added successfully
    const { data: updatedStructure, error: updatedError } = await supabase
      .from('admin_users')
      .select('*')
      .limit(1);

    console.log('Updated admin_users structure:', updatedStructure);

    return NextResponse.json({
      message: 'Schema check completed',
      originalStructure: adminUsersStructure,
      structureError,
      updatedStructure,
      updatedError
    });

  } catch (error) {
    console.error('Schema fix error:', error);
    return NextResponse.json(
      { error: 'Schema fix failed', details: error },
      { status: 500 }
    );
  }
}
