# Admin Authentication System

This document outlines the implementation of the admin authentication system for Tisha International.

## Overview

The admin authentication system provides secure access control for administrative functions, including:
- Role-based permissions
- JWT-based authentication
- Protected admin routes
- Session management

## Features

### Authentication
- ✅ Admin login/logout functionality
- ✅ JWT token-based authentication
- ✅ Secure password hashing with bcrypt
- ✅ Session persistence with HTTP-only cookies
- ✅ Auto-redirect for unauthenticated users

### Authorization
- ✅ Role-based access control (RBAC)
- ✅ Permission-based resource protection
- ✅ Granular permissions for different actions
- ✅ Multi-level admin roles (Super Admin, Admin, Support)

### Admin Panel
- ✅ Protected admin layout with navigation
- ✅ Dashboard with statistics overview
- ✅ Inquiry management interface
- ✅ Chat management system
- ✅ User management capabilities

## Default Admin Accounts

After setting up the database, the following admin accounts will be available:

### Super Admin
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: Super Admin (full access)

### Support Admin
- **Email**: <EMAIL>
- **Password**: support123
- **Role**: Support (limited access)

**⚠️ Important**: Change these default passwords immediately after first login!

## API Endpoints

### Authentication Endpoints
```
POST /api/admin/auth/login     - Admin login
GET  /api/admin/auth/verify    - Verify admin session
POST /api/admin/auth/logout    - Admin logout
```

### Protected Admin Endpoints
```
GET  /api/admin/chats                    - Get all inquiries with chat stats
GET  /api/admin/inquiries/{id}           - Get specific inquiry details
PATCH /api/admin/inquiries/{id}/status   - Update inquiry status
```

## Permission System

### Resources
- `products` - Product management
- `inquiries` - Inquiry management
- `users` - User management
- `roles` - Role management
- `chat` - Chat management
- `inventory` - Inventory management

### Actions
- `create` - Create new resources
- `read` - View/read resources
- `update` - Modify existing resources
- `delete` - Remove resources

### Example Role Configuration
```json
{
  "resource": "inquiries",
  "actions": ["read", "update"]
}
```

## Setup Instructions

### 1. Database Setup
Execute the following SQL files in your Supabase dashboard:
```sql
-- Create tables (if not already done)
\i database/schema.sql

-- Create admin users and roles
\i database/admin_setup.sql
```

### 2. Environment Variables
Add to your `.env.local`:
```bash
JWT_SECRET=your_secure_jwt_secret_key_here
```

### 3. Generate Password Hashes
To create new admin users with hashed passwords:
```bash
node scripts/hash-password.js your_password_here
```

## Usage

### Admin Login
1. Navigate to `/admin/login`
2. Enter admin credentials
3. System will redirect to `/admin/dashboard`

### Permission Checking (Frontend)
```typescript
import { useAdminAuth } from '@/hooks/useAdminAuth';

const { checkPermission } = useAdminAuth();

// Check if admin can update inquiries
if (checkPermission('inquiries', 'update')) {
  // Show update functionality
}
```

### Permission Checking (Backend)
```typescript
import { requireAdminAuth } from '@/lib/auth/admin';

export async function POST(request: NextRequest) {
  const authResult = await requireAdminAuth('products', 'create');
  if (!authResult.success) {
    return NextResponse.json({ error: authResult.error }, { status: authResult.status });
  }
  
  // Protected functionality here
}
```

## Security Features

### Authentication Security
- ✅ JWT tokens with expiration
- ✅ HTTP-only cookies
- ✅ Secure password hashing (bcrypt)
- ✅ Session timeout handling

### Authorization Security
- ✅ Role-based permissions
- ✅ Resource-level access control
- ✅ Action-level permissions
- ✅ Real-time permission validation

### Route Protection
- ✅ Protected admin routes
- ✅ Automatic authentication checks
- ✅ Permission-based component rendering
- ✅ Secure API endpoints

## File Structure

```
src/
├── app/
│   ├── admin/
│   │   ├── layout.tsx                 # Admin layout with auth protection
│   │   ├── page.tsx                   # Redirect to dashboard
│   │   ├── login/
│   │   │   └── page.tsx               # Admin login page
│   │   ├── dashboard/
│   │   │   └── page.tsx               # Admin dashboard
│   │   ├── inquiries/
│   │   │   └── page.tsx               # Inquiry management
│   │   └── chats/                     # Existing chat management
│   └── api/
│       └── admin/
│           └── auth/
│               ├── login/route.ts     # Login endpoint
│               ├── verify/route.ts    # Session verification
│               └── logout/route.ts    # Logout endpoint
├── components/
│   └── admin/
│       └── AdminLayout.tsx            # Admin layout component
├── hooks/
│   └── useAdminAuth.ts                # Admin auth hook
├── lib/
│   └── auth/
│       └── admin.ts                   # Admin auth utilities
└── scripts/
    └── hash-password.js               # Password hashing utility
```

## Next Steps

1. **Test the authentication system**:
   - Try logging in with default credentials
   - Test permission-based access
   - Verify logout functionality

2. **Change default passwords**:
   - Update default admin passwords
   - Create additional admin users as needed

3. **Implement remaining admin features**:
   - Product management interface
   - User management system
   - Role management interface
   - Inventory management

4. **Security enhancements**:
   - Implement rate limiting
   - Add audit logging
   - Set up session monitoring
   - Configure HTTPS in production

## Troubleshooting

### Common Issues

1. **"Admin not authenticated" error**:
   - Check if JWT_SECRET is set in environment variables
   - Verify admin user exists in database
   - Check browser cookies for authentication token

2. **"Insufficient permissions" error**:
   - Verify user role has required permissions
   - Check permission configuration in database
   - Ensure correct resource/action combination

3. **Login page redirect loop**:
   - Clear browser cookies
   - Check authentication hook initialization
   - Verify API endpoint responses

### Debug Commands
```bash
# Check admin users
SELECT * FROM admin_users;

# Check roles and permissions
SELECT * FROM roles;

# Test password hash
node scripts/hash-password.js test_password
```

## Contributing

When adding new admin features:
1. Add appropriate permissions to role definitions
2. Use `requireAdminAuth()` for API route protection
3. Use `checkPermission()` for frontend permission checks
4. Update this documentation with new endpoints/features
