import { NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { createSupabaseAdminClient } from '@/lib/supabase/server';

export async function GET() {
  try {
    // This is a development-only endpoint for resetting admin passwords
    if (process.env.NODE_ENV === 'production') {
      return NextResponse.json({ error: 'Not available in production' }, { status: 403 });
    }

    const supabase = createSupabaseAdminClient();

    // Hash the passwords
    const adminPassword = await bcrypt.hash('admin123', 10);
    const supportPassword = await bcrypt.hash('support123', 10);

    console.log('Resetting admin passwords...');
    console.log('New admin hash:', adminPassword);

    // Update admin user password
    const { error: adminError } = await supabase
      .from('admin_users')
      .update({ password_hash: adminPassword })
      .eq('email', '<EMAIL>');

    // Update support user password
    const { error: supportError } = await supabase
      .from('admin_users')
      .update({ password_hash: supportPassword })
      .eq('email', '<EMAIL>');

    if (adminError) {
      console.error('Error updating admin password:', adminError);
    }

    if (supportError) {
      console.error('Error updating support password:', supportError);
    }

    // Test the new password
    const { data: testUser, error: testError } = await supabase
      .from('admin_users')
      .select('email, password_hash')
      .eq('email', '<EMAIL>')
      .single();

    let testResult = false;
    if (testUser && !testError) {
      testResult = await bcrypt.compare('admin123', testUser.password_hash);
    }

    return NextResponse.json({
      message: 'Admin passwords reset successfully',
      adminError,
      supportError,
      testResult,
      credentials: [
        { email: '<EMAIL>', password: 'admin123', role: 'Super Admin' },
        { email: '<EMAIL>', password: 'support123', role: 'Support' }
      ]
    });

  } catch (error) {
    console.error('Password reset error:', error);
    return NextResponse.json(
      { error: 'Password reset failed', details: error },
      { status: 500 }
    );
  }
}
