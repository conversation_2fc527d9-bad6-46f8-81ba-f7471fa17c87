// Email service for sending notifications
// This is a basic implementation - in production, you'd use a service like SendGrid, Mailgun, etc.

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

interface InquiryData {
  id: string;
  inquiry_type: 'regular' | 'bulk';
  quantity: number;
  status: string;
  user_details: {
    fullName: string;
    email: string;
    phone: string;
    address: string;
  };
  product?: {
    name: string;
    brand: string;
    model_name: string;
    price: number;
  };
  created_at: string;
}

export class EmailService {
  private static instance: EmailService;
  
  public static getInstance(): EmailService {
    if (!EmailService.instance) {
      EmailService.instance = new EmailService();
    }
    return EmailService.instance;
  }

  // Send inquiry confirmation to customer
  async sendInquiryConfirmation(inquiryData: InquiryData): Promise<boolean> {
    try {
      const template = this.generateInquiryConfirmationTemplate(inquiryData);
      
      // In a real implementation, you would use an email service here
      console.log('Sending inquiry confirmation email:', {
        to: inquiryData.user_details.email,
        subject: template.subject,
        html: template.html
      });

      // Simulate email sending
      await this.simulateEmailSend();
      
      return true;
    } catch (error) {
      console.error('Error sending inquiry confirmation email:', error);
      return false;
    }
  }

  // Send inquiry notification to admin
  async sendInquiryNotificationToAdmin(inquiryData: InquiryData): Promise<boolean> {
    try {
      const template = this.generateAdminNotificationTemplate(inquiryData);
      
      // In a real implementation, you would use an email service here
      console.log('Sending admin notification email:', {
        to: '<EMAIL>', // Admin email
        subject: template.subject,
        html: template.html
      });

      // Simulate email sending
      await this.simulateEmailSend();
      
      return true;
    } catch (error) {
      console.error('Error sending admin notification email:', error);
      return false;
    }
  }

  // Send status update to customer
  async sendStatusUpdate(inquiryData: InquiryData, oldStatus: string, newStatus: string): Promise<boolean> {
    try {
      const template = this.generateStatusUpdateTemplate(inquiryData, oldStatus, newStatus);
      
      // In a real implementation, you would use an email service here
      console.log('Sending status update email:', {
        to: inquiryData.user_details.email,
        subject: template.subject,
        html: template.html
      });

      // Simulate email sending
      await this.simulateEmailSend();
      
      return true;
    } catch (error) {
      console.error('Error sending status update email:', error);
      return false;
    }
  }

  private generateInquiryConfirmationTemplate(inquiryData: InquiryData): EmailTemplate {
    const subject = `Inquiry Confirmation - ${inquiryData.inquiry_type === 'bulk' ? 'Bulk Order' : 'Product Inquiry'} #${inquiryData.id.slice(0, 8)}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${subject}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #956358, #b87568); color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .footer { padding: 20px; text-align: center; color: #666; font-size: 14px; }
            .inquiry-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
            .status-badge { display: inline-block; padding: 5px 10px; background: #ffa500; color: white; border-radius: 3px; font-size: 12px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>TISHA INTERNATIONAL</h1>
              <h2>Inquiry Confirmation</h2>
            </div>
            
            <div class="content">
              <p>Dear ${inquiryData.user_details.fullName},</p>
              
              <p>Thank you for your ${inquiryData.inquiry_type === 'bulk' ? 'bulk order' : 'product'} inquiry. We have received your request and our team will review it shortly.</p>
              
              <div class="inquiry-details">
                <h3>Inquiry Details</h3>
                <p><strong>Inquiry ID:</strong> ${inquiryData.id.slice(0, 8)}</p>
                <p><strong>Type:</strong> ${inquiryData.inquiry_type === 'bulk' ? 'Bulk Order' : 'Regular Inquiry'}</p>
                <p><strong>Quantity:</strong> ${inquiryData.quantity}</p>
                <p><strong>Status:</strong> <span class="status-badge">Pending Review</span></p>
                <p><strong>Submitted:</strong> ${new Date(inquiryData.created_at).toLocaleDateString()}</p>
                ${inquiryData.product ? `
                  <p><strong>Product:</strong> ${inquiryData.product.brand} ${inquiryData.product.model_name}</p>
                  <p><strong>Price:</strong> $${inquiryData.product.price.toLocaleString()}</p>
                ` : ''}
              </div>
              
              <p><strong>What happens next?</strong></p>
              <ul>
                <li>Our team will review your inquiry within 24 hours</li>
                <li>We'll contact you via email or phone to discuss details</li>
                <li>You can track your inquiry status in your account dashboard</li>
              </ul>
              
              <p>If you have any questions, please don't hesitate to contact us:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +****************</li>
                <li>WhatsApp: +****************</li>
              </ul>
            </div>
            
            <div class="footer">
              <p>Thank you for choosing Tisha International!</p>
              <p>This is an automated message. Please do not reply to this email.</p>
            </div>
          </div>
        </body>
      </html>
    `;

    const text = `
      Dear ${inquiryData.user_details.fullName},
      
      Thank you for your ${inquiryData.inquiry_type === 'bulk' ? 'bulk order' : 'product'} inquiry.
      
      Inquiry Details:
      - ID: ${inquiryData.id.slice(0, 8)}
      - Type: ${inquiryData.inquiry_type === 'bulk' ? 'Bulk Order' : 'Regular Inquiry'}
      - Quantity: ${inquiryData.quantity}
      - Status: Pending Review
      
      Our team will review your inquiry within 24 hours and contact you soon.
      
      Best regards,
      Tisha International Team
    `;

    return { subject, html, text };
  }

  private generateAdminNotificationTemplate(inquiryData: InquiryData): EmailTemplate {
    const subject = `New ${inquiryData.inquiry_type === 'bulk' ? 'Bulk Order' : 'Product'} Inquiry - ${inquiryData.user_details.fullName}`;
    
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${subject}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #956358; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .inquiry-details { background: white; padding: 15px; margin: 15px 0; border-radius: 5px; }
            .urgent { background: #ff4444; color: white; padding: 10px; text-align: center; margin-bottom: 20px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>New Inquiry Alert</h1>
            </div>
            
            ${inquiryData.inquiry_type === 'bulk' ? '<div class="urgent"><strong>BULK ORDER INQUIRY - HIGH PRIORITY</strong></div>' : ''}
            
            <div class="content">
              <h3>Customer Information</h3>
              <div class="inquiry-details">
                <p><strong>Name:</strong> ${inquiryData.user_details.fullName}</p>
                <p><strong>Email:</strong> ${inquiryData.user_details.email}</p>
                <p><strong>Phone:</strong> ${inquiryData.user_details.phone}</p>
                <p><strong>Address:</strong> ${inquiryData.user_details.address}</p>
              </div>
              
              <h3>Inquiry Details</h3>
              <div class="inquiry-details">
                <p><strong>Inquiry ID:</strong> ${inquiryData.id}</p>
                <p><strong>Type:</strong> ${inquiryData.inquiry_type === 'bulk' ? 'Bulk Order' : 'Regular Inquiry'}</p>
                <p><strong>Quantity:</strong> ${inquiryData.quantity}</p>
                <p><strong>Submitted:</strong> ${new Date(inquiryData.created_at).toLocaleString()}</p>
                ${inquiryData.product ? `
                  <p><strong>Product:</strong> ${inquiryData.product.brand} ${inquiryData.product.model_name}</p>
                  <p><strong>Product Name:</strong> ${inquiryData.product.name}</p>
                  <p><strong>Unit Price:</strong> $${inquiryData.product.price.toLocaleString()}</p>
                  <p><strong>Total Value:</strong> $${(inquiryData.product.price * inquiryData.quantity).toLocaleString()}</p>
                ` : ''}
              </div>
              
              <p><strong>Action Required:</strong></p>
              <ul>
                <li>Review the inquiry in the admin dashboard</li>
                <li>Contact the customer within 24 hours</li>
                <li>Update the inquiry status as appropriate</li>
              </ul>
              
              <p><a href="${process.env.NEXT_PUBLIC_APP_URL}/admin/inquiries/${inquiryData.id}" style="background: #956358; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View in Admin Dashboard</a></p>
            </div>
          </div>
        </body>
      </html>
    `;

    const text = `
      New ${inquiryData.inquiry_type === 'bulk' ? 'Bulk Order' : 'Product'} Inquiry
      
      Customer: ${inquiryData.user_details.fullName}
      Email: ${inquiryData.user_details.email}
      Phone: ${inquiryData.user_details.phone}
      
      Inquiry ID: ${inquiryData.id}
      Type: ${inquiryData.inquiry_type === 'bulk' ? 'Bulk Order' : 'Regular Inquiry'}
      Quantity: ${inquiryData.quantity}
      
      Please review and respond within 24 hours.
    `;

    return { subject, html, text };
  }

  private generateStatusUpdateTemplate(inquiryData: InquiryData, oldStatus: string, newStatus: string): EmailTemplate {
    const subject = `Inquiry Status Update - ${inquiryData.inquiry_type === 'bulk' ? 'Bulk Order' : 'Product Inquiry'} #${inquiryData.id.slice(0, 8)}`;
    
    const statusMessages = {
      'in_progress': 'Your inquiry is now being processed by our team.',
      'accepted': 'Great news! Your inquiry has been accepted.',
      'completed': 'Your inquiry has been completed successfully.',
      'rejected': 'Unfortunately, we cannot proceed with this inquiry at this time.'
    };

    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>${subject}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #956358, #b87568); color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .status-update { background: white; padding: 20px; margin: 15px 0; border-radius: 5px; text-align: center; }
            .status-badge { display: inline-block; padding: 8px 15px; background: #28a745; color: white; border-radius: 5px; font-weight: bold; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>TISHA INTERNATIONAL</h1>
              <h2>Inquiry Status Update</h2>
            </div>
            
            <div class="content">
              <p>Dear ${inquiryData.user_details.fullName},</p>
              
              <div class="status-update">
                <h3>Your inquiry status has been updated</h3>
                <p><strong>Inquiry ID:</strong> ${inquiryData.id.slice(0, 8)}</p>
                <p><strong>New Status:</strong> <span class="status-badge">${newStatus.replace('_', ' ').toUpperCase()}</span></p>
                <p>${statusMessages[newStatus as keyof typeof statusMessages] || 'Your inquiry status has been updated.'}</p>
              </div>
              
              <p>You can view the full details of your inquiry and any messages from our team by logging into your account.</p>
              
              <p><a href="${process.env.NEXT_PUBLIC_APP_URL}/user/orders" style="background: #956358; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">View My Orders</a></p>
              
              <p>If you have any questions, please contact us:</p>
              <ul>
                <li>Email: <EMAIL></li>
                <li>Phone: +****************</li>
              </ul>
            </div>
          </div>
        </body>
      </html>
    `;

    const text = `
      Dear ${inquiryData.user_details.fullName},
      
      Your inquiry status has been updated:
      
      Inquiry ID: ${inquiryData.id.slice(0, 8)}
      New Status: ${newStatus.replace('_', ' ').toUpperCase()}
      
      ${statusMessages[newStatus as keyof typeof statusMessages] || 'Your inquiry status has been updated.'}
      
      View your orders: ${process.env.NEXT_PUBLIC_APP_URL}/user/orders
      
      Best regards,
      Tisha International Team
    `;

    return { subject, html, text };
  }

  private async simulateEmailSend(): Promise<void> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

export const emailService = EmailService.getInstance();
