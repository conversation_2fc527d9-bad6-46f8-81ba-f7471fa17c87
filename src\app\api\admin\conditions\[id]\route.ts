import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';
import { verifyAdminAuth } from '@/lib/auth/admin';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const supabase = createSupabaseAdminClient();
    const { data: condition, error } = await supabase
      .from('conditions')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching condition:', error);
      return NextResponse.json(
        { error: 'Condition not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ condition });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const body = await request.json();
    const { name, description, is_active } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'Condition name is required' },
        { status: 400 }
      );
    }

    const supabase = createSupabaseAdminClient();

    // Generate slug from name
    const slug = name.toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();

    // Update condition
    const { data: condition, error } = await supabase
      .from('conditions')
      .update({
        name,
        slug,
        description,
        is_active,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating condition:', error);
      if (error.code === '23505') { // Unique constraint violation
        return NextResponse.json(
          { error: 'Condition name already exists' },
          { status: 409 }
        );
      }
      return NextResponse.json(
        { error: 'Failed to update condition' },
        { status: 500 }
      );
    }

    return NextResponse.json({ condition });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;
    const supabase = createSupabaseAdminClient();

    // Check if condition is used by any products
    const { data: products, error: checkError } = await supabase
      .from('products')
      .select('id')
      .eq('condition_id', id)
      .limit(1);

    if (checkError) {
      console.error('Error checking condition usage:', checkError);
      return NextResponse.json(
        { error: 'Failed to check condition usage' },
        { status: 500 }
      );
    }

    if (products && products.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete condition that is used by products. Please reassign products first.' },
        { status: 409 }
      );
    }

    // Delete condition
    const { error } = await supabase
      .from('conditions')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting condition:', error);
      return NextResponse.json(
        { error: 'Failed to delete condition' },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: 'Condition deleted successfully' });

  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
