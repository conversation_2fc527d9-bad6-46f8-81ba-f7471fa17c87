'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import Link from 'next/link';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, Filter, Grid, List, Loader2 } from 'lucide-react';
import { getProductUrl } from '@/lib/product-utils';
import { PrivateProductsQueryForm } from '@/components/product/PrivateProductsQueryForm';
import { useAuth } from '@/contexts/AuthContext';
import EnhancedProductsPage from '@/components/products/EnhancedProductsPage';

interface Category {
  id: string;
  name: string;
  slug: string;
  count?: number;
}

interface Product {
  id: string;
  name: string;
  brand: string;
  model_name?: string;
  price: number;
  images?: string[];
  is_active: boolean;
  categories?: Category;
  description?: string;
  slug: string;
  status: 'active' | 'private' | 'deactivated' | 'archived';
  show_price?: boolean;
}

export default function ProductsPage() {
  const { user, loading: authLoading } = useAuth();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedBrand, setSelectedBrand] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreActive, setHasMoreActive] = useState(true);
  const [showLoadMoreButton, setShowLoadMoreButton] = useState(false);
  const [showQueryForm, setShowQueryForm] = useState(false);
  const [hasPrivateAccess, setHasPrivateAccess] = useState(false);
  const [activeProductsCount, setActiveProductsCount] = useState(0);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);

  // Check for private access on component mount and when auth changes
  useEffect(() => {
    if (authLoading) return; // Wait for auth to load

    let newPrivateAccess = false;

    // Logged-in users automatically get private access
    if (user) {
      newPrivateAccess = true;
    } else {
      // For non-logged users, check localStorage
      const privateAccess = localStorage.getItem('privateProductsAccess');
      newPrivateAccess = privateAccess === 'true';
    }

    setHasPrivateAccess(newPrivateAccess);
  }, [user, authLoading]);

  // Initial load when auth is ready
  useEffect(() => {
    if (authLoading) return; // Wait for auth to load

    setProducts([]);
    setCurrentPage(1);
    setHasMoreActive(true);
    setShowLoadMoreButton(false);
    fetchProducts(1, true);
    fetchCategories();
  }, [authLoading]);

  // Refetch when filters change
  useEffect(() => {
    if (authLoading) return; // Wait for auth to load




    setProducts([]);
    setCurrentPage(1);
    setHasMoreActive(true);
    setShowLoadMoreButton(false);
    fetchProducts(1, true);
  }, [searchTerm, selectedCategory, selectedBrand, sortBy]);





  // Set up intersection observer for infinite scroll
  useEffect(() => {
    if (authLoading) return; // Wait for auth to load

    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMoreActive && !isLoading && !isLoadingMore) {
          loadMoreProducts();
        }
      },
      { threshold: 0.1 }
    );

    if (loadMoreRef.current) {
      observerRef.current.observe(loadMoreRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [hasMoreActive, isLoading, isLoadingMore, authLoading]);

  const fetchProducts = async (page = 1, reset = false, forceIncludePrivate?: boolean) => {
    // Determine private access: check user login, localStorage, or explicit override
    let shouldIncludePrivate = false;

    if (forceIncludePrivate !== undefined) {
      shouldIncludePrivate = forceIncludePrivate;
    } else if (user) {
      shouldIncludePrivate = true; // Logged-in users get private access
    } else {
      const privateAccess = localStorage.getItem('privateProductsAccess');
      shouldIncludePrivate = privateAccess === 'true'; // Check localStorage directly
    }




    try {
      if (reset) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }

      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (selectedCategory) params.append('category', selectedCategory);
      if (selectedBrand) params.append('brand', selectedBrand);
      if (sortBy) params.append('sortBy', sortBy);
      params.append('page', page.toString());
      params.append('limit', '12');
      if (shouldIncludePrivate) params.append('includePrivate', 'true');





      const response = await fetch(`/api/products?${params}`);

      if (response.ok) {
        const data = await response.json();
        const newProducts = data.products || [];









        if (reset) {
          setProducts(newProducts);
        } else {
          // Deduplicate products by ID to prevent duplicate keys
          setProducts(prev => {
            const existingIds = new Set(prev.map(p => p.id));
            const uniqueNewProducts = newProducts.filter(p => !existingIds.has(p.id));
            return [...prev, ...uniqueNewProducts];
          });
        }

        setActiveProductsCount(data.pagination?.activeCount || 0);

        // Check if we have more products to load based on pagination
        const hasMoreProducts = data.pagination && (data.pagination.page * data.pagination.limit) < data.pagination.total;
        setHasMoreActive(hasMoreProducts);

        // Show Load More button if we've loaded all active products but haven't accessed private products
        // Only show for non-logged users (logged users automatically get private access)
        if (!hasMoreProducts && !hasPrivateAccess && !user && data.pagination?.activeCount > 0) {
          setShowLoadMoreButton(true);
        }
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  const loadMoreProducts = useCallback(() => {
    if (hasMoreActive && !isLoadingMore && !isLoading) {
      const nextPage = currentPage + 1;
      setCurrentPage(nextPage);
      fetchProducts(nextPage, false); // Let it use current hasPrivateAccess state
    }
  }, [currentPage, hasMoreActive, isLoadingMore, isLoading, searchTerm, selectedCategory, selectedBrand, sortBy, hasPrivateAccess]);

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories');

      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      } else {
        setCategories([]);
      }
    } catch (error) {
      // Silently handle error
    }
  };

  const handleQueryFormSubmit = async (formData: {
    mobile: string;
    name?: string;
    companyName?: string;
    email?: string;
  }) => {
    try {
      const response = await fetch('/api/inquiries/hot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to submit request');
      }

      // Set private access and reload products
      localStorage.setItem('privateProductsAccess', 'true');
      localStorage.setItem('privateProductsAccessData', JSON.stringify({
        mobile: formData.mobile,
        submittedAt: new Date().toISOString()
      }));

      // Update state and reload products
      setHasPrivateAccess(true);
      setShowLoadMoreButton(false);

      // Force immediate reload with private products
      setProducts([]);
      setCurrentPage(1);
      setHasMoreActive(true);

      // Use setTimeout to ensure state updates are processed
      setTimeout(() => {
        fetchProducts(1, true); // Will use current hasPrivateAccess state
      }, 50);
    } catch (error) {
      throw error;
    }
  };

  // Transform products to match enhanced component interface
  const enhancedProducts = products.map(product => {

    return {
      ...product,
      model: product.model_name || '',
      images: product.images || [],
      categories: product.categories ? {
        id: product.categories.id,
        name: product.categories.name,
        slug: product.categories.slug
      } : undefined,
      brands: product.brands ? {
        id: product.brands.id,
        name: product.brands.name,
        slug: product.brands.slug
      } : undefined,
      conditions: {
        id: '',
        name: 'Excellent',
        slug: 'excellent'
      }
    };
  });



  const enhancedCategories = categories.map(category => ({
    ...category,
    image: ''
  }));





  return (
    <>
      <EnhancedProductsPage
        initialProducts={enhancedProducts}
        initialCategories={enhancedCategories}
        isLoading={isLoading}
        isLoadingMore={isLoadingMore}
        hasMoreActive={hasMoreActive}
        showLoadMoreButton={showLoadMoreButton}
        hasPrivateAccess={hasPrivateAccess}
        activeProductsCount={activeProductsCount}
        loadMoreRef={loadMoreRef}
        onLoadMore={loadMoreProducts}
        onShowQueryForm={() => setShowQueryForm(true)}
        searchTerm={searchTerm}
        setSearchTerm={setSearchTerm}
        selectedCategory={selectedCategory}
        setSelectedCategory={setSelectedCategory}
        selectedBrand={selectedBrand}
        setSelectedBrand={setSelectedBrand}
        sortBy={sortBy}
        setSortBy={setSortBy}
      />

      {/* Private Products Query Form Modal - Only for non-logged users */}
      {!user && (
        <PrivateProductsQueryForm
          isOpen={showQueryForm}
          onClose={() => setShowQueryForm(false)}
          onSubmit={handleQueryFormSubmit}
        />
      )}
    </>
  );
}
