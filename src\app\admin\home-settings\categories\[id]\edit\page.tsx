'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { ArrowLeft, Save, Upload, Search, Plus, Trash2 } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  price: number;
  images: string[];
  brand: string;
  model: string;
  category_id: string;
  show_price?: boolean;
}

interface GalleryItem {
  id: string;
  product_id: string;
  display_order: number;
  is_active: boolean;
  products: Product;
}

interface FeaturedCategory {
  id: string;
  category_id: string;
  custom_image?: string;
  display_order: number;
  is_active: boolean;
  categories: {
    id: string;
    name: string;
    slug: string;
  };
}

export default function EditFeaturedCategoryPage() {
  const router = useRouter();
  const params = useParams();
  const categoryId = params.id as string;

  const [featuredCategory, setFeaturedCategory] = useState<FeaturedCategory | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [galleryItems, setGalleryItems] = useState<GalleryItem[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProducts, setSelectedProducts] = useState<Set<string>>(new Set());
  const [formData, setFormData] = useState({
    custom_image: ''
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    fetchData();
  }, [categoryId]);

  useEffect(() => {
    if (featuredCategory && searchTerm) {
      const categoryProducts = products.filter(product => 
        product.category_id === featuredCategory.category_id &&
        !galleryItems.some(item => item.product_id === product.id)
      );
      const filtered = categoryProducts.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.model.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredProducts(filtered);
    } else if (featuredCategory) {
      const categoryProducts = products.filter(product => 
        product.category_id === featuredCategory.category_id &&
        !galleryItems.some(item => item.product_id === product.id)
      );
      setFilteredProducts(categoryProducts);
    } else {
      setFilteredProducts([]);
    }
  }, [searchTerm, products, featuredCategory, galleryItems]);

  const fetchData = async () => {
    try {
      // Fetch featured category details
      const categoryResponse = await fetch(`/api/admin/home/<USER>/${categoryId}`);
      
      // Fetch all products
      const productsResponse = await fetch('/api/products?limit=1000');
      
      // Fetch gallery items for this category
      const galleryResponse = await fetch(`/api/admin/home/<USER>
      
      if (categoryResponse.ok && productsResponse.ok && galleryResponse.ok) {
        const categoryData = await categoryResponse.json();
        const productsData = await productsResponse.json();
        const galleryData = await galleryResponse.json();
        
        setFeaturedCategory(categoryData.category);
        setProducts(productsData.products || []);
        setGalleryItems(galleryData.gallery_items || []);
        setFormData({
          custom_image: categoryData.category?.custom_image || ''
        });
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleProductSelection = (productId: string) => {
    const newSelected = new Set(selectedProducts);
    if (newSelected.has(productId)) {
      newSelected.delete(productId);
    } else {
      newSelected.add(productId);
    }
    setSelectedProducts(newSelected);
  };

  const addSelectedProducts = async () => {
    if (selectedProducts.size === 0) return;

    try {
      const promises = Array.from(selectedProducts).map((productId, index) => 
        fetch('/api/admin/home/<USER>', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            category_id: featuredCategory?.category_id,
            product_id: productId,
            display_order: galleryItems.length + index
          }),
        })
      );

      await Promise.all(promises);
      setSelectedProducts(new Set());
      fetchData(); // Refresh data
    } catch (error) {
      console.error('Error adding products to gallery:', error);
      alert('Error adding products to gallery');
    }
  };

  const removeFromGallery = async (galleryItemId: string) => {
    if (!confirm('Remove this product from the gallery?')) return;

    try {
      const response = await fetch(`/api/admin/home/<USER>/${galleryItemId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setGalleryItems(prev => prev.filter(item => item.id !== galleryItemId));
      }
    } catch (error) {
      console.error('Error removing product from gallery:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    setIsSaving(true);
    try {
      const response = await fetch(`/api/admin/home/<USER>/${categoryId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          custom_image: formData.custom_image || null
        }),
      });

      if (response.ok) {
        router.push('/admin/home-settings');
      } else {
        alert('Failed to update featured category');
      }
    } catch (error) {
      console.error('Error updating featured category:', error);
      alert('Error updating featured category');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="w-8 h-8 border-2 border-[var(--accent-primary)] border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  if (!featuredCategory) {
    return (
      <div className="text-center py-12">
        <p className="text-[var(--text-secondary)]">Featured category not found</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4 border-b border-[var(--shadow-grey)] pb-4">
        <button
          onClick={() => router.back()}
          className="p-2 hover:bg-[var(--shadow-grey)] rounded-md transition-colors"
        >
          <ArrowLeft className="h-5 w-5 text-[var(--text-secondary)]" />
        </button>
        <div>
          <h1 className="text-3xl font-bold text-[var(--text-primary)]">
            Edit Featured Category: {featuredCategory.categories.name}
          </h1>
          <p className="text-[var(--text-secondary)] mt-1">
            Manage category settings and product gallery
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Configuration */}
        <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
          <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-4">Category Settings</h2>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                Category Name
              </label>
              <input
                type="text"
                value={featuredCategory.categories.name}
                readOnly
                className="w-full px-3 py-2 bg-[var(--shadow-grey)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-secondary)] cursor-not-allowed"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                Display Order
              </label>
              <input
                type="number"
                value={featuredCategory.display_order}
                readOnly
                className="w-full px-3 py-2 bg-[var(--shadow-grey)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-secondary)] cursor-not-allowed"
              />
            </div>

            <div className="md:col-span-2">
              <label className="block text-sm font-medium text-[var(--text-secondary)] mb-2">
                Custom Image URL (Optional)
              </label>
              <input
                type="url"
                value={formData.custom_image}
                onChange={(e) => setFormData(prev => ({ ...prev, custom_image: e.target.value }))}
                className="w-full px-3 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
                placeholder="https://example.com/category-image.jpg"
              />
            </div>
          </div>
        </div>

        {/* Current Gallery */}
        <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
          <h2 className="text-xl font-semibold text-[var(--text-primary)] mb-4">
            Current Gallery ({galleryItems.length} products)
          </h2>
          
          {galleryItems.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {galleryItems.map((item) => (
                <div key={item.id} className="relative p-3 border border-[var(--shadow-grey)] rounded-lg">
                  <div className="aspect-square bg-[var(--shadow-grey)] rounded-lg mb-2 overflow-hidden">
                    {item.products.images && item.products.images.length > 0 ? (
                      <img
                        src={item.products.images[0]}
                        alt={item.products.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-[var(--text-secondary)]">
                        <Upload className="h-6 w-6" />
                      </div>
                    )}
                  </div>
                  <h3 className="font-semibold text-[var(--text-primary)] text-xs line-clamp-1 mb-1">
                    {item.products.name}
                  </h3>
                  <p className="text-xs text-[var(--text-secondary)] line-clamp-1 mb-2">
                    {item.products.brand} • {item.products.model}
                  </p>
                  <button
                    onClick={() => removeFromGallery(item.id)}
                    className="w-full p-1 bg-red-600 text-white rounded text-xs hover:bg-red-700 transition-colors"
                  >
                    <Trash2 className="h-3 w-3 mx-auto" />
                  </button>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-[var(--text-secondary)]">No products in gallery</p>
            </div>
          )}
        </div>

        {/* Add Products */}
        <div className="bg-[var(--background-card)] rounded-lg p-6 border border-[var(--shadow-grey)]">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-[var(--text-primary)]">
              Add Products ({selectedProducts.size} selected)
            </h2>
            {selectedProducts.size > 0 && (
              <button
                onClick={addSelectedProducts}
                className="btn-primary flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Add Selected</span>
              </button>
            )}
          </div>
          
          {/* Search */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--text-secondary)]" />
            <input
              type="text"
              placeholder="Search available products..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-[var(--background-primary)] border border-[var(--shadow-grey)] rounded-md text-[var(--text-primary)] focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)]"
            />
          </div>

          {/* Available Products */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-h-96 overflow-y-auto">
            {filteredProducts.map((product) => (
              <div
                key={product.id}
                onClick={() => toggleProductSelection(product.id)}
                className={`p-3 rounded-lg border cursor-pointer transition-all ${
                  selectedProducts.has(product.id)
                    ? 'border-[var(--accent-primary)] bg-[var(--accent-primary)]/10'
                    : 'border-[var(--shadow-grey)] hover:border-[var(--accent-primary)]/50'
                }`}
              >
                <div className="aspect-square bg-[var(--shadow-grey)] rounded-lg mb-2 overflow-hidden">
                  {product.images && product.images.length > 0 ? (
                    <img
                      src={product.images[0]}
                      alt={product.name}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-[var(--text-secondary)]">
                      <Upload className="h-6 w-6" />
                    </div>
                  )}
                </div>
                <h3 className="font-semibold text-[var(--text-primary)] text-xs line-clamp-1 mb-1">
                  {product.name}
                </h3>
                <p className="text-xs text-[var(--text-secondary)] line-clamp-1">
                  {product.brand} • {product.model}
                </p>
                {selectedProducts.has(product.id) && (
                  <div className="mt-2 flex items-center justify-center">
                    <div className="w-5 h-5 bg-[var(--accent-primary)] rounded-full flex items-center justify-center">
                      <Plus className="h-3 w-3 text-white rotate-45" />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {filteredProducts.length === 0 && (
            <div className="text-center py-8">
              <p className="text-[var(--text-secondary)]">
                {searchTerm 
                  ? `No available products found matching "${searchTerm}"`
                  : 'All products in this category are already in the gallery'
                }
              </p>
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex items-center justify-end space-x-4">
          <button
            type="button"
            onClick={() => router.back()}
            className="px-6 py-2 border border-[var(--shadow-grey)] text-[var(--text-secondary)] rounded-md hover:bg-[var(--shadow-grey)] transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSaving}
            className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Save className="h-4 w-4" />
            <span>{isSaving ? 'Saving...' : 'Save Changes'}</span>
          </button>
        </div>
      </form>
    </div>
  );
}
