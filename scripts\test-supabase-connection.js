// Test script for Supabase connection
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

async function testSupabaseConnection() {
  console.log('Testing Supabase connection...');
  
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    console.error('❌ Supabase environment variables are not set!');
    console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? '✓ Set' : '✗ Not set');
    console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✓ Set' : '✗ Not set');
    return;
  }
  
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  );
  
  try {
    // Test connection by fetching roles
    console.log('Fetching roles...');
    const { data: roles, error: rolesError } = await supabase
      .from('roles')
      .select('*');
      
    if (rolesError) {
      throw rolesError;
    }
    
    console.log('✅ Successfully connected to Supabase!');
    console.log(`Found ${roles.length} roles:`);
    console.table(roles);
    
    // Test admin users query
    console.log('\nTesting admin_users query...');
    const { data: adminUsers, error: adminError } = await supabase
      .from('admin_users')
      .select(`
        id,
        email,
        full_name,
        is_active,
        role_id,
        roles (
          id,
          name,
          permissions
        )
      `)
      .limit(1);
      
    if (adminError) {
      throw adminError;
    }
    
    if (adminUsers && adminUsers.length > 0) {
      console.log('✅ Successfully fetched admin user with role info:');
      const adminUser = adminUsers[0];
      console.log({
        id: adminUser.id,
        email: adminUser.email,
        fullName: adminUser.full_name,
        isActive: adminUser.is_active,
        roleId: adminUser.role_id,
        role: adminUser.roles
      });
    } else {
      console.log('⚠️ No admin users found in the database.');
    }
    
  } catch (error) {
    console.error('❌ Error connecting to Supabase:', error);
  }
}

testSupabaseConnection();
