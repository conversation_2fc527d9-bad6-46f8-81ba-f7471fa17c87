import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseAdminClient } from '@/lib/supabase/server';
import { verifyAdminAuth } from '@/lib/auth/admin';

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication
    const admin = await verifyAdminAuth();
    if (!admin) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const supabase = createSupabaseAdminClient();
    const body = await request.json();
    const { productIds, action, data } = body;

    // Validate input
    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return NextResponse.json(
        { error: 'Product IDs are required' },
        { status: 400 }
      );
    }

    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      );
    }

    let updateData: any = {};
    let updatedCount = 0;

    // Prepare update data based on action
    switch (action) {
      case 'update_brand':
        if (!data.brand_id) {
          return NextResponse.json(
            { error: 'Brand ID is required' },
            { status: 400 }
          );
        }
        updateData = { 
          brand_id: data.brand_id,
          updated_at: new Date().toISOString()
        };
        break;

      case 'update_category':
        if (!data.category_id) {
          return NextResponse.json(
            { error: 'Category ID is required' },
            { status: 400 }
          );
        }
        updateData = { 
          category_id: data.category_id,
          updated_at: new Date().toISOString()
        };
        break;

      case 'update_status':
        if (!data.status) {
          return NextResponse.json(
            { error: 'Status is required' },
            { status: 400 }
          );
        }
        updateData = { 
          status: data.status,
          is_active: data.status === 'active',
          updated_at: new Date().toISOString()
        };
        break;

      case 'update_stock':
        const stockQuantity = parseInt(data.stock_quantity);
        if (isNaN(stockQuantity) || stockQuantity < 0) {
          return NextResponse.json(
            { error: 'Valid stock quantity is required' },
            { status: 400 }
          );
        }
        updateData = { 
          stock_quantity: stockQuantity,
          updated_at: new Date().toISOString()
        };
        break;

      case 'update_price':
        const price = parseFloat(data.price);
        if (isNaN(price) || price < 0) {
          return NextResponse.json(
            { error: 'Valid price is required' },
            { status: 400 }
          );
        }

        if (data.price_adjustment_type === 'set') {
          updateData = { 
            price: price,
            updated_at: new Date().toISOString()
          };
        } else {
          // For increase/decrease, we need to update each product individually
          for (const productId of productIds) {
            // Get current price
            const { data: product, error: fetchError } = await supabase
              .from('products')
              .select('price')
              .eq('id', productId)
              .single();

            if (fetchError) {
              console.error(`Error fetching product ${productId}:`, fetchError);
              continue;
            }

            let newPrice = product.price;
            if (data.price_adjustment_type === 'increase') {
              newPrice = product.price + price;
            } else if (data.price_adjustment_type === 'decrease') {
              newPrice = Math.max(0, product.price - price);
            }

            const { error: updateError } = await supabase
              .from('products')
              .update({ 
                price: newPrice,
                updated_at: new Date().toISOString()
              })
              .eq('id', productId);

            if (!updateError) {
              updatedCount++;
            }
          }

          return NextResponse.json({
            message: 'Products updated successfully',
            updatedCount,
            action
          });
        }
        break;

      case 'delete_products':
        // Handle bulk delete with the same logic as individual delete
        let deletedCount = 0;
        let archivedCount = 0;
        const errors = [];

        for (const productId of productIds) {
          try {
            // Try to delete the product
            const { error: deleteError } = await supabase
              .from('products')
              .delete()
              .eq('id', productId);

            if (deleteError) {
              // If delete fails due to foreign key constraints, try to archive
              if (deleteError.code === '23503') {
                // Get the full product data for archiving
                const { data: product, error: fetchError } = await supabase
                  .from('products')
                  .select('*')
                  .eq('id', productId)
                  .single();

                if (!fetchError && product) {
                  const { error: archiveError } = await supabase
                    .from('products')
                    .update({
                      status: 'archived',
                      is_active: false,
                      updated_at: new Date().toISOString()
                    })
                    .eq('id', productId);

                  if (!archiveError) {
                    archivedCount++;
                  } else {
                    errors.push(`Failed to archive product ${productId}`);
                  }
                } else {
                  errors.push(`Failed to fetch product ${productId} for archiving`);
                }
              } else {
                errors.push(`Failed to delete product ${productId}: ${deleteError.message}`);
              }
            } else {
              deletedCount++;
            }
          } catch (error) {
            errors.push(`Error processing product ${productId}: ${error}`);
          }
        }

        let message = '';
        if (deletedCount > 0) {
          message += `${deletedCount} product${deletedCount !== 1 ? 's' : ''} deleted successfully. `;
        }
        if (archivedCount > 0) {
          message += `${archivedCount} product${archivedCount !== 1 ? 's' : ''} archived (had references). `;
        }
        if (errors.length > 0) {
          message += `${errors.length} error${errors.length !== 1 ? 's' : ''} occurred.`;
        }

        return NextResponse.json({
          message: message.trim(),
          deletedCount,
          archivedCount,
          errors,
          action
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    // Perform bulk update for non-price-adjustment actions
    if (action !== 'update_price' || data.price_adjustment_type === 'set') {
      const { error: updateError, count } = await supabase
        .from('products')
        .update(updateData)
        .in('id', productIds);

      if (updateError) {
        console.error('Error updating products:', updateError);
        return NextResponse.json(
          { error: 'Failed to update products' },
          { status: 500 }
        );
      }

      updatedCount = count || 0;
    }

    return NextResponse.json({
      message: 'Products updated successfully',
      updatedCount,
      action
    });

  } catch (error) {
    console.error('Bulk update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
