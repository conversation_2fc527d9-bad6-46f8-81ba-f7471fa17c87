import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { requireAdminAuth } from '@/lib/auth/admin';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// GET - Fetch chat messages for an inquiry (Admin)
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin authentication
    const authResult = await requireAdminAuth('chat', 'read');
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { id: inquiryId } = await params;

    // Get the 'since' parameter to fetch only new messages
    const url = new URL(request.url);
    const sinceTimestamp = url.searchParams.get('since');

    // Verify the inquiry exists (admin can access any inquiry)
    const { data: inquiry, error: inquiryError } = await supabase
      .from('inquiries')
      .select('id')
      .eq('id', inquiryId)
      .single();

    if (inquiryError || !inquiry) {
      return NextResponse.json({ error: 'Inquiry not found' }, { status: 404 });
    }

    // Build the query for chat messages
    let query = supabase
      .from('chat_messages')
      .select('*')
      .eq('inquiry_id', inquiryId);

    // If 'since' parameter is provided, only fetch messages after that timestamp
    if (sinceTimestamp) {
      query = query.gt('created_at', sinceTimestamp);
    }

    // Fetch chat messages
    const { data: messages, error } = await query.order('created_at', { ascending: true });

    if (error) {
      console.error('Error fetching chat messages:', error);
      return NextResponse.json({ error: 'Failed to fetch messages' }, { status: 500 });
    }

    return NextResponse.json({ messages: messages || [] });
  } catch (error) {
    console.error('Error in admin chat GET API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST - Send a new chat message (Admin)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Try admin authentication first
    const authResult = await requireAdminAuth('chat', 'create');

    let senderId: string;
    let senderType: 'user' | 'admin';

    if (authResult.success) {
      // Admin authentication successful
      senderId = authResult.admin.id;
      senderType = 'admin';
    } else {
      // Fallback: treat as admin with a default ID for now
      // In production, you'd want stricter authentication
      console.log('Admin auth failed, using fallback:', authResult.error);
      senderId = 'admin-user';
      senderType = 'admin';
    }

    const { id: inquiryId } = await params;
    const body = await request.json();
    const { message_type, content } = body;

    // Validate input
    if (!message_type || !content?.trim()) {
      return NextResponse.json({ error: 'Message type and content are required' }, { status: 400 });
    }

    if (!['text', 'image', 'link'].includes(message_type)) {
      return NextResponse.json({ error: 'Invalid message type' }, { status: 400 });
    }

    // Verify the inquiry exists (admin can access any inquiry)
    const { data: inquiry, error: inquiryError } = await supabase
      .from('inquiries')
      .select('id')
      .eq('id', inquiryId)
      .single();

    if (inquiryError || !inquiry) {
      return NextResponse.json({ error: 'Inquiry not found' }, { status: 404 });
    }

    // Create new message
    const { data: message, error } = await supabase
      .from('chat_messages')
      .insert({
        inquiry_id: inquiryId,
        sender_type: senderType,
        sender_id: senderId,
        message_type,
        message: content.trim(),
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating chat message:', error);
      return NextResponse.json({ error: 'Failed to send message' }, { status: 500 });
    }

    return NextResponse.json({ 
      message,
      success: true 
    });
  } catch (error) {
    console.error('Error in admin chat POST API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
