import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { requireAdminAuth } from '@/lib/auth/admin';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

// PATCH - Update inquiry status
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify admin authentication and permissions
    const authResult = await requireAdminAuth('inquiries', 'update');
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { id: inquiryId } = await params;
    const body = await request.json();
    const { status } = body;

    // Validate status
    const validStatuses = ['pending', 'in_progress', 'accepted', 'rejected', 'completed'];
    if (!validStatuses.includes(status)) {
      return NextResponse.json({ error: 'Invalid status' }, { status: 400 });
    }

    // Update inquiry status
    const { data: inquiry, error } = await supabase
      .from('inquiries')
      .update({ 
        status,
        updated_at: new Date().toISOString()
      })
      .eq('id', inquiryId)
      .select()
      .single();

    if (error) {
      console.error('Error updating inquiry status:', error);
      return NextResponse.json({ error: 'Failed to update inquiry status' }, { status: 500 });
    }

    if (!inquiry) {
      return NextResponse.json({ error: 'Inquiry not found' }, { status: 404 });
    }

    // Optional: Send notification to user about status change
    // This could be implemented later with email notifications

    return NextResponse.json({ 
      inquiry,
      message: 'Status updated successfully' 
    });

  } catch (error) {
    console.error('Error in update inquiry status API:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
