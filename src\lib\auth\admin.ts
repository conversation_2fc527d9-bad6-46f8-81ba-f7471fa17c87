import { createClient } from '@supabase/supabase-js';
import { getSupabaseServerClient } from '@/lib/supabase/server';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export interface AdminUser {
  id: string;
  email: string;
  fullName: string;
  isAdmin: boolean;
}

export interface Permission {
  resource: 'products' | 'inquiries' | 'users' | 'roles' | 'chat' | 'inventory';
  actions: ('create' | 'read' | 'update' | 'delete')[];
}

/**
 * Verify admin authentication using unified user auth
 */
export async function verifyAdminAuth(): Promise<AdminUser | null> {
  try {
    const userSupabase = await getSupabaseServerClient();

    // Get authenticated user
    const { data: { user }, error: authError } = await userSupabase.auth.getUser();

    if (authError || !user) {
      return null;
    }

    // Check if user is admin in the users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, full_name, is_admin')
      .eq('id', user.id)
      .eq('is_admin', true)
      .single();

    if (userError || !userData) {
      return null;
    }

    return {
      id: userData.id,
      email: userData.email,
      fullName: userData.full_name,
      isAdmin: userData.is_admin,
    };

  } catch (error) {
    console.error('Admin auth verification error:', error);
    return null;
  }
}

/**
 * Check if admin has permission for a specific resource and action
 * For now, all admin users have full permissions
 */
export function hasPermission(
  adminUser: AdminUser,
  _resource: Permission['resource'],
  _action: Permission['actions'][0]
): boolean {
  // For simplified admin system, all admin users have full permissions
  return adminUser.isAdmin;
}

/**
 * Middleware function to protect admin routes
 */
export async function requireAdminAuth(
  requiredResource?: Permission['resource'],
  requiredAction?: Permission['actions'][0]
): Promise<{ success: true; admin: AdminUser } | { success: false; error: string; status: number }> {
  const admin = await verifyAdminAuth();

  if (!admin) {
    return { success: false, error: 'Admin authentication required', status: 401 };
  }

  if (requiredResource && requiredAction) {
    if (!hasPermission(admin, requiredResource, requiredAction)) {
      return { success: false, error: 'Insufficient permissions', status: 403 };
    }
  }

  return { success: true, admin };
}

/**
 * Simple admin auth check for API routes
 * Returns admin user or throws response
 */
export async function requireAdminAuthSimple(): Promise<AdminUser> {
  const admin = await verifyAdminAuth();

  if (!admin) {
    throw new Response(
      JSON.stringify({ error: 'Unauthorized' }),
      { status: 401, headers: { 'Content-Type': 'application/json' } }
    );
  }

  return admin;
}
