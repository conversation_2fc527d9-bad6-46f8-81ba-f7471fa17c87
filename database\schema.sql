-- Tisha International Database Schema
-- This file contains all the table definitions and initial data setup

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enums
CREATE TYPE inquiry_type AS ENUM ('regular', 'bulk', 'hot');
CREATE TYPE inquiry_status AS ENUM ('pending', 'in_progress', 'accepted', 'rejected', 'completed');
CREATE TYPE transaction_type AS ENUM ('stock_in', 'stock_out', 'adjustment');
CREATE TYPE sender_type AS ENUM ('user', 'admin');
CREATE TYPE message_type AS ENUM ('text', 'image', 'link');
CREATE TYPE notification_template AS ENUM ('inquiry_update', 'chat_message', 'low_stock_alert');
CREATE TYPE notification_status AS ENUM ('pending', 'sent', 'failed');

-- Users Table (for customer users)
CREATE TABLE users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    full_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Roles Table (for admin roles)
CREATE TABLE roles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    permissions JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Admin Users Table
CREATE TABLE admin_users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role_id UUID REFERENCES roles(id) ON DELETE SET NULL,
    full_name VARCHAR(255) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories Table
CREATE TABLE categories (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products Table
CREATE TABLE products (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    brand VARCHAR(100) NOT NULL,
    model_name VARCHAR(100) NOT NULL,
    screen_size VARCHAR(50),
    color VARCHAR(50),
    storage VARCHAR(100),
    ram VARCHAR(100),
    operating_system VARCHAR(100),
    graphics_card VARCHAR(100),
    special_features JSONB DEFAULT '[]',
    additional_features JSONB DEFAULT '{}',
    description TEXT,
    images JSONB DEFAULT '[]',
    price DECIMAL(10,2) NOT NULL,
    show_price BOOLEAN DEFAULT true,
    stock_quantity INTEGER DEFAULT 0,
    low_stock_threshold INTEGER DEFAULT 5,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inventory Table (for tracking stock changes)
CREATE TABLE inventory (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    transaction_type transaction_type NOT NULL,
    quantity_change INTEGER NOT NULL,
    previous_quantity INTEGER NOT NULL,
    new_quantity INTEGER NOT NULL,
    reason VARCHAR(255),
    admin_id UUID REFERENCES admin_users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Inquiries Table
CREATE TABLE inquiries (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    product_id UUID REFERENCES products(id) ON DELETE SET NULL,
    inquiry_type inquiry_type NOT NULL DEFAULT 'regular',
    quantity INTEGER DEFAULT 1,
    status inquiry_status DEFAULT 'pending',
    user_details JSONB NOT NULL, -- Store user contact info at time of inquiry
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat Messages Table
CREATE TABLE chat_messages (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    inquiry_id UUID REFERENCES inquiries(id) ON DELETE CASCADE,
    sender_type sender_type NOT NULL,
    sender_id UUID NOT NULL, -- Can reference either users.id or admin_users.id
    message_type message_type DEFAULT 'text',
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email Notifications Table
CREATE TABLE email_notifications (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    recipient_email VARCHAR(255) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    template_type notification_template NOT NULL,
    related_id UUID, -- inquiry_id or product_id depending on template_type
    status notification_status DEFAULT 'pending',
    sent_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_slug ON products(slug);
CREATE INDEX idx_products_is_active ON products(is_active);
CREATE INDEX idx_inquiries_user_id ON inquiries(user_id);
CREATE INDEX idx_inquiries_product_id ON inquiries(product_id);
CREATE INDEX idx_inquiries_status ON inquiries(status);
CREATE INDEX idx_chat_messages_inquiry_id ON chat_messages(inquiry_id);
CREATE INDEX idx_inventory_product_id ON inventory(product_id);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_inquiries_updated_at BEFORE UPDATE ON inquiries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert initial data

-- Default admin role
INSERT INTO roles (name, permissions) VALUES 
('Super Admin', '{"products": {"read": true, "write": true, "delete": true}, "inquiries": {"read": true, "write": true, "delete": true}, "users": {"read": true, "write": true, "delete": true}, "admin": {"read": true, "write": true, "delete": true}}'),
('Product Manager', '{"products": {"read": true, "write": true, "delete": false}, "inquiries": {"read": true, "write": true, "delete": false}}'),
('Customer Service', '{"inquiries": {"read": true, "write": true, "delete": false}, "users": {"read": true, "write": false, "delete": false}}');

-- Default categories
INSERT INTO categories (name, slug, description, is_active) VALUES 
('Laptops', 'laptops', 'Premium refurbished laptops for business and personal use', true),
('Desktops', 'desktops', 'Powerful desktop computers for office and home', true),
('Gaming', 'gaming', 'High-performance gaming computers and laptops', true),
('Workstations', 'workstations', 'Professional workstations for demanding applications', true);

-- Sample products (will be replaced with real data)
INSERT INTO products (
    category_id, name, slug, brand, model_name, screen_size, color, storage, ram, 
    operating_system, graphics_card, special_features, additional_features, 
    description, images, price, stock_quantity, low_stock_threshold, is_active
) VALUES 
(
    (SELECT id FROM categories WHERE slug = 'desktops'),
    'Dell OptiPlex 7090 Desktop',
    'dell-optiplex-7090-desktop',
    'Dell',
    'OptiPlex 7090',
    NULL,
    'Black',
    '512GB SSD',
    '16GB DDR4',
    'Windows 11 Pro',
    'Intel UHD Graphics',
    '["Business-grade reliability", "Compact form factor", "Energy efficient design", "Multiple connectivity options"]',
    '{"webcam": false, "fingerprintSensor": false, "keyboard": "Standard USB", "pointerDevice": "USB Mouse"}',
    'The Dell OptiPlex 7090 is a powerful business desktop designed for professional environments requiring reliability and performance.',
    '["/api/placeholder-image"]',
    65000,
    5,
    2,
    true
),
(
    (SELECT id FROM categories WHERE slug = 'laptops'),
    'HP EliteBook 840 G8 Laptop',
    'hp-elitebook-840-g8-laptop',
    'HP',
    'EliteBook 840 G8',
    '14 inch',
    'Silver',
    '256GB SSD',
    '8GB DDR4',
    'Windows 11 Pro',
    'Intel Iris Xe',
    '["Enterprise security features", "Durable design with MIL-STD testing", "Fast charging capability", "Professional video conferencing"]',
    '{"webcam": true, "fingerprintSensor": true, "keyboard": "Backlit", "pointerDevice": "Touchpad"}',
    'The HP EliteBook 840 G8 combines enterprise-grade security with powerful performance in a sleek, professional design.',
    '["/api/placeholder-image"]',
    42000,
    12,
    3,
    true
),
(
    (SELECT id FROM categories WHERE slug = 'laptops'),
    'Lenovo ThinkPad T14 Gen 2',
    'lenovo-thinkpad-t14-gen-2',
    'Lenovo',
    'ThinkPad T14',
    '14 inch',
    'Black',
    '512GB SSD',
    '16GB DDR4',
    'Windows 11 Pro',
    'Intel Iris Xe',
    '["Legendary ThinkPad durability", "Military-grade tested", "Rapid Charge technology", "Superior typing experience"]',
    '{"webcam": true, "fingerprintSensor": true, "keyboard": "Backlit with TrackPoint", "pointerDevice": "Touchpad + TrackPoint"}',
    'The Lenovo ThinkPad T14 Gen 2 delivers the legendary ThinkPad experience with modern performance and security features.',
    '["/api/placeholder-image"]',
    48000,
    8,
    2,
    true
);

-- Row Level Security (RLS) Policies

-- Enable RLS on tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE inquiries ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_notifications ENABLE ROW LEVEL SECURITY;

-- Public read access for products and categories
CREATE POLICY "Anyone can view active products" ON products FOR SELECT USING (is_active = true);
CREATE POLICY "Anyone can view active categories" ON categories FOR SELECT USING (is_active = true);

-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

-- Users can access their own inquiries
CREATE POLICY "Users can view own inquiries" ON inquiries FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create inquiries" ON inquiries FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can access chat messages for their inquiries
CREATE POLICY "Users can view own chat messages" ON chat_messages FOR SELECT USING (
    EXISTS (
        SELECT 1 FROM inquiries 
        WHERE inquiries.id = chat_messages.inquiry_id 
        AND inquiries.user_id = auth.uid()
    )
);

CREATE POLICY "Users can send chat messages" ON chat_messages FOR INSERT WITH CHECK (
    sender_type = 'user' 
    AND sender_id = auth.uid()
    AND EXISTS (
        SELECT 1 FROM inquiries 
        WHERE inquiries.id = inquiry_id 
        AND inquiries.user_id = auth.uid()
    )
);

-- Admin policies (will be implemented with custom functions)
-- For now, we'll use service role for admin operations
