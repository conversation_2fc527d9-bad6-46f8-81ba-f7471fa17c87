'use client';

import { useState } from 'react';
import { MessageCircle, Wifi, WifiOff, RefreshCw } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { MessageList } from './MessageList';
import { MessageInput } from './MessageInput';
import { TypingIndicator } from './TypingIndicator';
import { OnlineStatus } from './OnlineStatus';
import { useRealtimeChat } from '@/hooks/useRealtimeChat';
import { useOnlinePresence } from '@/hooks/useOnlinePresence';

interface ChatWindowProps {
  inquiryId: string;
  currentUserId?: string;
  title?: string;
  subtitle?: string;
  isAdmin?: boolean;
  customerName?: string;
}

export function ChatWindow({
  inquiryId,
  currentUserId,
  title = "Chat Support",
  subtitle = "Communicate with our team about your inquiry",
  isAdmin = false,
  customerName
}: ChatWindowProps) {
  const [isSending, setIsSending] = useState(false);
  
  const {
    messages,
    isLoading,
    isConnected,
    error,
    sendMessage,
    refreshMessages,
    otherUserTyping,
    handleTyping
  } = useRealtimeChat({
    inquiryId,
    currentUserId,
    isAdmin
  });



  const {
    isOnline,
    isOtherUserOnline,
    otherUserType
  } = useOnlinePresence({
    inquiryId,
    currentUserId,
    isAdmin
  });

  const handleSendMessage = async (content: string) => {
    setIsSending(true);
    try {
      await sendMessage(content);
    } catch (error) {
      console.error('Failed to send message:', error);
      // You could add a toast notification here
    } finally {
      setIsSending(false);
    }
  };
  return (
    <Card className="p-0 h-[500px] sm:h-[600px] lg:h-[700px] flex flex-col">
      {/* Chat Header */}
      <div className="p-4 sm:p-6 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="p-2 bg-gradient-to-r from-purple-600 to-pink-600 rounded-lg">
              <MessageCircle className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
            </div>
            <div className="min-w-0 flex-1">
              <h3 className="text-base sm:text-lg font-semibold text-white truncate">{title}</h3>
              <p className="text-xs sm:text-sm text-gray-400 truncate">{subtitle}</p>
            </div>
          </div>
          
          {/* Connection Status and Refresh */}
          <div className="flex items-center space-x-1 sm:space-x-2 flex-shrink-0">
            {/* Refresh Button */}
            <button
              onClick={refreshMessages}
              className="p-1 text-gray-400 hover:text-white transition-colors"
              title="Refresh messages"
            >
              <RefreshCw className="h-3 w-3 sm:h-4 sm:w-4" />
            </button>

            {/* Connection Status */}
            {isConnected ? (
              <div className="flex items-center space-x-1 text-green-400">
                <Wifi className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="text-xs hidden sm:inline">Connected</span>
              </div>
            ) : (
              <div className="flex items-center space-x-1 text-yellow-400">
                <WifiOff className="h-3 w-3 sm:h-4 sm:w-4" />
                <span className="text-xs hidden sm:inline">Connecting...</span>
              </div>
            )}
          </div>
        </div>
        
        {/* Error Display */}
        {error && (
          <div className="mt-3 p-3 bg-red-900/50 border border-red-600 rounded-lg">
            <p className="text-sm text-red-300">{error}</p>
          </div>
        )}
      </div>

      {/* Online Status */}
      <OnlineStatus
        isOnline={isOnline}
        isOtherUserOnline={isOtherUserOnline}
        otherUserType={otherUserType}
        isAdmin={isAdmin}
      />

      {/* Chat Content Area */}
      <div className="flex-1 flex flex-col min-h-0">
        {/* Loading State */}
        {isLoading ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
              <p className="text-gray-400">Loading messages...</p>
            </div>
          </div>
        ) : (
          <>
            {/* Messages */}
            <MessageList
              messages={messages}
              currentUserId={currentUserId}
              isAdmin={isAdmin}
              customerName={customerName}
            />

            {/* Typing Indicator */}
            <TypingIndicator
              isTyping={otherUserTyping}
              userName={isAdmin ? customerName : 'Support Team'}
              isAdmin={!isAdmin}
            />
          </>
        )}
      </div>

      {/* Message Input - Always visible */}
      <div className="p-3 sm:p-4 border-t border-gray-700">
        <MessageInput
          onSendMessage={handleSendMessage}
          onTyping={handleTyping}
          disabled={!isConnected || isSending}
        />
      </div>
    </Card>
  );
}
