'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

interface UserDetails {
  fullName: string;
  email: string;
  phone: string;
  address?: string;
}

interface CreateInquiryParams {
  productId: string;
  inquiryType: 'regular' | 'bulk';
  quantity: number;
  userDetails: UserDetails;
}

interface Inquiry {
  id: string;
  product_id: string;
  type: 'regular' | 'bulk';
  quantity: number;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  contact_details: UserDetails;
  created_at: string;
  updated_at: string;
  products?: {
    id: string;
    name: string;
    slug: string;
    brand: string;
    model: string;
    price: number;
    images: string[];
    categories?: {
      slug: string;
    };
  };
}

export function useInquiries() {
  const { user } = useAuth();
  const [inquiries, setInquiries] = useState<Inquiry[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createInquiry = async (params: CreateInquiryParams) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/inquiries', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create inquiry');
      }

      // Refresh inquiries if user is logged in
      if (user) {
        await fetchInquiries();
      }

      return data.inquiry;
    } catch (err) {
      const message = err instanceof Error ? err.message : 'An error occurred';
      setError(message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const fetchInquiries = async (page = 1, limit = 10) => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/inquiries?page=${page}&limit=${limit}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch inquiries');
      }

      setInquiries(data.inquiries);
      return data;
    } catch (err) {
      const message = err instanceof Error ? err.message : 'An error occurred';
      setError(message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user) {
      fetchInquiries();
    } else {
      setInquiries([]);
    }
  }, [user]);

  const getUserInquiries = async () => {
    if (!user) return [];

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/inquiries');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch inquiries');
      }

      return data.inquiries || [];
    } catch (err) {
      const message = err instanceof Error ? err.message : 'An error occurred';
      setError(message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    inquiries,
    loading,
    error,
    createInquiry,
    fetchInquiries,
    getUserInquiries,
    refetch: fetchInquiries,
  };
}
