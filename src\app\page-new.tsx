import { HeroSection } from '@/components/layout/HeroSection'

export default function HomePage() {
  return (
    <div className="min-h-screen">
      <HeroSection />
      
      {/* Placeholder for additional sections */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl font-bold text-white mb-8 font-['Poppins']">
            Featured Products
          </h2>
          <p className="text-gray-300 mb-12">
            Discover our handpicked selection of premium refurbished devices
          </p>
          
          {/* Product grid will be implemented in Phase 2 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="card animate-fade-in">
                <div className="w-full h-48 bg-gradient-to-br from-gray-700 to-gray-800 rounded-lg mb-4 flex items-center justify-center">
                  <span className="text-gray-400">Product Image</span>
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">Product {i}</h3>
                <p className="text-gray-300 text-sm mb-4">
                  High-quality refurbished device with excellent performance
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-2xl font-bold text-gradient">$599</span>
                  <button className="btn-primary text-sm px-4 py-2">
                    View Details
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
